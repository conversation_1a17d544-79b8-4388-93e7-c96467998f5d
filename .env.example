# Server Configuration
PORT=3001
NODE_ENV=development
LOG_LEVEL=info

# Redis Configuration
# Option 1: Individual Redis settings
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# Option 2: Redis URL (Railway format)
# REDIS_URL=redis://username:password@hostname:port/database

# Database Configuration (add your database settings here)
# DATABASE_URL=postgresql://username:password@hostname:port/database
# MONGODB_URI=************************************:port/database

# API Keys and External Services
# Add your API keys here for crawling services, databases, etc.
# EXAMPLE_API_KEY=your_api_key_here

# Bull Queue Configuration
BULL_BOARD_USERNAME=admin
BULL_BOARD_PASSWORD=admin123

# Crawling Configuration
DEFAULT_USER_AGENT=Mozilla/5.0 (compatible; TaskServer/1.0)
DEFAULT_TIMEOUT=30000
MAX_CONCURRENT_JOBS=5

# Security
# Add any security-related environment variables here
