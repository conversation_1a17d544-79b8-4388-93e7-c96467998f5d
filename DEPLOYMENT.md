# Railway 部署指南

## 快速部署到 Railway

### 1. 准备工作

1. 确保你有 [Railway](https://railway.app) 账户
2. 将代码推送到 GitHub 仓库

### 2. 部署步骤

1. **连接 Railway 到你的 GitHub 仓库**

   - 登录 Railway
   - 点击 "New Project"
   - 选择 "Deploy from GitHub repo"
   - 选择你的 task-server 仓库

2. **添加 Redis 服务**

   - 在 Railway 项目中点击 "New"
   - 选择 "Database" → "Add Redis"
   - Railway 会自动创建 Redis 实例并设置 `REDIS_URL` 环境变量

3. **配置环境变量**
   Railway 会自动设置以下变量：

   - `PORT` - Railway 自动设置
   - `REDIS_URL` - Redis 插件自动设置

   你可以在 Variables 标签页添加其他变量：

   ```
   NODE_ENV=production
   LOG_LEVEL=info
   ```

4. **部署**
   - Railway 会自动检测 Node.js 项目
   - 使用 `package.json` 中的 `start` 脚本
   - 部署完成后会提供一个 URL

### 3. 访问应用

部署完成后：

- 主页：`https://your-app.railway.app/`
- Bull Board 管理界面：`https://your-app.railway.app/admin/queues`
- 健康检查：`https://your-app.railway.app/health`

### 4. 测试部署

使用 curl 测试 API：

```bash
# 健康检查
curl https://your-app.railway.app/health

# 添加爬虫任务
curl -X POST https://your-app.railway.app/api/jobs/crawling \
  -H "Content-Type: application/json" \
  -d '{"url": "https://example.com"}'

# 添加数据库任务
curl -X POST https://your-app.railway.app/api/jobs/database \
  -H "Content-Type: application/json" \
  -d '{"operation": "insert", "data": {"table": "test", "record": {"name": "test"}}}'
```

## 本地开发

### 使用 Docker 运行 Redis

```bash
# 启动 Redis
docker run -d -p 6379:6379 --name redis redis:alpine

# 启动开发服务器
npm run dev
```

**注意**: 如果端口 3001 被占用，服务器会自动尝试下一个可用端口（3002, 3003 等）。

### 不使用 Docker

```bash
# macOS
brew install redis
brew services start redis

# Ubuntu
sudo apt install redis-server
sudo systemctl start redis

# 启动开发服务器
npm run dev
```

## 环境变量说明

| 变量名       | 说明           | 默认值      | Railway             |
| ------------ | -------------- | ----------- | ------------------- |
| `PORT`       | 服务器端口     | 3001        | 自动设置            |
| `NODE_ENV`   | 环境           | development | 建议设为 production |
| `REDIS_URL`  | Redis 连接 URL | -           | 自动设置            |
| `REDIS_HOST` | Redis 主机     | localhost   | 被 REDIS_URL 覆盖   |
| `REDIS_PORT` | Redis 端口     | 6379        | 被 REDIS_URL 覆盖   |
| `LOG_LEVEL`  | 日志级别       | info        | 可选                |

## 故障排除

### 常见问题

1. **Redis 连接失败**

   - 确保 Redis 服务已添加到 Railway 项目
   - 检查 `REDIS_URL` 环境变量是否正确设置

2. **应用启动失败**

   - 检查 Railway 日志
   - 确保 `package.json` 中的 `start` 脚本正确

3. **Bull Board 无法访问**
   - 确保访问 `/admin/queues` 路径
   - 检查应用是否正常启动

### 查看日志

在 Railway 控制台中：

1. 选择你的服务
2. 点击 "Deployments" 标签
3. 点击最新的部署查看日志

## 扩展配置

### 自定义域名

1. 在 Railway 项目设置中
2. 点击 "Settings" → "Domains"
3. 添加你的自定义域名

### 环境分离

可以创建多个环境：

- `production` - 生产环境
- `staging` - 测试环境

每个环境可以有不同的环境变量配置。
