# Lark (飞书) 通知功能

Task Server 现在支持向 Lark (飞书) 群组发送通知消息。这个功能特别适合用于：

- 任务完成通知
- 定期状态报告
- 系统监控警报
- 定时提醒

## 🚀 功能特性

- **单条消息**: 立即发送一条消息
- **定期消息**: 每隔指定时间发送消息（如每5秒发送一次）
- **定时消息**: 在指定时间发送消息
- **日文支持**: 完美支持日文内容
- **Emoji 支持**: 支持 emoji 表情

## 📡 Webhook 配置

当前配置的 Lark Webhook URL:
```
https://open.larksuite.com/open-apis/bot/v2/hook/153dda5d-bbb6-4ac7-933c-d9c0e57217b4
```

## 🔧 API 使用方法

### 基础端点
```
POST /api/jobs/notification
Content-Type: application/json
```

### 1. 发送单条消息

```bash
curl -X POST http://localhost:3001/api/jobs/notification \
  -H "Content-Type: application/json" \
  -d '{
    "type": "lark-message",
    "message": "こんにちは！タスクが完了しました。🎉"
  }'
```

**响应示例:**
```json
{
  "success": true,
  "jobId": "12345",
  "type": "lark-message",
  "message": "Notification job added successfully"
}
```

### 2. 发送定期消息（每5秒一次）

```bash
curl -X POST http://localhost:3001/api/jobs/notification \
  -H "Content-Type: application/json" \
  -d '{
    "type": "lark-recurring",
    "message": "定期システムチェック",
    "options": {
      "intervalSeconds": 5,
      "maxCount": 10
    }
  }'
```

**参数说明:**
- `intervalSeconds`: 发送间隔（秒）
- `maxCount`: 最大发送次数

### 3. 发送定时消息

```bash
curl -X POST http://localhost:3001/api/jobs/notification \
  -H "Content-Type: application/json" \
  -d '{
    "type": "lark-scheduled",
    "message": "予定されたメンテナンス開始のお知らせ",
    "options": {
      "scheduleTime": "2025-07-13T20:00:00.000Z"
    }
  }'
```

**参数说明:**
- `scheduleTime`: ISO 8601 格式的时间字符串

## 📝 JavaScript 示例

### 使用 axios 发送通知

```javascript
const axios = require('axios');

// 发送单条消息
async function sendLarkMessage(message) {
  try {
    const response = await axios.post('http://localhost:3001/api/jobs/notification', {
      type: 'lark-message',
      message: message
    });
    console.log('消息发送成功:', response.data.jobId);
  } catch (error) {
    console.error('发送失败:', error.message);
  }
}

// 设置定期消息
async function setupRecurringMessage() {
  try {
    const response = await axios.post('http://localhost:3001/api/jobs/notification', {
      type: 'lark-recurring',
      message: 'サーバー稼働状況: 正常',
      options: {
        intervalSeconds: 5,  // 每5秒
        maxCount: 12        // 发送12次（总计1分钟）
      }
    });
    console.log('定期消息设置成功:', response.data.jobId);
  } catch (error) {
    console.error('设置失败:', error.message);
  }
}

// 设置定时消息
async function scheduleMessage() {
  const scheduleTime = new Date(Date.now() + 30 * 1000); // 30秒后
  
  try {
    const response = await axios.post('http://localhost:3001/api/jobs/notification', {
      type: 'lark-scheduled',
      message: '30秒後の予定メッセージです！',
      options: {
        scheduleTime: scheduleTime.toISOString()
      }
    });
    console.log('定时消息设置成功:', response.data.jobId);
  } catch (error) {
    console.error('设置失败:', error.message);
  }
}
```

## 🎯 实际使用场景

### 1. 任务完成通知
```javascript
// 在任务完成后发送通知
async function notifyTaskCompletion(taskName, result) {
  const message = `タスク「${taskName}」が完了しました。\n結果: ${result} ✅`;
  await sendLarkMessage(message);
}
```

### 2. 系统监控
```javascript
// 每5分钟发送系统状态
async function setupSystemMonitoring() {
  await axios.post('http://localhost:3001/api/jobs/notification', {
    type: 'lark-recurring',
    message: 'システム監視: すべて正常 🟢',
    options: {
      intervalSeconds: 300,  // 5分钟
      maxCount: 288         // 24小时
    }
  });
}
```

### 3. 定时提醒
```javascript
// 设置每日提醒
async function setupDailyReminder() {
  const tomorrow9AM = new Date();
  tomorrow9AM.setDate(tomorrow9AM.getDate() + 1);
  tomorrow9AM.setHours(9, 0, 0, 0);
  
  await axios.post('http://localhost:3001/api/jobs/notification', {
    type: 'lark-scheduled',
    message: '今日のタスクを確認してください 📋',
    options: {
      scheduleTime: tomorrow9AM.toISOString()
    }
  });
}
```

## 🔍 监控和调试

### 查看任务状态
```bash
curl http://localhost:3001/api/jobs/{jobId}/status
```

### Bull Board 管理界面
访问 `http://localhost:3001/admin/queues` 查看所有通知任务的状态。

## 📋 测试脚本

项目包含完整的测试脚本：

```bash
# 运行 Lark 通知测试
npm run test:lark

# 或者直接运行
node examples/test-lark-notifications.js
```

## ⚠️ 注意事项

1. **Webhook URL**: 确保 Lark webhook URL 是有效的
2. **Redis 连接**: 通知功能需要 Redis 运行
3. **网络连接**: 确保服务器能访问 Lark API
4. **消息频率**: 避免发送过于频繁的消息，以免被限流
5. **no-cors 模式**: 由于使用 no-cors 模式，无法读取响应状态，假设发送成功

## 🔧 自定义配置

如需修改 Webhook URL，请编辑 `src/processors/notificationProcessor.js` 文件中的 `LARK_WEBHOOK_URL` 常量。

## 🚀 部署到 Railway

在 Railway 部署时，通知功能会自动工作，无需额外配置。确保：

1. Redis 服务已添加
2. 环境变量正确设置
3. 网络连接正常
