# Task Server

A robust task server built with Bull queues and Bull Board for Railway deployment. Perfect for handling crawling tasks, database operations, and other background jobs migrated from Next.js/Vercel applications.

## Features

- 🚀 **Bull Queue System**: Reliable job processing with Redis
- 📊 **Bull Board**: Web-based queue management interface
- 🕷️ **Crawling Tasks**: Built-in web scraping capabilities
- 💾 **Database Operations**: Flexible database task processing
- 🔄 **Auto-retry**: Configurable retry logic with exponential backoff
- 📝 **Comprehensive Logging**: Winston-based logging with emojis
- 🚢 **Railway Ready**: Optimized for Railway deployment
- 🔍 **Health Checks**: Built-in health monitoring endpoints
- 🔄 **Smart Port Selection**: Automatically finds available ports if default is in use

## Quick Start

### Local Development

1. **Clone and install dependencies:**

```bash
git clone <your-repo>
cd task-server
npm install
```

2. **Set up environment variables:**

```bash
cp .env.example .env
# Edit .env with your Redis and other configurations
```

3. **Start Redis (if running locally):**

```bash
# Using Docker
docker run -d -p 6379:6379 redis:alpine

# Or install Redis locally
# macOS: brew install redis && brew services start redis
# Ubuntu: sudo apt install redis-server
```

4. **Run the server:**

```bash
# Development mode with auto-reload
npm run dev

# Production mode
npm start
```

5. **Access Bull Board:**
   Open http://localhost:3001/admin/queues to manage your queues

### Railway Deployment

1. **Connect your repository to Railway**
2. **Add Redis service** in Railway dashboard
3. **Set environment variables** (Railway will auto-set REDIS_URL)
4. **Deploy** - Railway will automatically detect and deploy your app

## API Endpoints

### Health Check

```
GET /health
```

### Add Crawling Job

```
POST /api/jobs/crawling
Content-Type: application/json

{
  "url": "https://example.com",
  "options": {
    "timeout": 30000,
    "selectors": {
      "titles": "h2",
      "prices": ".price"
    }
  }
}
```

### Add Database Job

```
POST /api/jobs/database
Content-Type: application/json

{
  "operation": "insert",
  "data": {
    "table": "users",
    "record": {
      "name": "John Doe",
      "email": "<EMAIL>"
    }
  }
}
```

### Get Job Status

```
GET /api/jobs/{jobId}/status
```

## Job Types

### Crawling Jobs

#### Basic Page Crawling

```javascript
{
  "url": "https://example.com",
  "options": {
    "timeout": 30000,
    "userAgent": "Custom User Agent",
    "selectors": {
      "customData": ".my-selector"
    }
  }
}
```

#### Sitemap Crawling

```javascript
// Add this job type to your crawling queue
{
  "type": "crawl-sitemap",
  "sitemapUrl": "https://example.com/sitemap.xml"
}
```

### Database Jobs

Supported operations:

- `insert`: Insert new records
- `update`: Update existing records
- `delete`: Delete records
- `select`: Query records
- `bulk-insert`: Insert multiple records
- `migrate`: Run database migrations

## Project Structure

```
task-server/
├── src/
│   ├── index.js              # Main application entry
│   ├── config/
│   │   └── redis.js          # Redis configuration
│   ├── queues/
│   │   ├── crawling.js       # Crawling queue definition
│   │   └── database.js       # Database queue definition
│   ├── processors/
│   │   ├── crawlingProcessor.js  # Crawling job handlers
│   │   └── databaseProcessor.js  # Database job handlers
│   └── utils/
│       └── logger.js         # Winston logger setup
├── logs/                     # Log files directory
├── package.json
├── railway.toml             # Railway deployment config
└── .env.example            # Environment variables template
```

## Environment Variables

| Variable         | Description          | Default       |
| ---------------- | -------------------- | ------------- |
| `PORT`           | Server port          | `3001`        |
| `NODE_ENV`       | Environment          | `development` |
| `REDIS_URL`      | Redis connection URL | -             |
| `REDIS_HOST`     | Redis host           | `localhost`   |
| `REDIS_PORT`     | Redis port           | `6379`        |
| `REDIS_PASSWORD` | Redis password       | -             |
| `LOG_LEVEL`      | Logging level        | `info`        |

## Extending the Server

### Adding New Job Types

1. **Create a new queue** (if needed):

```javascript
// src/queues/myNewQueue.js
const Queue = require("bull");
const redisConfig = require("../config/redis");

const myNewQueue = new Queue("my-new-queue", { redis: redisConfig });
module.exports = myNewQueue;
```

2. **Create a processor**:

```javascript
// src/processors/myNewProcessor.js
const myNewQueue = require("../queues/myNewQueue");

myNewQueue.process("my-job-type", async (job) => {
  // Your job processing logic here
  return { success: true };
});
```

3. **Register in main app**:

```javascript
// src/index.js
const myNewQueue = require("./queues/myNewQueue");
require("./processors/myNewProcessor");

// Add to Bull Board
const { router: bullBoardRouter } = createBullBoard([
  new BullAdapter(crawlingQueue),
  new BullAdapter(databaseQueue),
  new BullAdapter(myNewQueue), // Add your new queue
]);
```

### Database Integration

Replace the mock database operations in `src/processors/databaseProcessor.js` with your actual database client:

```javascript
// Example with PostgreSQL
const { Pool } = require("pg");
const pool = new Pool({ connectionString: process.env.DATABASE_URL });

async function handleInsert(data, options, job) {
  const { table, record } = data;
  const keys = Object.keys(record);
  const values = Object.values(record);
  const placeholders = keys.map((_, i) => `$${i + 1}`).join(", ");

  const query = `INSERT INTO ${table} (${keys.join(
    ", "
  )}) VALUES (${placeholders}) RETURNING *`;
  const result = await pool.query(query, values);

  return result.rows[0];
}
```

## Monitoring and Debugging

- **Bull Board**: Access at `/admin/queues` for visual queue management
- **Health Check**: Monitor at `/health` endpoint
- **Logs**: Check Railway logs or local `logs/` directory
- **Job Status**: Query individual job status via API

## Migration from Next.js/Vercel

When migrating tasks from your Next.js application:

1. **API Routes** → **Queue Jobs**: Convert API routes to queue jobs
2. **Cron Jobs** → **Scheduled Jobs**: Use Bull's repeat functionality
3. **Database Operations** → **Database Queue**: Move DB operations to database queue
4. **External API Calls** → **Crawling Queue**: Use crawling queue for external requests

Example migration:

```javascript
// Before (Next.js API route)
export default async function handler(req, res) {
  const data = await scrapeWebsite(req.body.url);
  await saveToDatabase(data);
  res.json({ success: true });
}

// After (Task Server)
// Add job to crawling queue, then chain to database queue
const crawlJob = await crawlingQueue.add("crawl-page", { url });
const dbJob = await databaseQueue.add("insert", {
  table: "scraped_data",
  record: crawlJob.result,
});
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

MIT License - see LICENSE file for details
