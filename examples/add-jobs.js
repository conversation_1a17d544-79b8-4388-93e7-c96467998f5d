// 示例脚本：添加各种类型的任务到队列
require("dotenv").config();
const axios = require("axios");

const BASE_URL = process.env.BASE_URL || "http://localhost:3001";

async function addCrawlingJobs() {
  console.log("🔥 添加爬虫任务...");

  const crawlingJobs = [
    {
      url: "https://example.com",
      options: {
        timeout: 30000,
        selectors: {
          title: "h1",
          description: 'meta[name="description"]',
        },
      },
    },
    {
      url: "https://httpbin.org/json",
      options: {
        timeout: 15000,
      },
    },
    {
      url: "https://jsonplaceholder.typicode.com/posts/1",
      options: {
        timeout: 10000,
      },
    },
  ];

  for (const job of crawlingJobs) {
    try {
      const response = await axios.post(`${BASE_URL}/api/jobs/crawling`, job);
      console.log(`✅ 爬虫任务已添加: ${response.data.jobId} - ${job.url}`);
    } catch (error) {
      console.error(`❌ 添加爬虫任务失败: ${job.url} - ${error.message}`);
    }
  }
}

async function addDatabaseJobs() {
  console.log("🔥 添加数据库任务...");

  const databaseJobs = [
    {
      operation: "insert",
      data: {
        table: "users",
        record: {
          name: "John Doe",
          email: "<EMAIL>",
          created_at: new Date().toISOString(),
        },
      },
    },
    {
      operation: "update",
      data: {
        table: "users",
        set: { last_login: new Date().toISOString() },
        where: { id: 1 },
      },
    },
    {
      operation: "bulk-insert",
      data: {
        table: "products",
        records: [
          { name: "Product 1", price: 100 },
          { name: "Product 2", price: 200 },
          { name: "Product 3", price: 300 },
        ],
      },
    },
    {
      operation: "migrate",
      data: {
        migrationName: "add_user_preferences",
        steps: [
          { description: "Create user_preferences table" },
          { description: "Add foreign key constraint" },
          { description: "Create indexes" },
        ],
      },
    },
  ];

  for (const job of databaseJobs) {
    try {
      const response = await axios.post(`${BASE_URL}/api/jobs/database`, job);
      console.log(
        `✅ 数据库任务已添加: ${response.data.jobId} - ${job.operation}`
      );
    } catch (error) {
      console.error(
        `❌ 添加数据库任务失败: ${job.operation} - ${error.message}`
      );
    }
  }
}

async function checkJobStatus(jobId) {
  try {
    const response = await axios.get(`${BASE_URL}/api/jobs/${jobId}/status`);
    console.log(`📊 任务状态 ${jobId}:`, response.data);
  } catch (error) {
    console.error(`❌ 获取任务状态失败: ${jobId} - ${error.message}`);
  }
}

async function main() {
  console.log(`🚀 开始添加示例任务到 ${BASE_URL}`);
  console.log("📝 确保服务器正在运行: npm start");
  console.log("");

  try {
    // 检查服务器是否运行
    await axios.get(`${BASE_URL}/health`);
    console.log("✅ 服务器运行正常");
    console.log("");

    // 添加任务
    await addCrawlingJobs();
    console.log("");
    await addDatabaseJobs();
    console.log("");

    console.log("🎉 所有示例任务已添加完成！");
    console.log(`📊 查看任务状态: ${BASE_URL}/admin/queues`);
  } catch (error) {
    console.error("❌ 服务器连接失败:", error.message);
    console.log("请确保服务器正在运行: npm start");
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  addCrawlingJobs,
  addDatabaseJobs,
  checkJobStatus,
};
