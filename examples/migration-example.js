// 从 Next.js/Vercel 迁移任务的示例
require('dotenv').config();
const axios = require('axios');

const BASE_URL = process.env.BASE_URL || 'http://localhost:3000';

// 模拟原来在 Next.js API 路由中的任务
class TaskMigrationExample {
  constructor() {
    this.baseUrl = BASE_URL;
  }

  // 原来的 Next.js API 路由: /api/scrape-website
  async migrateWebsiteScraping() {
    console.log('🔥 迁移网站爬虫任务...');
    
    // 原来可能是这样的 API 调用
    const websites = [
      'https://news.ycombinator.com',
      'https://github.com/trending',
      'https://stackoverflow.com/questions'
    ];

    for (const url of websites) {
      const job = {
        url,
        options: {
          timeout: 30000,
          selectors: {
            titles: 'h1, h2, h3',
            links: 'a[href]'
          }
        }
      };

      try {
        const response = await axios.post(`${this.baseUrl}/api/jobs/crawling`, job);
        console.log(`✅ 网站爬虫任务已迁移: ${response.data.jobId} - ${url}`);
      } catch (error) {
        console.error(`❌ 迁移失败: ${url} - ${error.message}`);
      }
    }
  }

  // 原来的 Next.js API 路由: /api/update-database
  async migrateDatabaseUpdates() {
    console.log('🔥 迁移数据库更新任务...');
    
    // 模拟批量数据更新
    const updates = [
      {
        operation: 'update',
        data: {
          table: 'users',
          set: { last_active: new Date().toISOString() },
          where: { status: 'active' }
        }
      },
      {
        operation: 'insert',
        data: {
          table: 'analytics',
          record: {
            event: 'migration_completed',
            timestamp: new Date().toISOString(),
            metadata: { source: 'nextjs_migration' }
          }
        }
      }
    ];

    for (const update of updates) {
      try {
        const response = await axios.post(`${this.baseUrl}/api/jobs/database`, update);
        console.log(`✅ 数据库任务已迁移: ${response.data.jobId} - ${update.operation}`);
      } catch (error) {
        console.error(`❌ 迁移失败: ${update.operation} - ${error.message}`);
      }
    }
  }

  // 原来的定时任务 (cron jobs)
  async migrateScheduledTasks() {
    console.log('🔥 迁移定时任务...');
    
    // 注意：这里只是添加任务，实际的定时调度需要在生产环境中设置
    // 可以使用 Bull 的 repeat 功能或外部 cron 服务
    
    const scheduledTasks = [
      {
        name: 'daily-report',
        type: 'database',
        data: {
          operation: 'select',
          data: {
            table: 'daily_stats',
            where: { date: new Date().toISOString().split('T')[0] }
          }
        }
      },
      {
        name: 'weekly-crawl',
        type: 'crawling',
        data: {
          url: 'https://example.com/weekly-data',
          options: { timeout: 60000 }
        }
      }
    ];

    for (const task of scheduledTasks) {
      try {
        const endpoint = task.type === 'crawling' ? '/api/jobs/crawling' : '/api/jobs/database';
        const response = await axios.post(`${this.baseUrl}${endpoint}`, task.data);
        console.log(`✅ 定时任务已迁移: ${response.data.jobId} - ${task.name}`);
      } catch (error) {
        console.error(`❌ 迁移失败: ${task.name} - ${error.message}`);
      }
    }
  }

  // 批量迁移所有任务
  async migrateAll() {
    console.log('🚀 开始批量迁移 Next.js 任务到 Railway Task Server');
    console.log('');

    try {
      // 检查服务器状态
      await axios.get(`${this.baseUrl}/health`);
      console.log('✅ Task Server 运行正常');
      console.log('');

      // 执行迁移
      await this.migrateWebsiteScraping();
      console.log('');
      
      await this.migrateDatabaseUpdates();
      console.log('');
      
      await this.migrateScheduledTasks();
      console.log('');

      console.log('🎉 所有任务迁移完成！');
      console.log(`📊 查看任务状态: ${this.baseUrl}/admin/queues`);
      console.log('');
      console.log('📝 下一步:');
      console.log('1. 更新你的 Next.js 应用，将原来的 API 调用改为调用 Task Server');
      console.log('2. 设置定时任务调度 (使用 cron 或 Railway 的定时功能)');
      console.log('3. 更新数据库连接配置');
      
    } catch (error) {
      console.error('❌ 迁移失败:', error.message);
      console.log('请确保 Task Server 正在运行: npm start');
    }
  }
}

// 使用示例
async function main() {
  const migrator = new TaskMigrationExample();
  await migrator.migrateAll();
}

// 如果直接运行此脚本
if (require.main === module) {
  main().catch(console.error);
}

module.exports = TaskMigrationExample;
