// 测试 Lark 通知功能的示例脚本
require('dotenv').config();
const axios = require('axios');

const BASE_URL = process.env.BASE_URL || 'http://localhost:3001';

async function testSingleMessage() {
  console.log('🔥 测试单条消息发送...');
  
  try {
    const response = await axios.post(`${BASE_URL}/api/jobs/notification`, {
      type: 'lark-message',
      message: 'こんにちは！これはテストメッセージです。🚀'
    });
    
    console.log(`✅ 单条消息任务已添加: ${response.data.jobId}`);
    return response.data.jobId;
  } catch (error) {
    console.error(`❌ 单条消息发送失败: ${error.message}`);
    return null;
  }
}

async function testRecurringMessages() {
  console.log('🔥 测试每5秒发送消息（共10次）...');
  
  try {
    const response = await axios.post(`${BASE_URL}/api/jobs/notification`, {
      type: 'lark-recurring',
      message: 'タスクサーバーからの定期メッセージ',
      options: {
        intervalSeconds: 5,
        maxCount: 10
      }
    });
    
    console.log(`✅ 定期消息任务已添加: ${response.data.jobId}`);
    console.log('📝 这将每5秒发送一条消息，共发送10次');
    return response.data.jobId;
  } catch (error) {
    console.error(`❌ 定期消息设置失败: ${error.message}`);
    return null;
  }
}

async function testScheduledMessage() {
  console.log('🔥 测试定时消息（30秒后发送）...');
  
  // 设置30秒后的时间
  const scheduleTime = new Date(Date.now() + 30 * 1000).toISOString();
  
  try {
    const response = await axios.post(`${BASE_URL}/api/jobs/notification`, {
      type: 'lark-scheduled',
      message: 'これは30秒後に送信される予定メッセージです！⏰',
      options: {
        scheduleTime: scheduleTime
      }
    });
    
    console.log(`✅ 定时消息任务已添加: ${response.data.jobId}`);
    console.log(`📅 将在 ${new Date(scheduleTime).toLocaleString('ja-JP')} 发送`);
    return response.data.jobId;
  } catch (error) {
    console.error(`❌ 定时消息设置失败: ${error.message}`);
    return null;
  }
}

async function checkJobStatus(jobId) {
  if (!jobId) return;
  
  try {
    const response = await axios.get(`${BASE_URL}/api/jobs/${jobId}/status`);
    console.log(`📊 任务状态 ${jobId}:`, {
      state: response.data.state,
      progress: response.data.progress,
      processedOn: response.data.processedOn,
      finishedOn: response.data.finishedOn
    });
  } catch (error) {
    console.error(`❌ 获取任务状态失败: ${jobId} - ${error.message}`);
  }
}

async function main() {
  console.log(`🚀 开始测试 Lark 通知功能 - ${BASE_URL}`);
  console.log('📝 确保服务器正在运行: npm start');
  console.log('');

  try {
    // 检查服务器是否运行
    await axios.get(`${BASE_URL}/health`);
    console.log('✅ 服务器运行正常');
    console.log('');

    // 测试单条消息
    const singleJobId = await testSingleMessage();
    console.log('');

    // 等待一下再检查状态
    if (singleJobId) {
      setTimeout(() => checkJobStatus(singleJobId), 2000);
    }

    // 测试定期消息
    const recurringJobId = await testRecurringMessages();
    console.log('');

    // 测试定时消息
    const scheduledJobId = await testScheduledMessage();
    console.log('');

    console.log('🎉 所有通知任务已添加完成！');
    console.log(`📊 查看任务状态: ${BASE_URL}/admin/queues`);
    console.log('');
    console.log('📱 检查你的 Lark 群组以查看消息');
    console.log('');
    console.log('⏱️  任务时间线:');
    console.log('   - 立即: 单条测试消息');
    console.log('   - 每5秒: 定期消息 (共10次，总计50秒)');
    console.log('   - 30秒后: 定时消息');
    
    // 定期检查任务状态
    if (recurringJobId) {
      console.log('');
      console.log('🔍 将每10秒检查一次定期任务状态...');
      
      const statusInterval = setInterval(async () => {
        await checkJobStatus(recurringJobId);
      }, 10000);
      
      // 60秒后停止检查
      setTimeout(() => {
        clearInterval(statusInterval);
        console.log('🏁 状态检查已停止');
      }, 60000);
    }
    
  } catch (error) {
    console.error('❌ 服务器连接失败:', error.message);
    console.log('请确保服务器正在运行: npm start');
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  testSingleMessage,
  testRecurringMessages,
  testScheduledMessage,
  checkJobStatus
};
