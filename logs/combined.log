{"level":"info","message":"🔥 Logger test successful","service":"task-server","timestamp":"2025-07-13T10:47:41.321Z"}
{"level":"info","message":"🔥 Redis config: localhost:6379","service":"task-server","timestamp":"2025-07-13T10:47:41.437Z"}
{"level":"info","message":"🔥 Redis config: localhost:6379","service":"task-server","timestamp":"2025-07-13T11:15:55.806Z"}
{"level":"info","message":"🔥 Crawling processor initialized","service":"task-server","timestamp":"2025-07-13T11:15:55.892Z"}
{"level":"info","message":"🔥 Database processor initialized","service":"task-server","timestamp":"2025-07-13T11:15:55.892Z"}
{"level":"info","message":"🔥 Task server is running on port 3001","service":"task-server","timestamp":"2025-07-13T11:15:55.897Z"}
{"level":"info","message":"🔥 Bull Board available at http://localhost:3001/admin/queues","service":"task-server","timestamp":"2025-07-13T11:15:55.897Z"}
{"level":"info","message":"🔥 Health check: http://localhost:3001/health","service":"task-server","timestamp":"2025-07-13T11:15:55.897Z"}
{"level":"info","message":"🔥 Redis config: localhost:6379","service":"task-server","timestamp":"2025-07-13T11:16:24.763Z"}
{"level":"info","message":"🔥 Crawling processor initialized","service":"task-server","timestamp":"2025-07-13T11:16:24.837Z"}
{"level":"info","message":"🔥 Database processor initialized","service":"task-server","timestamp":"2025-07-13T11:16:24.838Z"}
{"level":"warn","message":"🔥 Port 3001 is in use, trying port 30011","service":"task-server","timestamp":"2025-07-13T11:16:24.842Z"}
{"level":"info","message":"🔥 Task server is running on port 30011","service":"task-server","timestamp":"2025-07-13T11:16:24.842Z"}
{"level":"info","message":"🔥 Bull Board available at http://localhost:30011/admin/queues","service":"task-server","timestamp":"2025-07-13T11:16:24.842Z"}
{"level":"info","message":"🔥 Health check: http://localhost:30011/health","service":"task-server","timestamp":"2025-07-13T11:16:24.843Z"}
{"level":"info","message":"🔥 Redis config: localhost:6379","service":"task-server","timestamp":"2025-07-13T11:17:11.004Z"}
{"level":"info","message":"🔥 Crawling processor initialized","service":"task-server","timestamp":"2025-07-13T11:17:11.082Z"}
{"level":"info","message":"🔥 Database processor initialized","service":"task-server","timestamp":"2025-07-13T11:17:11.082Z"}
{"level":"info","message":"🔥 Task server is running on port 3001","service":"task-server","timestamp":"2025-07-13T11:17:11.087Z"}
{"level":"info","message":"🔥 Bull Board available at http://localhost:3001/admin/queues","service":"task-server","timestamp":"2025-07-13T11:17:11.087Z"}
{"level":"info","message":"🔥 Health check: http://localhost:3001/health","service":"task-server","timestamp":"2025-07-13T11:17:11.087Z"}
{"level":"info","message":"🔥 Redis config: localhost:6379","service":"task-server","timestamp":"2025-07-13T11:17:27.943Z"}
{"level":"info","message":"🔥 Crawling processor initialized","service":"task-server","timestamp":"2025-07-13T11:17:28.025Z"}
{"level":"info","message":"🔥 Database processor initialized","service":"task-server","timestamp":"2025-07-13T11:17:28.026Z"}
{"level":"warn","message":"🔥 Port 3001 is in use, trying port 3002","service":"task-server","timestamp":"2025-07-13T11:17:28.030Z"}
{"level":"info","message":"🔥 Task server is running on port 3002","service":"task-server","timestamp":"2025-07-13T11:17:28.031Z"}
{"level":"info","message":"🔥 Bull Board available at http://localhost:3002/admin/queues","service":"task-server","timestamp":"2025-07-13T11:17:28.031Z"}
{"level":"info","message":"🔥 Health check: http://localhost:3002/health","service":"task-server","timestamp":"2025-07-13T11:17:28.031Z"}
{"level":"info","message":"🔥 Test server running on port 3001","service":"task-server","timestamp":"2025-07-13T11:19:36.191Z"}
{"level":"info","message":"🔥 Test endpoint: http://localhost:3001/test","service":"task-server","timestamp":"2025-07-13T11:19:36.192Z"}
{"level":"info","message":"🔥 Test server on port 3001 closed","service":"task-server","timestamp":"2025-07-13T11:19:38.194Z"}
