{"level":"info","message":"🔥 Logger test successful","service":"task-server","timestamp":"2025-07-13T10:47:41.321Z"}
{"level":"info","message":"🔥 Redis config: localhost:6379","service":"task-server","timestamp":"2025-07-13T10:47:41.437Z"}
{"level":"info","message":"🔥 Redis config: localhost:6379","service":"task-server","timestamp":"2025-07-13T11:15:55.806Z"}
{"level":"info","message":"🔥 Crawling processor initialized","service":"task-server","timestamp":"2025-07-13T11:15:55.892Z"}
{"level":"info","message":"🔥 Database processor initialized","service":"task-server","timestamp":"2025-07-13T11:15:55.892Z"}
{"level":"info","message":"🔥 Task server is running on port 3001","service":"task-server","timestamp":"2025-07-13T11:15:55.897Z"}
{"level":"info","message":"🔥 Bull Board available at http://localhost:3001/admin/queues","service":"task-server","timestamp":"2025-07-13T11:15:55.897Z"}
{"level":"info","message":"🔥 Health check: http://localhost:3001/health","service":"task-server","timestamp":"2025-07-13T11:15:55.897Z"}
{"level":"info","message":"🔥 Redis config: localhost:6379","service":"task-server","timestamp":"2025-07-13T11:16:24.763Z"}
{"level":"info","message":"🔥 Crawling processor initialized","service":"task-server","timestamp":"2025-07-13T11:16:24.837Z"}
{"level":"info","message":"🔥 Database processor initialized","service":"task-server","timestamp":"2025-07-13T11:16:24.838Z"}
{"level":"warn","message":"🔥 Port 3001 is in use, trying port 30011","service":"task-server","timestamp":"2025-07-13T11:16:24.842Z"}
{"level":"info","message":"🔥 Task server is running on port 30011","service":"task-server","timestamp":"2025-07-13T11:16:24.842Z"}
{"level":"info","message":"🔥 Bull Board available at http://localhost:30011/admin/queues","service":"task-server","timestamp":"2025-07-13T11:16:24.842Z"}
{"level":"info","message":"🔥 Health check: http://localhost:30011/health","service":"task-server","timestamp":"2025-07-13T11:16:24.843Z"}
{"level":"info","message":"🔥 Redis config: localhost:6379","service":"task-server","timestamp":"2025-07-13T11:17:11.004Z"}
{"level":"info","message":"🔥 Crawling processor initialized","service":"task-server","timestamp":"2025-07-13T11:17:11.082Z"}
{"level":"info","message":"🔥 Database processor initialized","service":"task-server","timestamp":"2025-07-13T11:17:11.082Z"}
{"level":"info","message":"🔥 Task server is running on port 3001","service":"task-server","timestamp":"2025-07-13T11:17:11.087Z"}
{"level":"info","message":"🔥 Bull Board available at http://localhost:3001/admin/queues","service":"task-server","timestamp":"2025-07-13T11:17:11.087Z"}
{"level":"info","message":"🔥 Health check: http://localhost:3001/health","service":"task-server","timestamp":"2025-07-13T11:17:11.087Z"}
{"level":"info","message":"🔥 Redis config: localhost:6379","service":"task-server","timestamp":"2025-07-13T11:17:27.943Z"}
{"level":"info","message":"🔥 Crawling processor initialized","service":"task-server","timestamp":"2025-07-13T11:17:28.025Z"}
{"level":"info","message":"🔥 Database processor initialized","service":"task-server","timestamp":"2025-07-13T11:17:28.026Z"}
{"level":"warn","message":"🔥 Port 3001 is in use, trying port 3002","service":"task-server","timestamp":"2025-07-13T11:17:28.030Z"}
{"level":"info","message":"🔥 Task server is running on port 3002","service":"task-server","timestamp":"2025-07-13T11:17:28.031Z"}
{"level":"info","message":"🔥 Bull Board available at http://localhost:3002/admin/queues","service":"task-server","timestamp":"2025-07-13T11:17:28.031Z"}
{"level":"info","message":"🔥 Health check: http://localhost:3002/health","service":"task-server","timestamp":"2025-07-13T11:17:28.031Z"}
{"level":"info","message":"🔥 Test server running on port 3001","service":"task-server","timestamp":"2025-07-13T11:19:36.191Z"}
{"level":"info","message":"🔥 Test endpoint: http://localhost:3001/test","service":"task-server","timestamp":"2025-07-13T11:19:36.192Z"}
{"level":"info","message":"🔥 Test server on port 3001 closed","service":"task-server","timestamp":"2025-07-13T11:19:38.194Z"}
{"level":"info","message":"🔥 Redis config: localhost:6379","service":"task-server","timestamp":"2025-07-13T11:20:18.635Z"}
{"level":"info","message":"🔥 Crawling processor initialized","service":"task-server","timestamp":"2025-07-13T11:20:18.713Z"}
{"level":"info","message":"🔥 Database processor initialized","service":"task-server","timestamp":"2025-07-13T11:20:18.714Z"}
{"level":"info","message":"🔥 Task server is running on port 3001","service":"task-server","timestamp":"2025-07-13T11:20:18.717Z"}
{"level":"info","message":"🔥 Bull Board available at http://localhost:3001/admin/queues","service":"task-server","timestamp":"2025-07-13T11:20:18.717Z"}
{"level":"info","message":"🔥 Health check: http://localhost:3001/health","service":"task-server","timestamp":"2025-07-13T11:20:18.717Z"}
{"level":"info","message":"🔥 Redis config: localhost:6379","service":"task-server","timestamp":"2025-07-13T11:28:50.924Z"}
{"level":"info","message":"🔥 Crawling processor initialized","service":"task-server","timestamp":"2025-07-13T11:28:51.007Z"}
{"level":"info","message":"🔥 Database processor initialized","service":"task-server","timestamp":"2025-07-13T11:28:51.007Z"}
{"level":"info","message":"🔥 Task server is running on port 3001","service":"task-server","timestamp":"2025-07-13T11:28:51.011Z"}
{"level":"info","message":"🔥 Bull Board available at http://localhost:3001/admin/queues","service":"task-server","timestamp":"2025-07-13T11:28:51.011Z"}
{"level":"info","message":"🔥 Health check: http://localhost:3001/health","service":"task-server","timestamp":"2025-07-13T11:28:51.011Z"}
{"level":"info","message":"🔥 Redis config: localhost:6379","service":"task-server","timestamp":"2025-07-13T11:29:21.745Z"}
{"level":"info","message":"🔥 Crawling processor initialized","service":"task-server","timestamp":"2025-07-13T11:29:21.845Z"}
{"level":"info","message":"🔥 Database processor initialized","service":"task-server","timestamp":"2025-07-13T11:29:21.845Z"}
{"level":"info","message":"🔥 Task server is running on port 3001","service":"task-server","timestamp":"2025-07-13T11:29:21.849Z"}
{"level":"info","message":"🔥 Bull Board available at http://localhost:3001/admin/queues","service":"task-server","timestamp":"2025-07-13T11:29:21.850Z"}
{"level":"info","message":"🔥 Health check: http://localhost:3001/health","service":"task-server","timestamp":"2025-07-13T11:29:21.850Z"}
{"level":"info","message":"🔥 Redis config: localhost:6379","service":"task-server","timestamp":"2025-07-13T11:29:30.550Z"}
{"level":"info","message":"🔥 Crawling processor initialized","service":"task-server","timestamp":"2025-07-13T11:29:30.605Z"}
{"level":"info","message":"🔥 Database processor initialized","service":"task-server","timestamp":"2025-07-13T11:29:30.605Z"}
{"level":"info","message":"🔥 Notification processor initialized","service":"task-server","timestamp":"2025-07-13T11:29:30.605Z"}
{"level":"info","message":"🔥 Task server is running on port 3001","service":"task-server","timestamp":"2025-07-13T11:29:30.609Z"}
{"level":"info","message":"🔥 Bull Board available at http://localhost:3001/admin/queues","service":"task-server","timestamp":"2025-07-13T11:29:30.609Z"}
{"level":"info","message":"🔥 Health check: http://localhost:3001/health","service":"task-server","timestamp":"2025-07-13T11:29:30.609Z"}
{"level":"info","message":"🔥 Redis config: localhost:6379","service":"task-server","timestamp":"2025-07-13T11:29:41.159Z"}
{"level":"info","message":"🔥 Crawling processor initialized","service":"task-server","timestamp":"2025-07-13T11:29:41.213Z"}
{"level":"info","message":"🔥 Database processor initialized","service":"task-server","timestamp":"2025-07-13T11:29:41.214Z"}
{"level":"info","message":"🔥 Notification processor initialized","service":"task-server","timestamp":"2025-07-13T11:29:41.214Z"}
{"level":"info","message":"🔥 Task server is running on port 3001","service":"task-server","timestamp":"2025-07-13T11:29:41.218Z"}
{"level":"info","message":"🔥 Bull Board available at http://localhost:3001/admin/queues","service":"task-server","timestamp":"2025-07-13T11:29:41.218Z"}
{"level":"info","message":"🔥 Health check: http://localhost:3001/health","service":"task-server","timestamp":"2025-07-13T11:29:41.218Z"}
{"level":"info","message":"🔥 Redis config: localhost:6379","service":"task-server","timestamp":"2025-07-13T11:30:10.225Z"}
{"level":"info","message":"🔥 Crawling processor initialized","service":"task-server","timestamp":"2025-07-13T11:30:10.302Z"}
{"level":"info","message":"🔥 Database processor initialized","service":"task-server","timestamp":"2025-07-13T11:30:10.302Z"}
{"level":"info","message":"🔥 Notification processor initialized","service":"task-server","timestamp":"2025-07-13T11:30:10.302Z"}
{"level":"info","message":"🔥 Task server is running on port 3001","service":"task-server","timestamp":"2025-07-13T11:30:10.306Z"}
{"level":"info","message":"🔥 Bull Board available at http://localhost:3001/admin/queues","service":"task-server","timestamp":"2025-07-13T11:30:10.307Z"}
{"level":"info","message":"🔥 Health check: http://localhost:3001/health","service":"task-server","timestamp":"2025-07-13T11:30:10.307Z"}
{"level":"info","message":"🔥 Redis config: localhost:6379","service":"task-server","timestamp":"2025-07-13T11:30:48.072Z"}
{"level":"info","message":"🔥 Crawling processor initialized","service":"task-server","timestamp":"2025-07-13T11:30:48.154Z"}
{"level":"info","message":"🔥 Database processor initialized","service":"task-server","timestamp":"2025-07-13T11:30:48.154Z"}
{"level":"info","message":"🔥 Notification processor initialized","service":"task-server","timestamp":"2025-07-13T11:30:48.155Z"}
{"level":"info","message":"🔥 Task server is running on port 3001","service":"task-server","timestamp":"2025-07-13T11:30:48.159Z"}
{"level":"info","message":"🔥 Bull Board available at http://localhost:3001/admin/queues","service":"task-server","timestamp":"2025-07-13T11:30:48.159Z"}
{"level":"info","message":"🔥 Health check: http://localhost:3001/health","service":"task-server","timestamp":"2025-07-13T11:30:48.159Z"}
{"level":"info","message":"🔥 Redis config: localhost:6379","service":"task-server","timestamp":"2025-07-13T11:31:04.917Z"}
{"level":"info","message":"🔥 Crawling processor initialized","service":"task-server","timestamp":"2025-07-13T11:31:05.000Z"}
{"level":"info","message":"🔥 Database processor initialized","service":"task-server","timestamp":"2025-07-13T11:31:05.000Z"}
{"level":"info","message":"🔥 Notification processor initialized","service":"task-server","timestamp":"2025-07-13T11:31:05.001Z"}
{"level":"info","message":"🔥 Task server is running on port 3001","service":"task-server","timestamp":"2025-07-13T11:31:05.005Z"}
{"level":"info","message":"🔥 Bull Board available at http://localhost:3001/admin/queues","service":"task-server","timestamp":"2025-07-13T11:31:05.005Z"}
{"level":"info","message":"🔥 Health check: http://localhost:3001/health","service":"task-server","timestamp":"2025-07-13T11:31:05.005Z"}
{"level":"info","message":"🔥 Redis config: localhost:6379","service":"task-server","timestamp":"2025-07-13T11:31:20.854Z"}
{"level":"info","message":"🔥 Crawling processor initialized","service":"task-server","timestamp":"2025-07-13T11:31:20.954Z"}
{"level":"info","message":"🔥 Database processor initialized","service":"task-server","timestamp":"2025-07-13T11:31:20.955Z"}
{"level":"info","message":"🔥 Notification processor initialized","service":"task-server","timestamp":"2025-07-13T11:31:20.956Z"}
{"level":"info","message":"🔥 Task server is running on port 3001","service":"task-server","timestamp":"2025-07-13T11:31:20.960Z"}
{"level":"info","message":"🔥 Bull Board available at http://localhost:3001/admin/queues","service":"task-server","timestamp":"2025-07-13T11:31:20.960Z"}
{"level":"info","message":"🔥 Health check: http://localhost:3001/health","service":"task-server","timestamp":"2025-07-13T11:31:20.961Z"}
{"level":"info","message":"🔥 Redis config: localhost:6379","service":"task-server","timestamp":"2025-07-13T11:31:35.263Z"}
{"level":"info","message":"🔥 Crawling processor initialized","service":"task-server","timestamp":"2025-07-13T11:31:35.314Z"}
{"level":"info","message":"🔥 Database processor initialized","service":"task-server","timestamp":"2025-07-13T11:31:35.314Z"}
{"level":"info","message":"🔥 Notification processor initialized","service":"task-server","timestamp":"2025-07-13T11:31:35.314Z"}
{"level":"info","message":"🔥 Task server is running on port 3001","service":"task-server","timestamp":"2025-07-13T11:31:35.318Z"}
{"level":"info","message":"🔥 Bull Board available at http://localhost:3001/admin/queues","service":"task-server","timestamp":"2025-07-13T11:31:35.318Z"}
{"level":"info","message":"🔥 Health check: http://localhost:3001/health","service":"task-server","timestamp":"2025-07-13T11:31:35.318Z"}
{"level":"info","message":"🔥 Redis config: localhost:6379","service":"task-server","timestamp":"2025-07-13T11:31:45.809Z"}
{"level":"info","message":"🔥 Crawling processor initialized","service":"task-server","timestamp":"2025-07-13T11:31:45.862Z"}
{"level":"info","message":"🔥 Database processor initialized","service":"task-server","timestamp":"2025-07-13T11:31:45.863Z"}
{"level":"info","message":"🔥 Notification processor initialized","service":"task-server","timestamp":"2025-07-13T11:31:45.863Z"}
{"level":"info","message":"🔥 Task server is running on port 3001","service":"task-server","timestamp":"2025-07-13T11:31:45.866Z"}
{"level":"info","message":"🔥 Bull Board available at http://localhost:3001/admin/queues","service":"task-server","timestamp":"2025-07-13T11:31:45.866Z"}
{"level":"info","message":"🔥 Health check: http://localhost:3001/health","service":"task-server","timestamp":"2025-07-13T11:31:45.867Z"}
{"level":"info","message":"🔥 Redis config: localhost:6379","service":"task-server","timestamp":"2025-07-13T11:32:17.393Z"}
{"level":"info","message":"🔥 Crawling processor initialized","service":"task-server","timestamp":"2025-07-13T11:32:17.470Z"}
{"level":"info","message":"🔥 Database processor initialized","service":"task-server","timestamp":"2025-07-13T11:32:17.470Z"}
{"level":"info","message":"🔥 Notification processor initialized","service":"task-server","timestamp":"2025-07-13T11:32:17.471Z"}
{"level":"info","message":"🔥 Task server is running on port 3001","service":"task-server","timestamp":"2025-07-13T11:32:17.475Z"}
{"level":"info","message":"🔥 Bull Board available at http://localhost:3001/admin/queues","service":"task-server","timestamp":"2025-07-13T11:32:17.475Z"}
{"level":"info","message":"🔥 Health check: http://localhost:3001/health","service":"task-server","timestamp":"2025-07-13T11:32:17.475Z"}
{"level":"info","message":"🔥 Redis config: localhost:6379","service":"task-server","timestamp":"2025-07-13T11:32:26.571Z"}
{"level":"info","message":"🔥 Crawling processor initialized","service":"task-server","timestamp":"2025-07-13T11:32:26.622Z"}
{"level":"info","message":"🔥 Database processor initialized","service":"task-server","timestamp":"2025-07-13T11:32:26.622Z"}
{"level":"info","message":"🔥 Notification processor initialized","service":"task-server","timestamp":"2025-07-13T11:32:26.623Z"}
{"level":"info","message":"🔥 Task server is running on port 3001","service":"task-server","timestamp":"2025-07-13T11:32:26.626Z"}
{"level":"info","message":"🔥 Bull Board available at http://localhost:3001/admin/queues","service":"task-server","timestamp":"2025-07-13T11:32:26.626Z"}
{"level":"info","message":"🔥 Health check: http://localhost:3001/health","service":"task-server","timestamp":"2025-07-13T11:32:26.626Z"}
{"level":"info","message":"🔥 Redis config: localhost:6379","service":"task-server","timestamp":"2025-07-13T11:32:37.860Z"}
{"level":"info","message":"🔥 Crawling processor initialized","service":"task-server","timestamp":"2025-07-13T11:32:37.937Z"}
{"level":"info","message":"🔥 Database processor initialized","service":"task-server","timestamp":"2025-07-13T11:32:37.938Z"}
{"level":"info","message":"🔥 Notification processor initialized","service":"task-server","timestamp":"2025-07-13T11:32:37.938Z"}
{"level":"warn","message":"🔥 Port 3001 is in use, trying port 3002","service":"task-server","timestamp":"2025-07-13T11:32:37.943Z"}
{"level":"info","message":"🔥 Task server is running on port 3002","service":"task-server","timestamp":"2025-07-13T11:32:37.943Z"}
{"level":"info","message":"🔥 Bull Board available at http://localhost:3002/admin/queues","service":"task-server","timestamp":"2025-07-13T11:32:37.943Z"}
{"level":"info","message":"🔥 Health check: http://localhost:3002/health","service":"task-server","timestamp":"2025-07-13T11:32:37.943Z"}
{"level":"info","message":"🔥 Redis config: localhost:6379","service":"task-server","timestamp":"2025-07-13T11:35:32.573Z"}
{"level":"info","message":"🔥 Crawling processor initialized","service":"task-server","timestamp":"2025-07-13T11:35:32.661Z"}
{"level":"info","message":"🔥 Database processor initialized","service":"task-server","timestamp":"2025-07-13T11:35:32.661Z"}
{"level":"info","message":"🔥 Notification processor initialized","service":"task-server","timestamp":"2025-07-13T11:35:32.662Z"}
{"level":"info","message":"🔥 Task server is running on port 3001","service":"task-server","timestamp":"2025-07-13T11:35:32.666Z"}
{"level":"info","message":"🔥 Bull Board available at http://localhost:3001/admin/queues","service":"task-server","timestamp":"2025-07-13T11:35:32.666Z"}
{"level":"info","message":"🔥 Health check: http://localhost:3001/health","service":"task-server","timestamp":"2025-07-13T11:35:32.666Z"}
{"level":"info","message":"🔥 Redis config: localhost:6379","service":"task-server","timestamp":"2025-07-13T11:36:16.316Z"}
{"level":"info","message":"🔥 Crawling processor initialized","service":"task-server","timestamp":"2025-07-13T11:36:16.368Z"}
{"level":"info","message":"🔥 Database processor initialized","service":"task-server","timestamp":"2025-07-13T11:36:16.368Z"}
{"level":"info","message":"🔥 Notification processor initialized","service":"task-server","timestamp":"2025-07-13T11:36:16.368Z"}
{"level":"info","message":"🔥 Task server is running on port 3001","service":"task-server","timestamp":"2025-07-13T11:36:16.373Z"}
{"level":"info","message":"🔥 Bull Board available at http://localhost:3001/admin/queues","service":"task-server","timestamp":"2025-07-13T11:36:16.373Z"}
{"level":"info","message":"🔥 Health check: http://localhost:3001/health","service":"task-server","timestamp":"2025-07-13T11:36:16.373Z"}
