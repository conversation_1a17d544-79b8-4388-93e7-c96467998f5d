/*! For license information please see 42801.f84a09f4.js.LICENSE.txt */
(self.webpackChunk_bull_board_ui=self.webpackChunk_bull_board_ui||[]).push([["42801"],{49732:function(e,t,n){"use strict";n.d(t,{Ry:()=>u});var r=new WeakMap,o=new WeakMap,i={},a=0,s=function(e){return e&&(e.host||s(e.parentNode))},l=function(e,t,n,l){var u=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=s(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});i[n]||(i[n]=new WeakMap);var c=i[n],d=[],f=new Set,p=new Set(u),h=function(e){!e||f.has(e)||(f.add(e),h(e.parentNode))};u.forEach(h);var g=function(e){!e||p.has(e)||Array.prototype.forEach.call(e.children,function(e){if(f.has(e))g(e);else try{var t=e.getAttribute(l),i=null!==t&&"false"!==t,a=(r.get(e)||0)+1,s=(c.get(e)||0)+1;r.set(e,a),c.set(e,s),d.push(e),1===a&&i&&o.set(e,!0),1===s&&e.setAttribute(n,"true"),i||e.setAttribute(l,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return g(t),f.clear(),a++,function(){d.forEach(function(e){var t=r.get(e)-1,i=c.get(e)-1;r.set(e,t),c.set(e,i),t||(o.has(e)||e.removeAttribute(l),o.delete(e)),i||e.removeAttribute(n)}),--a||(r=new WeakMap,r=new WeakMap,o=new WeakMap,i={})}},u=function(e,t,n){void 0===n&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),o=t||("undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live]"))),l(r,o,n,"aria-hidden")):function(){return null}}},96081:function(e,t,n){"use strict";n.d(t,{Z:()=>c});var r,o={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}};function i(e){return function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=t.width?String(t.width):e.defaultWidth;return e.formats[n]||e.formats[e.defaultWidth]}}var a={date:i({formats:{full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},defaultWidth:"full"}),time:i({formats:{full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},defaultWidth:"full"}),dateTime:i({formats:{full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},defaultWidth:"full"})},s={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"};function l(e){return function(t,n){var r;if("formatting"===(null!=n&&n.context?String(n.context):"standalone")&&e.formattingValues){var o=e.defaultFormattingWidth||e.defaultWidth,i=null!=n&&n.width?String(n.width):o;r=e.formattingValues[i]||e.formattingValues[o]}else{var a=e.defaultWidth,s=null!=n&&n.width?String(n.width):e.defaultWidth;r=e.values[s]||e.values[a]}return r[e.argumentCallback?e.argumentCallback(t):t]}}function u(e){return function(t){var n,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},o=r.width,i=o&&e.matchPatterns[o]||e.matchPatterns[e.defaultMatchWidth],a=t.match(i);if(!a)return null;var s=a[0],l=o&&e.parsePatterns[o]||e.parsePatterns[e.defaultParseWidth],u=Array.isArray(l)?function(e,t){for(var n=0;n<e.length;n++)if(t(e[n]))return n}(l,function(e){return e.test(s)}):function(e,t){for(var n in e)if(e.hasOwnProperty(n)&&t(e[n]))return n}(l,function(e){return e.test(s)});return n=e.valueCallback?e.valueCallback(u):u,{value:n=r.valueCallback?r.valueCallback(n):n,rest:t.slice(s.length)}}}let c={code:"en-US",formatDistance:function(e,t,n){var r,i=o[e];if(r="string"==typeof i?i:1===t?i.one:i.other.replace("{{count}}",t.toString()),null!=n&&n.addSuffix)if(n.comparison&&n.comparison>0)return"in "+r;else return r+" ago";return r},formatLong:a,formatRelative:function(e,t,n,r){return s[e]},localize:{ordinalNumber:function(e,t){var n=Number(e),r=n%100;if(r>20||r<10)switch(r%10){case 1:return n+"st";case 2:return n+"nd";case 3:return n+"rd"}return n+"th"},era:l({values:{narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},defaultWidth:"wide"}),quarter:l({values:{narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},defaultWidth:"wide",argumentCallback:function(e){return e-1}}),month:l({values:{narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},defaultWidth:"wide"}),day:l({values:{narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},defaultWidth:"wide"}),dayPeriod:l({values:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},defaultWidth:"wide",formattingValues:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},defaultFormattingWidth:"wide"})},match:{ordinalNumber:(r={matchPattern:/^(\d+)(th|st|nd|rd)?/i,parsePattern:/\d+/i,valueCallback:function(e){return parseInt(e,10)}},function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=e.match(r.matchPattern);if(!n)return null;var o=n[0],i=e.match(r.parsePattern);if(!i)return null;var a=r.valueCallback?r.valueCallback(i[0]):i[0];return{value:a=t.valueCallback?t.valueCallback(a):a,rest:e.slice(o.length)}}),era:u({matchPatterns:{narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^b/i,/^(a|c)/i]},defaultParseWidth:"any"}),quarter:u({matchPatterns:{narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},defaultMatchWidth:"wide",parsePatterns:{any:[/1/i,/2/i,/3/i,/4/i]},defaultParseWidth:"any",valueCallback:function(e){return e+1}}),month:u({matchPatterns:{narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},defaultParseWidth:"any"}),day:u({matchPatterns:{narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},defaultParseWidth:"any"}),dayPeriod:u({matchPatterns:{narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},defaultMatchWidth:"any",parsePatterns:{any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},defaultParseWidth:"any"})},options:{weekStartsOn:0,firstWeekContainsDate:1}}},73463:function(e,t,n){"use strict";var r=n(48570),o={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},i={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},a={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},s={};function l(e){return r.isMemo(e)?a:s[e.$$typeof]||o}s[r.ForwardRef]={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},s[r.Memo]=a;var u=Object.defineProperty,c=Object.getOwnPropertyNames,d=Object.getOwnPropertySymbols,f=Object.getOwnPropertyDescriptor,p=Object.getPrototypeOf,h=Object.prototype;e.exports=function e(t,n,r){if("string"!=typeof n){if(h){var o=p(n);o&&o!==h&&e(t,o,r)}var a=c(n);d&&(a=a.concat(d(n)));for(var s=l(t),g=l(n),m=0;m<a.length;++m){var y=a[m];if(!i[y]&&!(r&&r[y])&&!(g&&g[y])&&!(s&&s[y])){var v=f(n,y);try{u(t,y,v)}catch(e){}}}}return t}},37320:function(e){"use strict";var t=Object.getOwnPropertySymbols,n=Object.prototype.hasOwnProperty,r=Object.prototype.propertyIsEnumerable;e.exports=!function(){try{if(!Object.assign)return!1;var e=new String("abc");if(e[5]="de","5"===Object.getOwnPropertyNames(e)[0])return!1;for(var t={},n=0;n<10;n++)t["_"+String.fromCharCode(n)]=n;var r=Object.getOwnPropertyNames(t).map(function(e){return t[e]});if("0123456789"!==r.join(""))return!1;var o={};if("abcdefghijklmnopqrst".split("").forEach(function(e){o[e]=e}),"abcdefghijklmnopqrst"!==Object.keys(Object.assign({},o)).join(""))return!1;return!0}catch(e){return!1}}()?function(e,o){for(var i,a,s=function(e){if(null==e)throw TypeError("Object.assign cannot be called with null or undefined");return Object(e)}(e),l=1;l<arguments.length;l++){for(var u in i=Object(arguments[l]))n.call(i,u)&&(s[u]=i[u]);if(t){a=t(i);for(var c=0;c<a.length;c++)r.call(i,a[c])&&(s[a[c]]=i[a[c]])}}return s}:Object.assign},68262:function(e,t,n){"use strict";var r=n(23586);function o(){}function i(){}i.resetWarningCache=o,e.exports=function(){function e(e,t,n,o,i,a){if(a!==r){var s=Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw s.name="Invariant Violation",s}}function t(){return e}e.isRequired=e;var n={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:i,resetWarningCache:o};return n.PropTypes=n,n}},13980:function(e,t,n){e.exports=n(68262)()},23586:function(e){"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},66866:function(e,t){"use strict";var n="function"==typeof Symbol&&Symbol.for,r=n?Symbol.for("react.element"):60103,o=n?Symbol.for("react.portal"):60106,i=n?Symbol.for("react.fragment"):60107,a=n?Symbol.for("react.strict_mode"):60108,s=n?Symbol.for("react.profiler"):60114,l=n?Symbol.for("react.provider"):60109,u=n?Symbol.for("react.context"):60110,c=n?Symbol.for("react.async_mode"):60111,d=n?Symbol.for("react.concurrent_mode"):60111,f=n?Symbol.for("react.forward_ref"):60112,p=n?Symbol.for("react.suspense"):60113,h=n?Symbol.for("react.suspense_list"):60120,g=n?Symbol.for("react.memo"):60115,m=n?Symbol.for("react.lazy"):60116,y=n?Symbol.for("react.block"):60121,v=n?Symbol.for("react.fundamental"):60117,b=n?Symbol.for("react.responder"):60118,w=n?Symbol.for("react.scope"):60119;function x(e){if("object"==typeof e&&null!==e){var t=e.$$typeof;switch(t){case r:switch(e=e.type){case c:case d:case i:case s:case a:case p:return e;default:switch(e=e&&e.$$typeof){case u:case f:case m:case g:case l:return e;default:return t}}case o:return t}}}function S(e){return x(e)===d}t.AsyncMode=c,t.ConcurrentMode=d,t.ContextConsumer=u,t.ContextProvider=l,t.Element=r,t.ForwardRef=f,t.Fragment=i,t.Lazy=m,t.Memo=g,t.Portal=o,t.Profiler=s,t.StrictMode=a,t.Suspense=p,t.isAsyncMode=function(e){return S(e)||x(e)===c},t.isConcurrentMode=S,t.isContextConsumer=function(e){return x(e)===u},t.isContextProvider=function(e){return x(e)===l},t.isElement=function(e){return"object"==typeof e&&null!==e&&e.$$typeof===r},t.isForwardRef=function(e){return x(e)===f},t.isFragment=function(e){return x(e)===i},t.isLazy=function(e){return x(e)===m},t.isMemo=function(e){return x(e)===g},t.isPortal=function(e){return x(e)===o},t.isProfiler=function(e){return x(e)===s},t.isStrictMode=function(e){return x(e)===a},t.isSuspense=function(e){return x(e)===p},t.isValidElementType=function(e){return"string"==typeof e||"function"==typeof e||e===i||e===d||e===s||e===a||e===p||e===h||"object"==typeof e&&null!==e&&(e.$$typeof===m||e.$$typeof===g||e.$$typeof===l||e.$$typeof===u||e.$$typeof===f||e.$$typeof===v||e.$$typeof===b||e.$$typeof===w||e.$$typeof===y)},t.typeOf=x},48570:function(e,t,n){"use strict";e.exports=n(66866)},82577:function(e,t,n){"use strict";n.d(t,{Z:()=>J});var r,o,i,a,s,l,u,c=function(){return(c=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function d(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}var f=n(2784),p="right-scroll-bar-position",h="width-before-scroll-bar";function g(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var m="undefined"!=typeof window?f.useLayoutEffect:f.useEffect,y=new WeakMap,v=(void 0===o&&(o={}),(void 0===i&&(i=function(e){return e}),a=[],s=!1,l={read:function(){if(s)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return a.length?a[a.length-1]:null},useMedium:function(e){var t=i(e,s);return a.push(t),function(){a=a.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(s=!0;a.length;){var t=a;a=[],t.forEach(e)}a={push:function(t){return e(t)},filter:function(){return a}}},assignMedium:function(e){s=!0;var t=[];if(a.length){var n=a;a=[],n.forEach(e),t=a}var r=function(){var n=t;t=[],n.forEach(e)},o=function(){return Promise.resolve().then(r)};o(),a={push:function(e){t.push(e),o()},filter:function(e){return t=t.filter(e),a}}}}).options=c({async:!0,ssr:!1},o),l),b=function(){},w=f.forwardRef(function(e,t){var n,r,o,i,a=f.useRef(null),s=f.useState({onScrollCapture:b,onWheelCapture:b,onTouchMoveCapture:b}),l=s[0],u=s[1],p=e.forwardProps,h=e.children,w=e.className,x=e.removeScrollBar,S=e.enabled,O=e.shards,E=e.sideCar,C=e.noIsolation,R=e.inert,P=e.allowPinchZoom,k=e.as,j=e.gapMode,T=d(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as","gapMode"]),L=(n=[a,t],r=function(e){return n.forEach(function(t){return g(t,e)})},(o=(0,f.useState)(function(){return{value:null,callback:r,facade:{get current(){return o.value},set current(value){var e=o.value;e!==value&&(o.value=value,o.callback(value,e))}}}})[0]).callback=r,i=o.facade,m(function(){var e=y.get(i);if(e){var t=new Set(e),r=new Set(n),o=i.current;t.forEach(function(e){r.has(e)||g(e,null)}),r.forEach(function(e){t.has(e)||g(e,o)})}y.set(i,n)},[n]),i),M=c(c({},T),l);return f.createElement(f.Fragment,null,S&&f.createElement(E,{sideCar:v,removeScrollBar:x,shards:O,noIsolation:C,inert:R,setCallbacks:u,allowPinchZoom:!!P,lockRef:a,gapMode:j}),p?f.cloneElement(f.Children.only(h),c(c({},M),{ref:L})):f.createElement(void 0===k?"div":k,c({},M,{className:w,ref:L}),h))});w.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},w.classNames={fullWidth:h,zeroRight:p};var x=function(e){var t=e.sideCar,n=d(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return f.createElement(r,c({},n))};x.isSideCarExport=!0;var S=function(){var e=0,t=null;return{add:function(o){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=r||n.nc;return t&&e.setAttribute("nonce",t),e}())){var i,a;(i=t).styleSheet?i.styleSheet.cssText=o:i.appendChild(document.createTextNode(o)),a=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(a)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},O=function(){var e=S();return function(t,n){f.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},E=function(){var e=O();return function(t){return e(t.styles,t.dynamic),null}},C={left:0,top:0,right:0,gap:0},R=function(e){return parseInt(e||"",10)||0},P=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[R(n),R(r),R(o)]},k=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return C;var t=P(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},j=E(),T="data-scroll-locked",L=function(e,t,n,r){var o=e.left,i=e.top,a=e.right,s=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(s,"px ").concat(r,";\n  }\n  body[").concat(T,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(i,"px;\n    padding-right: ").concat(a,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(s,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(s,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(p," {\n    right: ").concat(s,"px ").concat(r,";\n  }\n  \n  .").concat(h," {\n    margin-right: ").concat(s,"px ").concat(r,";\n  }\n  \n  .").concat(p," .").concat(p," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(h," .").concat(h," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(T,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(s,"px;\n  }\n")},M=function(){var e=parseInt(document.body.getAttribute(T)||"0",10);return isFinite(e)?e:0},N=function(){f.useEffect(function(){return document.body.setAttribute(T,(M()+1).toString()),function(){var e=M()-1;e<=0?document.body.removeAttribute(T):document.body.setAttribute(T,e.toString())}},[])},D=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;N();var i=f.useMemo(function(){return k(o)},[o]);return f.createElement(j,{styles:L(i,!t,o,n?"":"!important")})},I=!1;if("undefined"!=typeof window)try{var A=Object.defineProperty({},"passive",{get:function(){return I=!0,!0}});window.addEventListener("test",A,A),window.removeEventListener("test",A,A)}catch(e){I=!1}var F=!!I&&{passive:!1},$=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&(n.overflowY!==n.overflowX||"TEXTAREA"===e.tagName||"visible"!==n[t])},_=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),W(e,r)){var o=V(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},W=function(e,t){return"v"===e?$(t,"overflowY"):$(t,"overflowX")},V=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},H=function(e,t,n,r,o){var i,a=(i=window.getComputedStyle(t).direction,"h"===e&&"rtl"===i?-1:1),s=a*r,l=n.target,u=t.contains(l),c=!1,d=s>0,f=0,p=0;do{var h=V(e,l),g=h[0],m=h[1]-h[2]-a*g;(g||m)&&W(e,l)&&(f+=m,p+=g),l=l instanceof ShadowRoot?l.host:l.parentNode}while(!u&&l!==document.body||u&&(t.contains(l)||t===l));return d&&(o&&1>Math.abs(f)||!o&&s>f)?c=!0:!d&&(o&&1>Math.abs(p)||!o&&-s>p)&&(c=!0),c},B=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},z=function(e){return[e.deltaX,e.deltaY]},U=function(e){return e&&"current"in e?e.current:e},K=0,q=[];let X=(u=function(e){var t=f.useRef([]),n=f.useRef([0,0]),r=f.useRef(),o=f.useState(K++)[0],i=f.useState(E)[0],a=f.useRef(e);f.useEffect(function(){a.current=e},[e]),f.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(function(e,t,n){if(n||2==arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(U),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var s=f.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!a.current.allowPinchZoom;var o,i=B(e),s=n.current,l="deltaX"in e?e.deltaX:s[0]-i[0],u="deltaY"in e?e.deltaY:s[1]-i[1],c=e.target,d=Math.abs(l)>Math.abs(u)?"h":"v";if("touches"in e&&"h"===d&&"range"===c.type)return!1;var f=_(d,c);if(!f)return!0;if(f?o=d:(o="v"===d?"h":"v",f=_(d,c)),!f)return!1;if(!r.current&&"changedTouches"in e&&(l||u)&&(r.current=o),!o)return!0;var p=r.current||o;return H(p,t,e,"h"===p?l:u,!0)},[]),l=f.useCallback(function(e){if(q.length&&q[q.length-1]===i){var n="deltaY"in e?z(e):B(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta,r[0]===n[0]&&r[1]===n[1])})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(a.current.shards||[]).map(U).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?s(e,o[0]):!a.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),u=f.useCallback(function(e,n,r,o){var i={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(i),setTimeout(function(){t.current=t.current.filter(function(e){return e!==i})},1)},[]),c=f.useCallback(function(e){n.current=B(e),r.current=void 0},[]),d=f.useCallback(function(t){u(t.type,z(t),t.target,s(t,e.lockRef.current))},[]),p=f.useCallback(function(t){u(t.type,B(t),t.target,s(t,e.lockRef.current))},[]);f.useEffect(function(){return q.push(i),e.setCallbacks({onScrollCapture:d,onWheelCapture:d,onTouchMoveCapture:p}),document.addEventListener("wheel",l,F),document.addEventListener("touchmove",l,F),document.addEventListener("touchstart",c,F),function(){q=q.filter(function(e){return e!==i}),document.removeEventListener("wheel",l,F),document.removeEventListener("touchmove",l,F),document.removeEventListener("touchstart",c,F)}},[]);var h=e.removeScrollBar,g=e.inert;return f.createElement(f.Fragment,null,g?f.createElement(i,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,h?f.createElement(D,{gapMode:e.gapMode}):null)},v.useMedium(u),x);var Y=f.forwardRef(function(e,t){return f.createElement(w,c({},e,{ref:t,sideCar:X}))});Y.classNames=w.classNames;let J=Y},83690:function(e,t,n){"use strict";n.d(t,{Am:()=>U,Ix:()=>F});var r,o,i,a,s,l,u,c,d,f,p,h,g=n(2784);let m=function(){for(var e,t,n=0,r="";n<arguments.length;)(e=arguments[n++])&&(t=function e(t){var n,r,o="";if("string"==typeof t||"number"==typeof t)o+=t;else if("object"==typeof t)if(Array.isArray(t))for(n=0;n<t.length;n++)t[n]&&(r=e(t[n]))&&(o&&(o+=" "),o+=r);else for(n in t)t[n]&&(o&&(o+=" "),o+=n);return o}(e))&&(r&&(r+=" "),r+=t);return r};var y=n(28316);function v(){return(v=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}function b(e){return"number"==typeof e&&!isNaN(e)}function w(e){return"boolean"==typeof e}function x(e){return"string"==typeof e}function S(e){return"function"==typeof e}function O(e){return x(e)||S(e)?e:null}var E=!!("undefined"!=typeof window&&window.document&&window.document.createElement);function C(e){return(0,g.isValidElement)(e)||x(e)||S(e)||b(e)}var R={TOP_LEFT:"top-left",TOP_RIGHT:"top-right",TOP_CENTER:"top-center",BOTTOM_LEFT:"bottom-left",BOTTOM_RIGHT:"bottom-right",BOTTOM_CENTER:"bottom-center"},P={INFO:"info",SUCCESS:"success",WARNING:"warning",ERROR:"error",DEFAULT:"default",DARK:"dark"},k={list:new Map,emitQueue:new Map,on:function(e,t){return this.list.has(e)||this.list.set(e,[]),this.list.get(e).push(t),this},off:function(e,t){if(t){var n=this.list.get(e).filter(function(e){return e!==t});return this.list.set(e,n),this}return this.list.delete(e),this},cancelEmit:function(e){var t=this.emitQueue.get(e);return t&&(t.forEach(clearTimeout),this.emitQueue.delete(e)),this},emit:function(e){for(var t=this,n=arguments.length,r=Array(n>1?n-1:0),o=1;o<n;o++)r[o-1]=arguments[o];this.list.has(e)&&this.list.get(e).forEach(function(n){var o=setTimeout(function(){n.apply(void 0,r)},0);t.emitQueue.has(e)||t.emitQueue.set(e,[]),t.emitQueue.get(e).push(o)})}};function j(e,t){void 0===t&&(t=!1);var n=(0,g.useRef)(e);return(0,g.useEffect)(function(){t&&(n.current=e)}),n.current}function T(e,t){switch(t.type){case 0:return[].concat(e,[t.toastId]).filter(function(e){return e!==t.staleId});case 1:var n;return 0===(n=t.toastId)||n?e.filter(function(e){return e!==t.toastId}):[]}}function L(e){return e.targetTouches&&e.targetTouches.length>=1?e.targetTouches[0].clientX:e.clientX}function M(e){return e.targetTouches&&e.targetTouches.length>=1?e.targetTouches[0].clientY:e.clientY}function N(e){var t=e.closeToast,n=e.type,r=e.ariaLabel;return(0,g.createElement)("button",{className:"Toastify__close-button Toastify__close-button--"+n,type:"button",onClick:function(e){e.stopPropagation(),t(e)},"aria-label":void 0===r?"close":r},(0,g.createElement)("svg",{"aria-hidden":"true",viewBox:"0 0 14 16"},(0,g.createElement)("path",{fillRule:"evenodd",d:"M7.71 8.23l3.75 3.75-1.48 1.48-3.75-3.75-3.75 3.75L1 11.98l3.75-3.75L1 4.48 2.48 3l3.75 3.75L9.98 3l1.48 1.48-3.75 3.75z"})))}function D(e){var t,n,r=e.delay,o=e.isRunning,i=e.closeToast,a=e.type,s=e.hide,l=e.className,u=e.style,c=e.controlledProgress,d=e.progress,f=e.rtl,p=e.isIn,h=v({},u,{animationDuration:r+"ms",animationPlayState:o?"running":"paused",opacity:+!s});c&&(h.transform="scaleX("+d+")");var y=m("Toastify__progress-bar",c?"Toastify__progress-bar--controlled":"Toastify__progress-bar--animated","Toastify__progress-bar--"+a,((t={})["Toastify__progress-bar--rtl"]=f,t)),b=S(l)?l({rtl:f,type:a,defaultClassName:y}):m(y,l),w=((n={})[c&&d>=1?"onTransitionEnd":"onAnimationEnd"]=c&&d<1?null:function(){p&&i()},n);return(0,g.createElement)("div",Object.assign({role:"progressbar","aria-hidden":s?"true":"false","aria-label":"notification timer",className:b,style:h},w))}D.defaultProps={type:P.DEFAULT,hide:!1};var I=function(e){var t,n=function(e){var t=(0,g.useState)(!0),n=t[0],r=t[1],o=(0,g.useState)(!1),i=o[0],a=o[1],s=(0,g.useRef)(null),l=j({start:0,x:0,y:0,delta:0,removalDistance:0,canCloseOnClick:!0,canDrag:!1,boundingRect:null}),u=j(e,!0),c=e.autoClose,d=e.pauseOnHover,f=e.closeToast,p=e.onClick,h=e.closeOnClick;function m(t){if(e.draggable){var n=s.current;l.canCloseOnClick=!0,l.canDrag=!0,l.boundingRect=n.getBoundingClientRect(),n.style.transition="",l.x=L(t.nativeEvent),l.y=M(t.nativeEvent),"x"===e.draggableDirection?(l.start=l.x,l.removalDistance=n.offsetWidth*(e.draggablePercent/100)):(l.start=l.y,l.removalDistance=n.offsetHeight*(e.draggablePercent/100))}}function y(){if(l.boundingRect){var t=l.boundingRect,n=t.top,r=t.bottom,o=t.left,i=t.right;e.pauseOnHover&&l.x>=o&&l.x<=i&&l.y>=n&&l.y<=r?b():v()}}function v(){r(!0)}function b(){r(!1)}function w(t){if(l.canDrag){t.preventDefault();var r=s.current;n&&b(),l.x=L(t),l.y=M(t),"x"===e.draggableDirection?l.delta=l.x-l.start:l.delta=l.y-l.start,l.start!==l.x&&(l.canCloseOnClick=!1),r.style.transform="translate"+e.draggableDirection+"("+l.delta+"px)",r.style.opacity=""+(1-Math.abs(l.delta/l.removalDistance))}}function x(){var t=s.current;if(l.canDrag){if(l.canDrag=!1,Math.abs(l.delta)>l.removalDistance){a(!0),e.closeToast();return}t.style.transition="transform 0.2s, opacity 0.2s",t.style.transform="translate"+e.draggableDirection+"(0)",t.style.opacity="1"}}(0,g.useEffect)(function(){return S(e.onOpen)&&e.onOpen((0,g.isValidElement)(e.children)&&e.children.props),function(){S(u.onClose)&&u.onClose((0,g.isValidElement)(u.children)&&u.children.props)}},[]),(0,g.useEffect)(function(){return e.draggable&&(document.addEventListener("mousemove",w),document.addEventListener("mouseup",x),document.addEventListener("touchmove",w),document.addEventListener("touchend",x)),function(){e.draggable&&(document.removeEventListener("mousemove",w),document.removeEventListener("mouseup",x),document.removeEventListener("touchmove",w),document.removeEventListener("touchend",x))}},[e.draggable]),(0,g.useEffect)(function(){return e.pauseOnFocusLoss&&(document.hasFocus()||b(),window.addEventListener("focus",v),window.addEventListener("blur",b)),function(){e.pauseOnFocusLoss&&(window.removeEventListener("focus",v),window.removeEventListener("blur",b))}},[e.pauseOnFocusLoss]);var O={onMouseDown:m,onTouchStart:m,onMouseUp:y,onTouchEnd:y};return c&&d&&(O.onMouseEnter=b,O.onMouseLeave=v),h&&(O.onClick=function(e){p&&p(e),l.canCloseOnClick&&f()}),{playToast:v,pauseToast:b,isRunning:n,preventExitTransition:i,toastRef:s,eventHandlers:O}}(e),r=n.isRunning,o=n.preventExitTransition,i=n.toastRef,a=n.eventHandlers,s=e.closeButton,l=e.children,u=e.autoClose,c=e.onClick,d=e.type,f=e.hideProgressBar,p=e.closeToast,h=e.transition,y=e.position,v=e.className,b=e.style,w=e.bodyClassName,x=e.bodyStyle,O=e.progressClassName,E=e.progressStyle,C=e.updateId,R=e.role,P=e.progress,k=e.rtl,T=e.toastId,N=e.deleteToast,I=e.isIn,A=m("Toastify__toast","Toastify__toast--"+d,((t={})["Toastify__toast--rtl"]=k,t)),F=S(v)?v({rtl:k,position:y,type:d,defaultClassName:A}):m(A,v),$=!!P;return(0,g.createElement)(h,{isIn:I,done:N,position:y,preventExitTransition:o,nodeRef:i},(0,g.createElement)("div",Object.assign({id:T,onClick:c,className:F},a,{style:b,ref:i}),(0,g.createElement)("div",Object.assign({},I&&{role:R},{className:S(w)?w({type:d}):m("Toastify__toast-body",w),style:x}),l),function(e){if(e){var t={closeToast:p,type:d};if(S(e))return e(t);if((0,g.isValidElement)(e))return(0,g.cloneElement)(e,t)}}(s),(u||$)&&(0,g.createElement)(D,Object.assign({},C&&!$?{key:"pb-"+C}:{},{rtl:k,delay:u,isRunning:r,isIn:I,closeToast:p,hide:f,type:d,style:E,className:O,controlledProgress:$,progress:P}))))},A=(o=(r={enter:"Toastify--animate Toastify__bounce-enter",exit:"Toastify--animate Toastify__bounce-exit",appendPosition:!0}).enter,i=r.exit,s=void 0!==(a=r.appendPosition)&&a,u=void 0===(l=r.collapse)||l,d=void 0===(c=r.collapseDuration)?300:c,function(e){var t=e.children,n=e.position,r=e.preventExitTransition,a=e.done,l=e.nodeRef,c=e.isIn,f=s?o+"--"+n:o,p=s?i+"--"+n:i,h=(0,g.useRef)(),m=(0,g.useRef)(0);function y(){var e=l.current;e.removeEventListener("animationend",y),0===m.current&&(e.className=h.current)}function v(){var e,t,n,r=l.current;r.removeEventListener("animationend",v),u?(void 0===(e=d)&&(e=300),t=r.scrollHeight,n=r.style,requestAnimationFrame(function(){n.minHeight="initial",n.height=t+"px",n.transition="all "+e+"ms",requestAnimationFrame(function(){n.height="0",n.padding="0",n.margin="0",setTimeout(a,e)})})):a()}return(0,g.useLayoutEffect)(function(){var e;h.current=(e=l.current).className,e.className+=" "+f,e.addEventListener("animationend",y)},[]),(0,g.useEffect)(function(){var e;c||(r?v():(m.current=1,e=l.current,e.className+=" "+p,e.addEventListener("animationend",v)))},[c]),g.createElement(g.Fragment,null,t)}),F=function(e){var t=function(e){var t=(0,g.useReducer)(function(e){return e+1},0)[1],n=(0,g.useReducer)(T,[]),r=n[0],o=n[1],i=(0,g.useRef)(null),a=j(0),s=j([]),l=j({}),u=j({toastKey:1,displayedToast:0,props:e,containerId:null,isToastActive:c,getToast:function(e){return l[e]||null}});function c(e){return -1!==r.indexOf(e)}function d(e){var t=e.containerId;u.props.limit&&(!t||u.containerId===t)&&(a-=s.length,s=[])}function f(e){o({type:1,toastId:e})}function p(){var e=s.shift();m(e.toastContent,e.toastProps,e.staleId)}function h(e,n){var r=n.delay,o=n.staleId,c=function(e,t){if(null==e)return{};var n,r,o={},i=Object.keys(e);for(r=0;r<i.length;r++)t.indexOf(n=i[r])>=0||(o[n]=e[n]);return o}(n,["delay","staleId"]);if(!(!C(e)||(d=c.containerId,h=c.toastId,y=c.updateId,!i.current||u.props.enableMultiContainer&&d!==u.props.containerId||l[h]&&null==y))){var d,h,y,v,E,R=c.toastId,P=c.updateId,k=u.props,j=function(){return f(R)},T=null==c.updateId;T&&a++;var L={toastId:R,updateId:P,isIn:!1,key:c.key||u.toastKey++,type:c.type,closeToast:j,closeButton:c.closeButton,rtl:k.rtl,position:c.position||k.position,transition:c.transition||k.transition,className:O(c.className||k.toastClassName),bodyClassName:O(c.bodyClassName||k.bodyClassName),style:c.style||k.toastStyle,bodyStyle:c.bodyStyle||k.bodyStyle,onClick:c.onClick||k.onClick,pauseOnHover:w(c.pauseOnHover)?c.pauseOnHover:k.pauseOnHover,pauseOnFocusLoss:w(c.pauseOnFocusLoss)?c.pauseOnFocusLoss:k.pauseOnFocusLoss,draggable:w(c.draggable)?c.draggable:k.draggable,draggablePercent:b(c.draggablePercent)?c.draggablePercent:k.draggablePercent,draggableDirection:c.draggableDirection||k.draggableDirection,closeOnClick:w(c.closeOnClick)?c.closeOnClick:k.closeOnClick,progressClassName:O(c.progressClassName||k.progressClassName),progressStyle:c.progressStyle||k.progressStyle,autoClose:(v=c.autoClose,E=k.autoClose,!1===v||b(v)&&v>0?v:E),hideProgressBar:w(c.hideProgressBar)?c.hideProgressBar:k.hideProgressBar,progress:c.progress,role:x(c.role)?c.role:k.role,deleteToast:function(){!function(e){delete l[e];var n=s.length;if((a=0===e||e?a-1:a-u.displayedToast)<0&&(a=0),n>0){var r=0===e||e?1:u.props.limit;if(1===n||1===r)u.displayedToast++,p();else{var o=r>n?n:r;u.displayedToast=o;for(var i=0;i<o;i++)p()}}else t()}(R)}};S(c.onOpen)&&(L.onOpen=c.onOpen),S(c.onClose)&&(L.onClose=c.onClose),"y"===L.draggableDirection&&80===L.draggablePercent&&(L.draggablePercent*=1.5);var M=k.closeButton;!1===c.closeButton||C(c.closeButton)?M=c.closeButton:!0===c.closeButton&&(M=!C(k.closeButton)||k.closeButton),L.closeButton=M;var N=e;(0,g.isValidElement)(e)&&!x(e.type)?N=(0,g.cloneElement)(e,{closeToast:j,toastProps:L}):S(e)&&(N=e({closeToast:j,toastProps:L})),k.limit&&k.limit>0&&a>k.limit&&T?s.push({toastContent:N,toastProps:L,staleId:o}):b(r)&&r>0?setTimeout(function(){m(N,L,o)},r):m(N,L,o)}}function m(e,t,n){var r=t.toastId;n&&delete l[n],l[r]={content:e,props:t},o({type:0,toastId:r,staleId:n})}return(0,g.useEffect)(function(){return u.containerId=e.containerId,k.cancelEmit(3).on(0,h).on(1,function(e){return i.current&&f(e)}).on(5,d).emit(2,u),function(){return k.emit(3,u)}},[]),(0,g.useEffect)(function(){u.isToastActive=c,u.displayedToast=r.length,k.emit(4,r.length,e.containerId)},[r]),(0,g.useEffect)(function(){u.props=e}),{getToastToRender:function(t){for(var n={},r=e.newestOnTop?Object.keys(l).reverse():Object.keys(l),o=0;o<r.length;o++){var i=l[r[o]],a=i.props.position;n[a]||(n[a]=[]),n[a].push(i)}return Object.keys(n).map(function(e){return t(e,n[e])})},collection:l,containerRef:i,isToastActive:c}}(e),n=t.getToastToRender,r=t.containerRef,o=t.isToastActive,i=e.className,a=e.style,s=e.rtl,l=e.containerId;return(0,g.createElement)("div",{ref:r,className:"Toastify",id:l},n(function(e,t){var n,r,l=0===t.length?v({},a,{pointerEvents:"none"}):v({},a);return(0,g.createElement)("div",{className:(r=m("Toastify__toast-container","Toastify__toast-container--"+e,((n={})["Toastify__toast-container--rtl"]=s,n)),S(i)?i({position:e,rtl:s,defaultClassName:r}):m(r,O(i))),style:l,key:"container-"+e},t.map(function(e){var t=e.content,n=e.props;return(0,g.createElement)(I,Object.assign({},n,{isIn:o(n.toastId),key:"toast-"+n.key,closeButton:!0===n.closeButton?N:n.closeButton}),t)}))}))};F.defaultProps={position:R.TOP_RIGHT,transition:A,rtl:!1,autoClose:5e3,hideProgressBar:!1,closeButton:N,pauseOnHover:!0,pauseOnFocusLoss:!0,closeOnClick:!0,newestOnTop:!1,draggable:!0,draggablePercent:80,draggableDirection:"x",role:"alert"};var $=new Map,_=[],W=!1;function V(){return Math.random().toString(36).substr(2,9)}function H(e,t){return $.size>0?k.emit(0,e,t):(_.push({content:e,options:t}),W&&E&&(W=!1,p=document.createElement("div"),document.body.appendChild(p),(0,y.render)((0,g.createElement)(F,Object.assign({},h)),p))),t.toastId}function B(e,t){return v({},t,{type:t&&t.type||e,toastId:t&&(x(t.toastId)||b(t.toastId))?t.toastId:V()})}var z=function(e){return function(t,n){return H(t,B(e,n))}},U=function(e,t){return H(e,B(P.DEFAULT,t))};U.success=z(P.SUCCESS),U.info=z(P.INFO),U.error=z(P.ERROR),U.warning=z(P.WARNING),U.dark=z(P.DARK),U.warn=U.warning,U.dismiss=function(e){return k.emit(1,e)},U.clearWaitingQueue=function(e){return void 0===e&&(e={}),k.emit(5,e)},U.isActive=function(e){var t=!1;return $.forEach(function(n){n.isToastActive&&n.isToastActive(e)&&(t=!0)}),t},U.update=function(e,t){void 0===t&&(t={}),setTimeout(function(){var n,r,o=(n=t.containerId,(r=$.get(n||f))?r.getToast(e):null);if(o){var i=o.props,a=o.content,s=v({},i,t,{toastId:t.toastId||e,updateId:V()});s.toastId!==e&&(s.staleId=e);var l=s.render||a;delete s.render,H(l,s)}},0)},U.done=function(e){U.update(e,{progress:1})},U.onChange=function(e){return S(e)&&k.on(4,e),function(){S(e)&&k.off(4,e)}},U.configure=function(e){void 0===e&&(e={}),W=!0,h=e},U.POSITION=R,U.TYPE=P,k.on(2,function(e){f=e.containerId||e,$.set(f,e),_.forEach(function(e){k.emit(0,e.content,e.options)}),_=[]}).on(3,function(e){$.delete(e.containerId||e),0===$.size&&k.off(0).off(1).off(5),E&&p&&document.body.removeChild(p)})},57613:function(e,t,n){"use strict";function r(e){return"/"===e.charAt(0)}function o(e,t){for(var n=t,r=n+1,o=e.length;r<o;n+=1,r+=1)e[n]=e[r];e.pop()}n.d(t,{Z:()=>i});let i=function(e,t){void 0===t&&(t="");var n,i=e&&e.split("/")||[],a=t&&t.split("/")||[],s=e&&r(e),l=t&&r(t),u=s||l;if(e&&r(e)?a=i:i.length&&(a.pop(),a=a.concat(i)),!a.length)return"/";if(a.length){var c=a[a.length-1];n="."===c||".."===c||""===c}else n=!1;for(var d=0,f=a.length;f>=0;f--){var p=a[f];"."===p?o(a,f):".."===p?(o(a,f),d++):d&&(o(a,f),d--)}if(!u)for(;d--;)a.unshift("..");!u||""===a[0]||a[0]&&r(a[0])||a.unshift("");var h=a.join("/");return n&&"/"!==h.substr(-1)&&(h+="/"),h}},1458:function(e,t,n){"use strict";var r=n(2784),o="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},i=r.useState,a=r.useEffect,s=r.useLayoutEffect,l=r.useDebugValue;function u(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!o(e,n)}catch(e){return!0}}var c="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var n=t(),r=i({inst:{value:n,getSnapshot:t}}),o=r[0].inst,c=r[1];return s(function(){o.value=n,o.getSnapshot=t,u(o)&&c({inst:o})},[e,n,t]),a(function(){return u(o)&&c({inst:o}),e(function(){u(o)&&c({inst:o})})},[e]),l(n),n};t.useSyncExternalStore=void 0!==r.useSyncExternalStore?r.useSyncExternalStore:c},64141:function(e,t,n){"use strict";var r=n(2784),o=n(43100),i="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},a=o.useSyncExternalStore,s=r.useRef,l=r.useEffect,u=r.useMemo,c=r.useDebugValue;t.useSyncExternalStoreWithSelector=function(e,t,n,r,o){var d=s(null);if(null===d.current){var f={hasValue:!1,value:null};d.current=f}else f=d.current;var p=a(e,(d=u(function(){function e(e){if(!l){if(l=!0,a=e,e=r(e),void 0!==o&&f.hasValue){var t=f.value;if(o(t,e))return s=t}return s=e}if(t=s,i(a,e))return t;var n=r(e);return void 0!==o&&o(t,n)?(a=e,t):(a=e,s=n)}var a,s,l=!1,u=void 0===n?null:n;return[function(){return e(t())},null===u?void 0:function(){return e(u())}]},[t,n,r,o]))[0],d[1]);return l(function(){f.hasValue=!0,f.value=p},[p]),c(p),p}},43100:function(e,t,n){"use strict";e.exports=n(1458)},41110:function(e,t,n){"use strict";e.exports=n(64141)},64896:function(e){e.exports={area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0}},13714:function(e,t,n){"use strict";function r(){return(r=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(null,arguments)}n.d(t,{Z:()=>r})},12485:function(e,t,n){"use strict";function r(e,t){return(r=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function o(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,r(e,t)}n.d(t,{Z:()=>o})},83660:function(e,t,n){"use strict";function r(e,t){if(null==e)return{};var n={};for(var r in e)if(({}).hasOwnProperty.call(e,r)){if(-1!==t.indexOf(r))continue;n[r]=e[r]}return n}n.d(t,{Z:()=>r})},56137:function(e,t,n){"use strict";function r(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}n.d(t,{M:()=>r})},718:function(e,t,n){"use strict";n.d(t,{$j:()=>I,Dx:()=>A,VY:()=>N,aU:()=>D,aV:()=>M,dk:()=>F,fC:()=>T,h_:()=>L});var r=n(2784),o=n(8210),i=n(59656),a=n(78364),s=n(56137),l=n(33883),u=n(52322),c="AlertDialog",[d,f]=(0,o.b)(c,[a.p8]),p=(0,a.p8)(),h=e=>{let{__scopeAlertDialog:t,...n}=e,r=p(t);return(0,u.jsx)(a.fC,{...r,...n,modal:!0})};h.displayName=c,r.forwardRef((e,t)=>{let{__scopeAlertDialog:n,...r}=e,o=p(n);return(0,u.jsx)(a.xz,{...o,...r,ref:t})}).displayName="AlertDialogTrigger";var g=e=>{let{__scopeAlertDialog:t,...n}=e,r=p(t);return(0,u.jsx)(a.h_,{...r,...n})};g.displayName="AlertDialogPortal";var m=r.forwardRef((e,t)=>{let{__scopeAlertDialog:n,...r}=e,o=p(n);return(0,u.jsx)(a.aV,{...o,...r,ref:t})});m.displayName="AlertDialogOverlay";var y="AlertDialogContent",[v,b]=d(y),w=(0,l.sA)("AlertDialogContent"),x=r.forwardRef((e,t)=>{let{__scopeAlertDialog:n,children:o,...l}=e,c=p(n),d=r.useRef(null),f=(0,i.e)(t,d),h=r.useRef(null);return(0,u.jsx)(a.jm,{contentName:y,titleName:S,docsSlug:"alert-dialog",children:(0,u.jsx)(v,{scope:n,cancelRef:h,children:(0,u.jsxs)(a.VY,{role:"alertdialog",...c,...l,ref:f,onOpenAutoFocus:(0,s.M)(l.onOpenAutoFocus,e=>{e.preventDefault(),h.current?.focus({preventScroll:!0})}),onPointerDownOutside:e=>e.preventDefault(),onInteractOutside:e=>e.preventDefault(),children:[(0,u.jsx)(w,{children:o}),(0,u.jsx)(j,{contentRef:d})]})})})});x.displayName=y;var S="AlertDialogTitle",O=r.forwardRef((e,t)=>{let{__scopeAlertDialog:n,...r}=e,o=p(n);return(0,u.jsx)(a.Dx,{...o,...r,ref:t})});O.displayName=S;var E="AlertDialogDescription",C=r.forwardRef((e,t)=>{let{__scopeAlertDialog:n,...r}=e,o=p(n);return(0,u.jsx)(a.dk,{...o,...r,ref:t})});C.displayName=E;var R=r.forwardRef((e,t)=>{let{__scopeAlertDialog:n,...r}=e,o=p(n);return(0,u.jsx)(a.x8,{...o,...r,ref:t})});R.displayName="AlertDialogAction";var P="AlertDialogCancel",k=r.forwardRef((e,t)=>{let{__scopeAlertDialog:n,...r}=e,{cancelRef:o}=b(P,n),s=p(n),l=(0,i.e)(t,o);return(0,u.jsx)(a.x8,{...s,...r,ref:l})});k.displayName=P;var j=({contentRef:e})=>{let t=`\`${y}\` requires a description for the component to be accessible for screen reader users.

You can add a description to the \`${y}\` by passing a \`${E}\` component as a child, which also benefits sighted users by adding visible context to the dialog.

Alternatively, you can use your own component as a description by assigning it an \`id\` and passing the same value to the \`aria-describedby\` prop in \`${y}\`. If the description is confusing or duplicative for sighted users, you can use the \`@radix-ui/react-visually-hidden\` primitive as a wrapper around your description component.

For more information, see https://radix-ui.com/primitives/docs/components/alert-dialog`;return r.useEffect(()=>{document.getElementById(e.current?.getAttribute("aria-describedby"))||console.warn(t)},[t,e]),null},T=h,L=g,M=m,N=x,D=R,I=k,A=O,F=C},59656:function(e,t,n){"use strict";n.d(t,{F:()=>i,e:()=>a});var r=n(2784);function o(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function i(...e){return t=>{let n=!1,r=e.map(e=>{let r=o(e,t);return n||"function"!=typeof r||(n=!0),r});if(n)return()=>{for(let t=0;t<r.length;t++){let n=r[t];"function"==typeof n?n():o(e[t],null)}}}}function a(...e){return r.useCallback(i(...e),e)}},8210:function(e,t,n){"use strict";n.d(t,{b:()=>a,k:()=>i});var r=n(2784),o=n(52322);function i(e,t){let n=r.createContext(t),i=e=>{let{children:t,...i}=e,a=r.useMemo(()=>i,Object.values(i));return(0,o.jsx)(n.Provider,{value:a,children:t})};return i.displayName=e+"Provider",[i,function(o){let i=r.useContext(n);if(i)return i;if(void 0!==t)return t;throw Error(`\`${o}\` must be used within \`${e}\``)}]}function a(e,t=[]){let n=[],i=()=>{let t=n.map(e=>r.createContext(e));return function(n){let o=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:o}}),[n,o])}};return i.scopeName=e,[function(t,i){let a=r.createContext(i),s=n.length;n=[...n,i];let l=t=>{let{scope:n,children:i,...l}=t,u=n?.[e]?.[s]||a,c=r.useMemo(()=>l,Object.values(l));return(0,o.jsx)(u.Provider,{value:c,children:i})};return l.displayName=t+"Provider",[l,function(n,o){let l=o?.[e]?.[s]||a,u=r.useContext(l);if(u)return u;if(void 0!==i)return i;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return n.scopeName=t.scopeName,n}(i,...t)]}},78364:function(e,t,n){"use strict";n.d(t,{Dx:()=>er,VY:()=>en,aV:()=>et,dk:()=>eo,fC:()=>Z,h_:()=>ee,jm:()=>X,p8:()=>x,x8:()=>ei,xz:()=>Q});var r=n(2784),o=n(56137),i=n(59656),a=n(8210),s=n(34890),l=n(69153),u=n(12536),c=n(65340),d=n(55827),f=n(42951),p=n(3436),h=n(70653),g=n(82577),m=n(49732),y=n(33883),v=n(52322),b="Dialog",[w,x]=(0,a.b)(b),[S,O]=w(b),E=e=>{let{__scopeDialog:t,children:n,open:o,defaultOpen:i,onOpenChange:a,modal:u=!0}=e,c=r.useRef(null),d=r.useRef(null),[f,p]=(0,l.T)({prop:o,defaultProp:i??!1,onChange:a,caller:b});return(0,v.jsx)(S,{scope:t,triggerRef:c,contentRef:d,contentId:(0,s.M)(),titleId:(0,s.M)(),descriptionId:(0,s.M)(),open:f,onOpenChange:p,onOpenToggle:r.useCallback(()=>p(e=>!e),[p]),modal:u,children:n})};E.displayName=b;var C="DialogTrigger",R=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,a=O(C,n),s=(0,i.e)(t,a.triggerRef);return(0,v.jsx)(p.WV.button,{type:"button","aria-haspopup":"dialog","aria-expanded":a.open,"aria-controls":a.contentId,"data-state":K(a.open),...r,ref:s,onClick:(0,o.M)(e.onClick,a.onOpenToggle)})});R.displayName=C;var P="DialogPortal",[k,j]=w(P,{forceMount:void 0}),T=e=>{let{__scopeDialog:t,forceMount:n,children:o,container:i}=e,a=O(P,t);return(0,v.jsx)(k,{scope:t,forceMount:n,children:r.Children.map(o,e=>(0,v.jsx)(f.z,{present:n||a.open,children:(0,v.jsx)(d.h,{asChild:!0,container:i,children:e})}))})};T.displayName=P;var L="DialogOverlay",M=r.forwardRef((e,t)=>{let n=j(L,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,i=O(L,e.__scopeDialog);return i.modal?(0,v.jsx)(f.z,{present:r||i.open,children:(0,v.jsx)(D,{...o,ref:t})}):null});M.displayName=L;var N=(0,y.Z8)("DialogOverlay.RemoveScroll"),D=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=O(L,n);return(0,v.jsx)(g.Z,{as:N,allowPinchZoom:!0,shards:[o.contentRef],children:(0,v.jsx)(p.WV.div,{"data-state":K(o.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),I="DialogContent",A=r.forwardRef((e,t)=>{let n=j(I,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,i=O(I,e.__scopeDialog);return(0,v.jsx)(f.z,{present:r||i.open,children:i.modal?(0,v.jsx)(F,{...o,ref:t}):(0,v.jsx)($,{...o,ref:t})})});A.displayName=I;var F=r.forwardRef((e,t)=>{let n=O(I,e.__scopeDialog),a=r.useRef(null),s=(0,i.e)(t,n.contentRef,a);return r.useEffect(()=>{let e=a.current;if(e)return(0,m.Ry)(e)},[]),(0,v.jsx)(_,{...e,ref:s,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.M)(e.onCloseAutoFocus,e=>{e.preventDefault(),n.triggerRef.current?.focus()}),onPointerDownOutside:(0,o.M)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()}),onFocusOutside:(0,o.M)(e.onFocusOutside,e=>e.preventDefault())})}),$=r.forwardRef((e,t)=>{let n=O(I,e.__scopeDialog),o=r.useRef(!1),i=r.useRef(!1);return(0,v.jsx)(_,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{e.onCloseAutoFocus?.(t),t.defaultPrevented||(o.current||n.triggerRef.current?.focus(),t.preventDefault()),o.current=!1,i.current=!1},onInteractOutside:t=>{e.onInteractOutside?.(t),t.defaultPrevented||(o.current=!0,"pointerdown"===t.detail.originalEvent.type&&(i.current=!0));let r=t.target;n.triggerRef.current?.contains(r)&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&i.current&&t.preventDefault()}})}),_=r.forwardRef((e,t)=>{let{__scopeDialog:n,trapFocus:o,onOpenAutoFocus:a,onCloseAutoFocus:s,...l}=e,d=O(I,n),f=r.useRef(null),p=(0,i.e)(t,f);return(0,h.EW)(),(0,v.jsxs)(v.Fragment,{children:[(0,v.jsx)(c.M,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:a,onUnmountAutoFocus:s,children:(0,v.jsx)(u.XB,{role:"dialog",id:d.contentId,"aria-describedby":d.descriptionId,"aria-labelledby":d.titleId,"data-state":K(d.open),...l,ref:p,onDismiss:()=>d.onOpenChange(!1)})}),(0,v.jsxs)(v.Fragment,{children:[(0,v.jsx)(J,{titleId:d.titleId}),(0,v.jsx)(G,{contentRef:f,descriptionId:d.descriptionId})]})]})}),W="DialogTitle",V=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=O(W,n);return(0,v.jsx)(p.WV.h2,{id:o.titleId,...r,ref:t})});V.displayName=W;var H="DialogDescription",B=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=O(H,n);return(0,v.jsx)(p.WV.p,{id:o.descriptionId,...r,ref:t})});B.displayName=H;var z="DialogClose",U=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,i=O(z,n);return(0,v.jsx)(p.WV.button,{type:"button",...r,ref:t,onClick:(0,o.M)(e.onClick,()=>i.onOpenChange(!1))})});function K(e){return e?"open":"closed"}U.displayName=z;var q="DialogTitleWarning",[X,Y]=(0,a.k)(q,{contentName:I,titleName:W,docsSlug:"dialog"}),J=({titleId:e})=>{let t=Y(q),n=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return r.useEffect(()=>{e&&(document.getElementById(e)||console.error(n))},[n,e]),null},G=({contentRef:e,descriptionId:t})=>{let n=Y("DialogDescriptionWarning"),o=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${n.contentName}}.`;return r.useEffect(()=>{let n=e.current?.getAttribute("aria-describedby");t&&n&&(document.getElementById(t)||console.warn(o))},[o,e,t]),null},Z=E,Q=R,ee=T,et=M,en=A,er=V,eo=B,ei=U},12536:function(e,t,n){"use strict";n.d(t,{XB:()=>f});var r,o=n(2784),i=n(56137),a=n(3436),s=n(59656),l=n(64148),u=n(52322),c="dismissableLayer.update",d=o.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),f=o.forwardRef((e,t)=>{let{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:f,onPointerDownOutside:g,onFocusOutside:m,onInteractOutside:y,onDismiss:v,...b}=e,w=o.useContext(d),[x,S]=o.useState(null),O=x?.ownerDocument??globalThis?.document,[,E]=o.useState({}),C=(0,s.e)(t,e=>S(e)),R=Array.from(w.layers),[P]=[...w.layersWithOutsidePointerEventsDisabled].slice(-1),k=R.indexOf(P),j=x?R.indexOf(x):-1,T=w.layersWithOutsidePointerEventsDisabled.size>0,L=j>=k,M=function(e,t=globalThis?.document){let n=(0,l.W)(e),r=o.useRef(!1),i=o.useRef(()=>{});return o.useEffect(()=>{let e=e=>{if(e.target&&!r.current){let r=function(){h("dismissableLayer.pointerDownOutside",n,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(t.removeEventListener("click",i.current),i.current=r,t.addEventListener("click",i.current,{once:!0})):r()}else t.removeEventListener("click",i.current);r.current=!1},o=window.setTimeout(()=>{t.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(o),t.removeEventListener("pointerdown",e),t.removeEventListener("click",i.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}(e=>{let t=e.target,n=[...w.branches].some(e=>e.contains(t));L&&!n&&(g?.(e),y?.(e),e.defaultPrevented||v?.())},O),N=function(e,t=globalThis?.document){let n=(0,l.W)(e),r=o.useRef(!1);return o.useEffect(()=>{let e=e=>{e.target&&!r.current&&h("dismissableLayer.focusOutside",n,{originalEvent:e},{discrete:!1})};return t.addEventListener("focusin",e),()=>t.removeEventListener("focusin",e)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}(e=>{let t=e.target;![...w.branches].some(e=>e.contains(t))&&(m?.(e),y?.(e),e.defaultPrevented||v?.())},O);return!function(e,t=globalThis?.document){let n=(0,l.W)(e);o.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{j===w.layers.size-1&&(f?.(e),!e.defaultPrevented&&v&&(e.preventDefault(),v()))},O),o.useEffect(()=>{if(x)return n&&(0===w.layersWithOutsidePointerEventsDisabled.size&&(r=O.body.style.pointerEvents,O.body.style.pointerEvents="none"),w.layersWithOutsidePointerEventsDisabled.add(x)),w.layers.add(x),p(),()=>{n&&1===w.layersWithOutsidePointerEventsDisabled.size&&(O.body.style.pointerEvents=r)}},[x,O,n,w]),o.useEffect(()=>()=>{x&&(w.layers.delete(x),w.layersWithOutsidePointerEventsDisabled.delete(x),p())},[x,w]),o.useEffect(()=>{let e=()=>E({});return document.addEventListener(c,e),()=>document.removeEventListener(c,e)},[]),(0,u.jsx)(a.WV.div,{...b,ref:C,style:{pointerEvents:T?L?"auto":"none":void 0,...e.style},onFocusCapture:(0,i.M)(e.onFocusCapture,N.onFocusCapture),onBlurCapture:(0,i.M)(e.onBlurCapture,N.onBlurCapture),onPointerDownCapture:(0,i.M)(e.onPointerDownCapture,M.onPointerDownCapture)})});function p(){let e=new CustomEvent(c);document.dispatchEvent(e)}function h(e,t,n,{discrete:r}){let o=n.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?(0,a.jH)(o,i):o.dispatchEvent(i)}f.displayName="DismissableLayer",o.forwardRef((e,t)=>{let n=o.useContext(d),r=o.useRef(null),i=(0,s.e)(t,r);return o.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,u.jsx)(a.WV.div,{...e,ref:i})}).displayName="DismissableLayerBranch"},74407:function(e,t,n){"use strict";n.d(t,{tu:()=>nF,fC:()=>nj,Tr:()=>nI,VY:()=>nM,xz:()=>nT,ck:()=>nN,Z0:()=>nD,fF:()=>nA,Uv:()=>nL});var r=n(2784),o=n(56137),i=n(59656),a=n(8210),s=n(69153),l=n(3436),u=n(33883),c=n(52322);function d(e){let t=e+"CollectionProvider",[n,o]=(0,a.b)(t),[s,l]=n(t,{collectionRef:{current:null},itemMap:new Map}),d=e=>{let{scope:t,children:n}=e,o=r.useRef(null),i=r.useRef(new Map).current;return(0,c.jsx)(s,{scope:t,itemMap:i,collectionRef:o,children:n})};d.displayName=t;let f=e+"CollectionSlot",p=(0,u.Z8)(f),h=r.forwardRef((e,t)=>{let{scope:n,children:r}=e,o=l(f,n),a=(0,i.e)(t,o.collectionRef);return(0,c.jsx)(p,{ref:a,children:r})});h.displayName=f;let g=e+"CollectionItemSlot",m="data-radix-collection-item",y=(0,u.Z8)(g),v=r.forwardRef((e,t)=>{let{scope:n,children:o,...a}=e,s=r.useRef(null),u=(0,i.e)(t,s),d=l(g,n);return r.useEffect(()=>(d.itemMap.set(s,{ref:s,...a}),()=>void d.itemMap.delete(s))),(0,c.jsx)(y,{...{[m]:""},ref:u,children:o})});return v.displayName=g,[{Provider:d,Slot:h,ItemSlot:v},function(t){let n=l(e+"CollectionConsumer",t);return r.useCallback(()=>{let e=n.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll(`[${m}]`));return Array.from(n.itemMap.values()).sort((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current))},[n.collectionRef,n.itemMap])},o]}var f=r.createContext(void 0);function p(e){let t=r.useContext(f);return e||t||"ltr"}var h=n(12536),g=n(70653),m=n(65340),y=n(34890);let v=["top","right","bottom","left"],b=Math.min,w=Math.max,x=Math.round,S=Math.floor,O=e=>({x:e,y:e}),E={left:"right",right:"left",bottom:"top",top:"bottom"},C={start:"end",end:"start"};function R(e,t){return"function"==typeof e?e(t):e}function P(e){return e.split("-")[0]}function k(e){return e.split("-")[1]}function j(e){return"x"===e?"y":"x"}function T(e){return"y"===e?"height":"width"}function L(e){return["top","bottom"].includes(P(e))?"y":"x"}function M(e){return e.replace(/start|end/g,e=>C[e])}function N(e){return e.replace(/left|right|bottom|top/g,e=>E[e])}function D(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function I(e){let{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function A(e,t,n){let r,{reference:o,floating:i}=e,a=L(t),s=j(L(t)),l=T(s),u=P(t),c="y"===a,d=o.x+o.width/2-i.width/2,f=o.y+o.height/2-i.height/2,p=o[l]/2-i[l]/2;switch(u){case"top":r={x:d,y:o.y-i.height};break;case"bottom":r={x:d,y:o.y+o.height};break;case"right":r={x:o.x+o.width,y:f};break;case"left":r={x:o.x-i.width,y:f};break;default:r={x:o.x,y:o.y}}switch(k(t)){case"start":r[s]-=p*(n&&c?-1:1);break;case"end":r[s]+=p*(n&&c?-1:1)}return r}let F=async(e,t,n)=>{let{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:a}=n,s=i.filter(Boolean),l=await (null==a.isRTL?void 0:a.isRTL(t)),u=await a.getElementRects({reference:e,floating:t,strategy:o}),{x:c,y:d}=A(u,r,l),f=r,p={},h=0;for(let n=0;n<s.length;n++){let{name:i,fn:g}=s[n],{x:m,y:y,data:v,reset:b}=await g({x:c,y:d,initialPlacement:r,placement:f,strategy:o,middlewareData:p,rects:u,platform:a,elements:{reference:e,floating:t}});c=null!=m?m:c,d=null!=y?y:d,p={...p,[i]:{...p[i],...v}},b&&h<=50&&(h++,"object"==typeof b&&(b.placement&&(f=b.placement),b.rects&&(u=!0===b.rects?await a.getElementRects({reference:e,floating:t,strategy:o}):b.rects),{x:c,y:d}=A(u,f,l)),n=-1)}return{x:c,y:d,placement:f,strategy:o,middlewareData:p}};async function $(e,t){var n;void 0===t&&(t={});let{x:r,y:o,platform:i,rects:a,elements:s,strategy:l}=e,{boundary:u="clippingAncestors",rootBoundary:c="viewport",elementContext:d="floating",altBoundary:f=!1,padding:p=0}=R(t,e),h=D(p),g=s[f?"floating"===d?"reference":"floating":d],m=I(await i.getClippingRect({element:null==(n=await (null==i.isElement?void 0:i.isElement(g)))||n?g:g.contextElement||await (null==i.getDocumentElement?void 0:i.getDocumentElement(s.floating)),boundary:u,rootBoundary:c,strategy:l})),y="floating"===d?{x:r,y:o,width:a.floating.width,height:a.floating.height}:a.reference,v=await (null==i.getOffsetParent?void 0:i.getOffsetParent(s.floating)),b=await (null==i.isElement?void 0:i.isElement(v))&&await (null==i.getScale?void 0:i.getScale(v))||{x:1,y:1},w=I(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:s,rect:y,offsetParent:v,strategy:l}):y);return{top:(m.top-w.top+h.top)/b.y,bottom:(w.bottom-m.bottom+h.bottom)/b.y,left:(m.left-w.left+h.left)/b.x,right:(w.right-m.right+h.right)/b.x}}function _(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function W(e){return v.some(t=>e[t]>=0)}async function V(e,t){let{placement:n,platform:r,elements:o}=e,i=await (null==r.isRTL?void 0:r.isRTL(o.floating)),a=P(n),s=k(n),l="y"===L(n),u=["left","top"].includes(a)?-1:1,c=i&&l?-1:1,d=R(t,e),{mainAxis:f,crossAxis:p,alignmentAxis:h}="number"==typeof d?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return s&&"number"==typeof h&&(p="end"===s?-1*h:h),l?{x:p*c,y:f*u}:{x:f*u,y:p*c}}function H(){return"undefined"!=typeof window}function B(e){return K(e)?(e.nodeName||"").toLowerCase():"#document"}function z(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function U(e){var t;return null==(t=(K(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function K(e){return!!H()&&(e instanceof Node||e instanceof z(e).Node)}function q(e){return!!H()&&(e instanceof Element||e instanceof z(e).Element)}function X(e){return!!H()&&(e instanceof HTMLElement||e instanceof z(e).HTMLElement)}function Y(e){return!!H()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof z(e).ShadowRoot)}function J(e){let{overflow:t,overflowX:n,overflowY:r,display:o}=et(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(o)}function G(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch(e){return!1}})}function Z(e){let t=Q(),n=q(e)?et(e):e;return["transform","translate","scale","rotate","perspective"].some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||["transform","translate","scale","rotate","perspective","filter"].some(e=>(n.willChange||"").includes(e))||["paint","layout","strict","content"].some(e=>(n.contain||"").includes(e))}function Q(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function ee(e){return["html","body","#document"].includes(B(e))}function et(e){return z(e).getComputedStyle(e)}function en(e){return q(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function er(e){if("html"===B(e))return e;let t=e.assignedSlot||e.parentNode||Y(e)&&e.host||U(e);return Y(t)?t.host:t}function eo(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let o=function e(t){let n=er(t);return ee(n)?t.ownerDocument?t.ownerDocument.body:t.body:X(n)&&J(n)?n:e(n)}(e),i=o===(null==(r=e.ownerDocument)?void 0:r.body),a=z(o);if(i){let e=ei(a);return t.concat(a,a.visualViewport||[],J(o)?o:[],e&&n?eo(e):[])}return t.concat(o,eo(o,[],n))}function ei(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function ea(e){let t=et(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,o=X(e),i=o?e.offsetWidth:n,a=o?e.offsetHeight:r,s=x(n)!==i||x(r)!==a;return s&&(n=i,r=a),{width:n,height:r,$:s}}function es(e){return q(e)?e:e.contextElement}function el(e){let t=es(e);if(!X(t))return O(1);let n=t.getBoundingClientRect(),{width:r,height:o,$:i}=ea(t),a=(i?x(n.width):n.width)/r,s=(i?x(n.height):n.height)/o;return a&&Number.isFinite(a)||(a=1),s&&Number.isFinite(s)||(s=1),{x:a,y:s}}let eu=O(0);function ec(e){let t=z(e);return Q()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:eu}function ed(e,t,n,r){var o;void 0===t&&(t=!1),void 0===n&&(n=!1);let i=e.getBoundingClientRect(),a=es(e),s=O(1);t&&(r?q(r)&&(s=el(r)):s=el(e));let l=(void 0===(o=n)&&(o=!1),r&&(!o||r===z(a))&&o)?ec(a):O(0),u=(i.left+l.x)/s.x,c=(i.top+l.y)/s.y,d=i.width/s.x,f=i.height/s.y;if(a){let e=z(a),t=r&&q(r)?z(r):r,n=e,o=ei(n);for(;o&&r&&t!==n;){let e=el(o),t=o.getBoundingClientRect(),r=et(o),i=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,a=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;u*=e.x,c*=e.y,d*=e.x,f*=e.y,u+=i,c+=a,o=ei(n=z(o))}}return I({width:d,height:f,x:u,y:c})}function ef(e,t){let n=en(e).scrollLeft;return t?t.left+n:ed(U(e)).left+n}function ep(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:ef(e,r)),y:r.top+t.scrollTop}}function eh(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=z(e),r=U(e),o=n.visualViewport,i=r.clientWidth,a=r.clientHeight,s=0,l=0;if(o){i=o.width,a=o.height;let e=Q();(!e||e&&"fixed"===t)&&(s=o.offsetLeft,l=o.offsetTop)}return{width:i,height:a,x:s,y:l}}(e,n);else if("document"===t)r=function(e){let t=U(e),n=en(e),r=e.ownerDocument.body,o=w(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=w(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),a=-n.scrollLeft+ef(e),s=-n.scrollTop;return"rtl"===et(r).direction&&(a+=w(t.clientWidth,r.clientWidth)-o),{width:o,height:i,x:a,y:s}}(U(e));else if(q(t))r=function(e,t){let n=ed(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,i=X(e)?el(e):O(1),a=e.clientWidth*i.x,s=e.clientHeight*i.y;return{width:a,height:s,x:o*i.x,y:r*i.y}}(t,n);else{let n=ec(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return I(r)}function eg(e){return"static"===et(e).position}function em(e,t){if(!X(e)||"fixed"===et(e).position)return null;if(t)return t(e);let n=e.offsetParent;return U(e)===n&&(n=n.ownerDocument.body),n}function ey(e,t){let n=z(e);if(G(e))return n;if(!X(e)){let t=er(e);for(;t&&!ee(t);){if(q(t)&&!eg(t))return t;t=er(t)}return n}let r=em(e,t);for(;r&&["table","td","th"].includes(B(r))&&eg(r);)r=em(r,t);return r&&ee(r)&&eg(r)&&!Z(r)?n:r||function(e){let t=er(e);for(;X(t)&&!ee(t);){if(Z(t))return t;if(G(t))break;t=er(t)}return null}(e)||n}let ev=async function(e){let t=this.getOffsetParent||ey,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=X(t),o=U(t),i="fixed"===n,a=ed(e,!0,i,t),s={scrollLeft:0,scrollTop:0},l=O(0);if(r||!r&&!i)if(("body"!==B(t)||J(o))&&(s=en(t)),r){let e=ed(t,!0,i,t);l.x=e.x+t.clientLeft,l.y=e.y+t.clientTop}else o&&(l.x=ef(o));let u=!o||r||i?O(0):ep(o,s);return{x:a.left+s.scrollLeft-l.x-u.x,y:a.top+s.scrollTop-l.y-u.y,width:a.width,height:a.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},eb={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e,i="fixed"===o,a=U(r),s=!!t&&G(t.floating);if(r===a||s&&i)return n;let l={scrollLeft:0,scrollTop:0},u=O(1),c=O(0),d=X(r);if((d||!d&&!i)&&(("body"!==B(r)||J(a))&&(l=en(r)),X(r))){let e=ed(r);u=el(r),c.x=e.x+r.clientLeft,c.y=e.y+r.clientTop}let f=!a||d||i?O(0):ep(a,l,!0);return{width:n.width*u.x,height:n.height*u.y,x:n.x*u.x-l.scrollLeft*u.x+c.x+f.x,y:n.y*u.y-l.scrollTop*u.y+c.y+f.y}},getDocumentElement:U,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e,i=[..."clippingAncestors"===n?G(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=eo(e,[],!1).filter(e=>q(e)&&"body"!==B(e)),o=null,i="fixed"===et(e).position,a=i?er(e):e;for(;q(a)&&!ee(a);){let t=et(a),n=Z(a);n||"fixed"!==t.position||(o=null),(i?!n&&!o:!n&&"static"===t.position&&!!o&&["absolute","fixed"].includes(o.position)||J(a)&&!n&&function e(t,n){let r=er(t);return!(r===n||!q(r)||ee(r))&&("fixed"===et(r).position||e(r,n))}(e,a))?r=r.filter(e=>e!==a):o=t,a=er(a)}return t.set(e,r),r}(t,this._c):[].concat(n),r],a=i[0],s=i.reduce((e,n)=>{let r=eh(t,n,o);return e.top=w(r.top,e.top),e.right=b(r.right,e.right),e.bottom=b(r.bottom,e.bottom),e.left=w(r.left,e.left),e},eh(t,a,o));return{width:s.right-s.left,height:s.bottom-s.top,x:s.left,y:s.top}},getOffsetParent:ey,getElementRects:ev,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=ea(e);return{width:t,height:n}},getScale:el,isElement:q,isRTL:function(e){return"rtl"===et(e).direction}};function ew(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let ex=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:o,rects:i,platform:a,elements:s,middlewareData:l}=t,{element:u,padding:c=0}=R(e,t)||{};if(null==u)return{};let d=D(c),f={x:n,y:r},p=j(L(o)),h=T(p),g=await a.getDimensions(u),m="y"===p,y=m?"clientHeight":"clientWidth",v=i.reference[h]+i.reference[p]-f[p]-i.floating[h],x=f[p]-i.reference[p],S=await (null==a.getOffsetParent?void 0:a.getOffsetParent(u)),O=S?S[y]:0;O&&await (null==a.isElement?void 0:a.isElement(S))||(O=s.floating[y]||i.floating[h]);let E=O/2-g[h]/2-1,C=b(d[m?"top":"left"],E),P=b(d[m?"bottom":"right"],E),M=O-g[h]-P,N=O/2-g[h]/2+(v/2-x/2),I=w(C,b(N,M)),A=!l.arrow&&null!=k(o)&&N!==I&&i.reference[h]/2-(N<C?C:P)-g[h]/2<0,F=A?N<C?N-C:N-M:0;return{[p]:f[p]+F,data:{[p]:I,centerOffset:N-I-F,...A&&{alignmentOffset:F}},reset:A}}}),eS=(e,t,n)=>{let r=new Map,o={platform:eb,...n},i={...o.platform,_c:r};return F(e,t,{...o,platform:i})};var eO=n(28316),eE="undefined"!=typeof document?r.useLayoutEffect:r.useEffect;function eC(e,t){let n,r,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!eC(e[r],t[r]))return!1;return!0}if((n=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!=r--;){let n=o[r];if(("_owner"!==n||!e.$$typeof)&&!eC(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function eR(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function eP(e,t){let n=eR(e);return Math.round(t*n)/n}function ek(e){let t=r.useRef(e);return eE(()=>{t.current=e}),t}let ej=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?ex({element:n.current,padding:r}).fn(t):{}:n?ex({element:n,padding:r}).fn(t):{}}}),eT=(e,t)=>{var n;return{...(void 0===(n=e)&&(n=0),{name:"offset",options:n,async fn(e){var t,r;let{x:o,y:i,placement:a,middlewareData:s}=e,l=await V(e,n);return a===(null==(t=s.offset)?void 0:t.placement)&&null!=(r=s.arrow)&&r.alignmentOffset?{}:{x:o+l.x,y:i+l.y,data:{...l,placement:a}}}}),options:[e,t]}},eL=(e,t)=>{var n;return{...(void 0===(n=e)&&(n={}),{name:"shift",options:n,async fn(e){let{x:t,y:r,placement:o}=e,{mainAxis:i=!0,crossAxis:a=!1,limiter:s={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...l}=R(n,e),u={x:t,y:r},c=await $(e,l),d=L(P(o)),f=j(d),p=u[f],h=u[d];if(i){let e="y"===f?"top":"left",t="y"===f?"bottom":"right",n=p+c[e],r=p-c[t];p=w(n,b(p,r))}if(a){let e="y"===d?"top":"left",t="y"===d?"bottom":"right",n=h+c[e],r=h-c[t];h=w(n,b(h,r))}let g=s.fn({...e,[f]:p,[d]:h});return{...g,data:{x:g.x-t,y:g.y-r,enabled:{[f]:i,[d]:a}}}}}),options:[e,t]}},eM=(e,t)=>{var n;return{...(void 0===(n=e)&&(n={}),{options:n,fn(e){let{x:t,y:r,placement:o,rects:i,middlewareData:a}=e,{offset:s=0,mainAxis:l=!0,crossAxis:u=!0}=R(n,e),c={x:t,y:r},d=L(o),f=j(d),p=c[f],h=c[d],g=R(s,e),m="number"==typeof g?{mainAxis:g,crossAxis:0}:{mainAxis:0,crossAxis:0,...g};if(l){let e="y"===f?"height":"width",t=i.reference[f]-i.floating[e]+m.mainAxis,n=i.reference[f]+i.reference[e]-m.mainAxis;p<t?p=t:p>n&&(p=n)}if(u){var y,v;let e="y"===f?"width":"height",t=["top","left"].includes(P(o)),n=i.reference[d]-i.floating[e]+(t&&(null==(y=a.offset)?void 0:y[d])||0)+(t?0:m.crossAxis),r=i.reference[d]+i.reference[e]+(t?0:(null==(v=a.offset)?void 0:v[d])||0)-(t?m.crossAxis:0);h<n?h=n:h>r&&(h=r)}return{[f]:p,[d]:h}}}),options:[e,t]}},eN=(e,t)=>{var n;return{...(void 0===(n=e)&&(n={}),{name:"flip",options:n,async fn(e){var t,r,o,i,a;let{placement:s,middlewareData:l,rects:u,initialPlacement:c,platform:d,elements:f}=e,{mainAxis:p=!0,crossAxis:h=!0,fallbackPlacements:g,fallbackStrategy:m="bestFit",fallbackAxisSideDirection:y="none",flipAlignment:v=!0,...b}=R(n,e);if(null!=(t=l.arrow)&&t.alignmentOffset)return{};let w=P(s),x=L(c),S=P(c)===c,O=await (null==d.isRTL?void 0:d.isRTL(f.floating)),E=g||(S||!v?[N(c)]:function(e){let t=N(e);return[M(e),t,M(t)]}(c)),C="none"!==y;!g&&C&&E.push(...function(e,t,n,r){let o=k(e),i=function(e,t,n){let r=["left","right"],o=["right","left"];switch(e){case"top":case"bottom":if(n)return t?o:r;return t?r:o;case"left":case"right":return t?["top","bottom"]:["bottom","top"];default:return[]}}(P(e),"start"===n,r);return o&&(i=i.map(e=>e+"-"+o),t&&(i=i.concat(i.map(M)))),i}(c,v,y,O));let D=[c,...E],I=await $(e,b),A=[],F=(null==(r=l.flip)?void 0:r.overflows)||[];if(p&&A.push(I[w]),h){let e=function(e,t,n){void 0===n&&(n=!1);let r=k(e),o=j(L(e)),i=T(o),a="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[i]>t.floating[i]&&(a=N(a)),[a,N(a)]}(s,u,O);A.push(I[e[0]],I[e[1]])}if(F=[...F,{placement:s,overflows:A}],!A.every(e=>e<=0)){let e=((null==(o=l.flip)?void 0:o.index)||0)+1,t=D[e];if(t)return{data:{index:e,overflows:F},reset:{placement:t}};let n=null==(i=F.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:i.placement;if(!n)switch(m){case"bestFit":{let e=null==(a=F.filter(e=>{if(C){let t=L(e.placement);return t===x||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:a[0];e&&(n=e);break}case"initialPlacement":n=c}if(s!==n)return{reset:{placement:n}}}return{}}}),options:[e,t]}},eD=(e,t)=>{var n;return{...(void 0===(n=e)&&(n={}),{name:"size",options:n,async fn(e){var t,r;let o,i,{placement:a,rects:s,platform:l,elements:u}=e,{apply:c=()=>{},...d}=R(n,e),f=await $(e,d),p=P(a),h=k(a),g="y"===L(a),{width:m,height:y}=s.floating;"top"===p||"bottom"===p?(o=p,i=h===(await (null==l.isRTL?void 0:l.isRTL(u.floating))?"start":"end")?"left":"right"):(i=p,o="end"===h?"top":"bottom");let v=y-f.top-f.bottom,x=m-f.left-f.right,S=b(y-f[o],v),O=b(m-f[i],x),E=!e.middlewareData.shift,C=S,j=O;if(null!=(t=e.middlewareData.shift)&&t.enabled.x&&(j=x),null!=(r=e.middlewareData.shift)&&r.enabled.y&&(C=v),E&&!h){let e=w(f.left,0),t=w(f.right,0),n=w(f.top,0),r=w(f.bottom,0);g?j=m-2*(0!==e||0!==t?e+t:w(f.left,f.right)):C=y-2*(0!==n||0!==r?n+r:w(f.top,f.bottom))}await c({...e,availableWidth:j,availableHeight:C});let T=await l.getDimensions(u.floating);return m!==T.width||y!==T.height?{reset:{rects:!0}}:{}}}),options:[e,t]}},eI=(e,t)=>{var n;return{...(void 0===(n=e)&&(n={}),{name:"hide",options:n,async fn(e){let{rects:t}=e,{strategy:r="referenceHidden",...o}=R(n,e);switch(r){case"referenceHidden":{let n=_(await $(e,{...o,elementContext:"reference"}),t.reference);return{data:{referenceHiddenOffsets:n,referenceHidden:W(n)}}}case"escaped":{let n=_(await $(e,{...o,altBoundary:!0}),t.floating);return{data:{escapedOffsets:n,escaped:W(n)}}}default:return{}}}}),options:[e,t]}},eA=(e,t)=>({...ej(e),options:[e,t]});var eF=r.forwardRef((e,t)=>{let{children:n,width:r=10,height:o=5,...i}=e;return(0,c.jsx)(l.WV.svg,{...i,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,c.jsx)("polygon",{points:"0,0 30,0 15,10"})})});eF.displayName="Arrow";var e$=n(64148),e_=n(34896),eW=n(65534),eV="Popper",[eH,eB]=(0,a.b)(eV),[ez,eU]=eH(eV),eK=e=>{let{__scopePopper:t,children:n}=e,[o,i]=r.useState(null);return(0,c.jsx)(ez,{scope:t,anchor:o,onAnchorChange:i,children:n})};eK.displayName=eV;var eq="PopperAnchor",eX=r.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:o,...a}=e,s=eU(eq,n),u=r.useRef(null),d=(0,i.e)(t,u);return r.useEffect(()=>{s.onAnchorChange(o?.current||u.current)}),o?null:(0,c.jsx)(l.WV.div,{...a,ref:d})});eX.displayName=eq;var eY="PopperContent",[eJ,eG]=eH(eY),eZ=r.forwardRef((e,t)=>{let{__scopePopper:n,side:o="bottom",sideOffset:a=0,align:s="center",alignOffset:u=0,arrowPadding:d=0,avoidCollisions:f=!0,collisionBoundary:p=[],collisionPadding:h=0,sticky:g="partial",hideWhenDetached:m=!1,updatePositionStrategy:y="optimized",onPlaced:v,...x}=e,O=eU(eY,n),[E,C]=r.useState(null),R=(0,i.e)(t,e=>C(e)),[P,k]=r.useState(null),j=(0,eW.t)(P),T=j?.width??0,L=j?.height??0,M="number"==typeof h?h:{top:0,right:0,bottom:0,left:0,...h},N=Array.isArray(p)?p:[p],D=N.length>0,I={padding:M,boundary:N.filter(e2),altBoundary:D},{refs:A,floatingStyles:F,placement:$,isPositioned:_,middlewareData:W}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:o=[],platform:i,elements:{reference:a,floating:s}={},transform:l=!0,whileElementsMounted:u,open:c}=e,[d,f]=r.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[p,h]=r.useState(o);eC(p,o)||h(o);let[g,m]=r.useState(null),[y,v]=r.useState(null),b=r.useCallback(e=>{e!==O.current&&(O.current=e,m(e))},[]),w=r.useCallback(e=>{e!==E.current&&(E.current=e,v(e))},[]),x=a||g,S=s||y,O=r.useRef(null),E=r.useRef(null),C=r.useRef(d),R=null!=u,P=ek(u),k=ek(i),j=ek(c),T=r.useCallback(()=>{if(!O.current||!E.current)return;let e={placement:t,strategy:n,middleware:p};k.current&&(e.platform=k.current),eS(O.current,E.current,e).then(e=>{let t={...e,isPositioned:!1!==j.current};L.current&&!eC(C.current,t)&&(C.current=t,eO.flushSync(()=>{f(t)}))})},[p,t,n,k,j]);eE(()=>{!1===c&&C.current.isPositioned&&(C.current.isPositioned=!1,f(e=>({...e,isPositioned:!1})))},[c]);let L=r.useRef(!1);eE(()=>(L.current=!0,()=>{L.current=!1}),[]),eE(()=>{if(x&&(O.current=x),S&&(E.current=S),x&&S){if(P.current)return P.current(x,S,T);T()}},[x,S,T,P,R]);let M=r.useMemo(()=>({reference:O,floating:E,setReference:b,setFloating:w}),[b,w]),N=r.useMemo(()=>({reference:x,floating:S}),[x,S]),D=r.useMemo(()=>{let e={position:n,left:0,top:0};if(!N.floating)return e;let t=eP(N.floating,d.x),r=eP(N.floating,d.y);return l?{...e,transform:"translate("+t+"px, "+r+"px)",...eR(N.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,l,N.floating,d.x,d.y]);return r.useMemo(()=>({...d,update:T,refs:M,elements:N,floatingStyles:D}),[d,T,M,N,D])}({strategy:"fixed",placement:o+("center"!==s?"-"+s:""),whileElementsMounted:(...e)=>(function(e,t,n,r){let o;void 0===r&&(r={});let{ancestorScroll:i=!0,ancestorResize:a=!0,elementResize:s="function"==typeof ResizeObserver,layoutShift:l="function"==typeof IntersectionObserver,animationFrame:u=!1}=r,c=es(e),d=i||a?[...c?eo(c):[],...eo(t)]:[];d.forEach(e=>{i&&e.addEventListener("scroll",n,{passive:!0}),a&&e.addEventListener("resize",n)});let f=c&&l?function(e,t){let n,r=null,o=U(e);function i(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return!function a(s,l){void 0===s&&(s=!1),void 0===l&&(l=1),i();let u=e.getBoundingClientRect(),{left:c,top:d,width:f,height:p}=u;if(s||t(),!f||!p)return;let h=S(d),g=S(o.clientWidth-(c+f)),m={rootMargin:-h+"px "+-g+"px "+-S(o.clientHeight-(d+p))+"px "+-S(c)+"px",threshold:w(0,b(1,l))||1},y=!0;function v(t){let r=t[0].intersectionRatio;if(r!==l){if(!y)return a();r?a(!1,r):n=setTimeout(()=>{a(!1,1e-7)},1e3)}1!==r||ew(u,e.getBoundingClientRect())||a(),y=!1}try{r=new IntersectionObserver(v,{...m,root:o.ownerDocument})}catch(e){r=new IntersectionObserver(v,m)}r.observe(e)}(!0),i}(c,n):null,p=-1,h=null;s&&(h=new ResizeObserver(e=>{let[r]=e;r&&r.target===c&&h&&(h.unobserve(t),cancelAnimationFrame(p),p=requestAnimationFrame(()=>{var e;null==(e=h)||e.observe(t)})),n()}),c&&!u&&h.observe(c),h.observe(t));let g=u?ed(e):null;return u&&function t(){let r=ed(e);g&&!ew(g,r)&&n(),g=r,o=requestAnimationFrame(t)}(),n(),()=>{var e;d.forEach(e=>{i&&e.removeEventListener("scroll",n),a&&e.removeEventListener("resize",n)}),null==f||f(),null==(e=h)||e.disconnect(),h=null,u&&cancelAnimationFrame(o)}})(...e,{animationFrame:"always"===y}),elements:{reference:O.anchor},middleware:[eT({mainAxis:a+L,alignmentAxis:u}),f&&eL({mainAxis:!0,crossAxis:!1,limiter:"partial"===g?eM():void 0,...I}),f&&eN({...I}),eD({...I,apply:({elements:e,rects:t,availableWidth:n,availableHeight:r})=>{let{width:o,height:i}=t.reference,a=e.floating.style;a.setProperty("--radix-popper-available-width",`${n}px`),a.setProperty("--radix-popper-available-height",`${r}px`),a.setProperty("--radix-popper-anchor-width",`${o}px`),a.setProperty("--radix-popper-anchor-height",`${i}px`)}}),P&&eA({element:P,padding:d}),e8({arrowWidth:T,arrowHeight:L}),m&&eI({strategy:"referenceHidden",...I})]}),[V,H]=e3($),B=(0,e$.W)(v);(0,e_.b)(()=>{_&&B?.()},[_,B]);let z=W.arrow?.x,K=W.arrow?.y,q=W.arrow?.centerOffset!==0,[X,Y]=r.useState();return(0,e_.b)(()=>{E&&Y(window.getComputedStyle(E).zIndex)},[E]),(0,c.jsx)("div",{ref:A.setFloating,"data-radix-popper-content-wrapper":"",style:{...F,transform:_?F.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:X,"--radix-popper-transform-origin":[W.transformOrigin?.x,W.transformOrigin?.y].join(" "),...W.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,c.jsx)(eJ,{scope:n,placedSide:V,onArrowChange:k,arrowX:z,arrowY:K,shouldHideArrow:q,children:(0,c.jsx)(l.WV.div,{"data-side":V,"data-align":H,...x,ref:R,style:{...x.style,animation:_?void 0:"none"}})})})});eZ.displayName=eY;var eQ="PopperArrow",e0={top:"bottom",right:"left",bottom:"top",left:"right"},e1=r.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,o=eG(eQ,n),i=e0[o.placedSide];return(0,c.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,c.jsx)(eF,{...r,ref:t,style:{...r.style,display:"block"}})})});function e2(e){return null!==e}e1.displayName=eQ;var e8=e=>({name:"transformOrigin",options:e,fn(t){let{placement:n,rects:r,middlewareData:o}=t,i=o.arrow?.centerOffset!==0,a=i?0:e.arrowWidth,s=i?0:e.arrowHeight,[l,u]=e3(n),c={start:"0%",center:"50%",end:"100%"}[u],d=(o.arrow?.x??0)+a/2,f=(o.arrow?.y??0)+s/2,p="",h="";return"bottom"===l?(p=i?c:`${d}px`,h=`${-s}px`):"top"===l?(p=i?c:`${d}px`,h=`${r.floating.height+s}px`):"right"===l?(p=`${-s}px`,h=i?c:`${f}px`):"left"===l&&(p=`${r.floating.width+s}px`,h=i?c:`${f}px`),{data:{x:p,y:h}}}});function e3(e){let[t,n="center"]=e.split("-");return[t,n]}var e6=n(55827),e4=n(42951),e5="rovingFocusGroup.onEntryFocus",e7={bubbles:!1,cancelable:!0},e9="RovingFocusGroup",[te,tt,tn]=d(e9),[tr,to]=(0,a.b)(e9,[tn]),[ti,ta]=tr(e9),ts=r.forwardRef((e,t)=>(0,c.jsx)(te.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,c.jsx)(te.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,c.jsx)(tl,{...e,ref:t})})}));ts.displayName=e9;var tl=r.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,orientation:a,loop:u=!1,dir:d,currentTabStopId:f,defaultCurrentTabStopId:h,onCurrentTabStopIdChange:g,onEntryFocus:m,preventScrollOnEntryFocus:y=!1,...v}=e,b=r.useRef(null),w=(0,i.e)(t,b),x=p(d),[S,O]=(0,s.T)({prop:f,defaultProp:h??null,onChange:g,caller:e9}),[E,C]=r.useState(!1),R=(0,e$.W)(m),P=tt(n),k=r.useRef(!1),[j,T]=r.useState(0);return r.useEffect(()=>{let e=b.current;if(e)return e.addEventListener(e5,R),()=>e.removeEventListener(e5,R)},[R]),(0,c.jsx)(ti,{scope:n,orientation:a,dir:x,loop:u,currentTabStopId:S,onItemFocus:r.useCallback(e=>O(e),[O]),onItemShiftTab:r.useCallback(()=>C(!0),[]),onFocusableItemAdd:r.useCallback(()=>T(e=>e+1),[]),onFocusableItemRemove:r.useCallback(()=>T(e=>e-1),[]),children:(0,c.jsx)(l.WV.div,{tabIndex:E||0===j?-1:0,"data-orientation":a,...v,ref:w,style:{outline:"none",...e.style},onMouseDown:(0,o.M)(e.onMouseDown,()=>{k.current=!0}),onFocus:(0,o.M)(e.onFocus,e=>{let t=!k.current;if(e.target===e.currentTarget&&t&&!E){let t=new CustomEvent(e5,e7);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=P().filter(e=>e.focusable);tf([e.find(e=>e.active),e.find(e=>e.id===S),...e].filter(Boolean).map(e=>e.ref.current),y)}}k.current=!1}),onBlur:(0,o.M)(e.onBlur,()=>C(!1))})})}),tu="RovingFocusGroupItem",tc=r.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,focusable:i=!0,active:a=!1,tabStopId:s,children:u,...d}=e,f=(0,y.M)(),p=s||f,h=ta(tu,n),g=h.currentTabStopId===p,m=tt(n),{onFocusableItemAdd:v,onFocusableItemRemove:b,currentTabStopId:w}=h;return r.useEffect(()=>{if(i)return v(),()=>b()},[i,v,b]),(0,c.jsx)(te.ItemSlot,{scope:n,id:p,focusable:i,active:a,children:(0,c.jsx)(l.WV.span,{tabIndex:g?0:-1,"data-orientation":h.orientation,...d,ref:t,onMouseDown:(0,o.M)(e.onMouseDown,e=>{i?h.onItemFocus(p):e.preventDefault()}),onFocus:(0,o.M)(e.onFocus,()=>h.onItemFocus(p)),onKeyDown:(0,o.M)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void h.onItemShiftTab();if(e.target!==e.currentTarget)return;let t=function(e,t,n){var r;let o=(r=e.key,"rtl"!==n?r:"ArrowLeft"===r?"ArrowRight":"ArrowRight"===r?"ArrowLeft":r);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return td[o]}(e,h.orientation,h.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let o=m().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)o.reverse();else if("prev"===t||"next"===t){var n,r;"prev"===t&&o.reverse();let i=o.indexOf(e.currentTarget);o=h.loop?(n=o,r=i+1,n.map((e,t)=>n[(r+t)%n.length])):o.slice(i+1)}setTimeout(()=>tf(o))}}),children:"function"==typeof u?u({isCurrentTabStop:g,hasTabStop:null!=w}):u})})});tc.displayName=tu;var td={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function tf(e,t=!1){let n=document.activeElement;for(let r of e)if(r===n||(r.focus({preventScroll:t}),document.activeElement!==n))return}var tp=n(49732),th=n(82577),tg=["Enter"," "],tm=["ArrowUp","PageDown","End"],ty=["ArrowDown","PageUp","Home",...tm],tv={ltr:[...tg,"ArrowRight"],rtl:[...tg,"ArrowLeft"]},tb={ltr:["ArrowLeft"],rtl:["ArrowRight"]},tw="Menu",[tx,tS,tO]=d(tw),[tE,tC]=(0,a.b)(tw,[tO,eB,to]),tR=eB(),tP=to(),[tk,tj]=tE(tw),[tT,tL]=tE(tw),tM=e=>{let{__scopeMenu:t,open:n=!1,children:o,dir:i,onOpenChange:a,modal:s=!0}=e,l=tR(t),[u,d]=r.useState(null),f=r.useRef(!1),h=(0,e$.W)(a),g=p(i);return r.useEffect(()=>{let e=()=>{f.current=!0,document.addEventListener("pointerdown",t,{capture:!0,once:!0}),document.addEventListener("pointermove",t,{capture:!0,once:!0})},t=()=>f.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",t,{capture:!0}),document.removeEventListener("pointermove",t,{capture:!0})}},[]),(0,c.jsx)(eK,{...l,children:(0,c.jsx)(tk,{scope:t,open:n,onOpenChange:h,content:u,onContentChange:d,children:(0,c.jsx)(tT,{scope:t,onClose:r.useCallback(()=>h(!1),[h]),isUsingKeyboardRef:f,dir:g,modal:s,children:o})})})};tM.displayName=tw;var tN=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e,o=tR(n);return(0,c.jsx)(eX,{...o,...r,ref:t})});tN.displayName="MenuAnchor";var tD="MenuPortal",[tI,tA]=tE(tD,{forceMount:void 0}),tF=e=>{let{__scopeMenu:t,forceMount:n,children:r,container:o}=e,i=tj(tD,t);return(0,c.jsx)(tI,{scope:t,forceMount:n,children:(0,c.jsx)(e4.z,{present:n||i.open,children:(0,c.jsx)(e6.h,{asChild:!0,container:o,children:r})})})};tF.displayName=tD;var t$="MenuContent",[t_,tW]=tE(t$),tV=r.forwardRef((e,t)=>{let n=tA(t$,e.__scopeMenu),{forceMount:r=n.forceMount,...o}=e,i=tj(t$,e.__scopeMenu),a=tL(t$,e.__scopeMenu);return(0,c.jsx)(tx.Provider,{scope:e.__scopeMenu,children:(0,c.jsx)(e4.z,{present:r||i.open,children:(0,c.jsx)(tx.Slot,{scope:e.__scopeMenu,children:a.modal?(0,c.jsx)(tH,{...o,ref:t}):(0,c.jsx)(tB,{...o,ref:t})})})})}),tH=r.forwardRef((e,t)=>{let n=tj(t$,e.__scopeMenu),a=r.useRef(null),s=(0,i.e)(t,a);return r.useEffect(()=>{let e=a.current;if(e)return(0,tp.Ry)(e)},[]),(0,c.jsx)(tU,{...e,ref:s,trapFocus:n.open,disableOutsidePointerEvents:n.open,disableOutsideScroll:!0,onFocusOutside:(0,o.M)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>n.onOpenChange(!1)})}),tB=r.forwardRef((e,t)=>{let n=tj(t$,e.__scopeMenu);return(0,c.jsx)(tU,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>n.onOpenChange(!1)})}),tz=(0,u.Z8)("MenuContent.ScrollLock"),tU=r.forwardRef((e,t)=>{let{__scopeMenu:n,loop:a=!1,trapFocus:s,onOpenAutoFocus:l,onCloseAutoFocus:u,disableOutsidePointerEvents:d,onEntryFocus:f,onEscapeKeyDown:p,onPointerDownOutside:y,onFocusOutside:v,onInteractOutside:b,onDismiss:w,disableOutsideScroll:x,...S}=e,O=tj(t$,n),E=tL(t$,n),C=tR(n),R=tP(n),P=tS(n),[k,j]=r.useState(null),T=r.useRef(null),L=(0,i.e)(t,T,O.onContentChange),M=r.useRef(0),N=r.useRef(""),D=r.useRef(0),I=r.useRef(null),A=r.useRef("right"),F=r.useRef(0),$=x?th.Z:r.Fragment,_=e=>{let t=N.current+e,n=P().filter(e=>!e.disabled),r=document.activeElement,o=n.find(e=>e.ref.current===r)?.textValue,i=function(e,t,n){var r;let o=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,i=n?e.indexOf(n):-1,a=(r=Math.max(i,0),e.map((t,n)=>e[(r+n)%e.length]));1===o.length&&(a=a.filter(e=>e!==n));let s=a.find(e=>e.toLowerCase().startsWith(o.toLowerCase()));return s!==n?s:void 0}(n.map(e=>e.textValue),t,o),a=n.find(e=>e.textValue===i)?.ref.current;!function e(t){N.current=t,window.clearTimeout(M.current),""!==t&&(M.current=window.setTimeout(()=>e(""),1e3))}(t),a&&setTimeout(()=>a.focus())};r.useEffect(()=>()=>window.clearTimeout(M.current),[]),(0,g.EW)();let W=r.useCallback(e=>{var t,n;return A.current===I.current?.side&&(t=e,!!(n=I.current?.area)&&function(e,t){let{x:n,y:r}=e,o=!1;for(let e=0,i=t.length-1;e<t.length;i=e++){let a=t[e],s=t[i],l=a.x,u=a.y,c=s.x,d=s.y;u>r!=d>r&&n<(c-l)*(r-u)/(d-u)+l&&(o=!o)}return o}({x:t.clientX,y:t.clientY},n))},[]);return(0,c.jsx)(t_,{scope:n,searchRef:N,onItemEnter:r.useCallback(e=>{W(e)&&e.preventDefault()},[W]),onItemLeave:r.useCallback(e=>{W(e)||(T.current?.focus(),j(null))},[W]),onTriggerLeave:r.useCallback(e=>{W(e)&&e.preventDefault()},[W]),pointerGraceTimerRef:D,onPointerGraceIntentChange:r.useCallback(e=>{I.current=e},[]),children:(0,c.jsx)($,{...x?{as:tz,allowPinchZoom:!0}:void 0,children:(0,c.jsx)(m.M,{asChild:!0,trapped:s,onMountAutoFocus:(0,o.M)(l,e=>{e.preventDefault(),T.current?.focus({preventScroll:!0})}),onUnmountAutoFocus:u,children:(0,c.jsx)(h.XB,{asChild:!0,disableOutsidePointerEvents:d,onEscapeKeyDown:p,onPointerDownOutside:y,onFocusOutside:v,onInteractOutside:b,onDismiss:w,children:(0,c.jsx)(ts,{asChild:!0,...R,dir:E.dir,orientation:"vertical",loop:a,currentTabStopId:k,onCurrentTabStopIdChange:j,onEntryFocus:(0,o.M)(f,e=>{E.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,c.jsx)(eZ,{role:"menu","aria-orientation":"vertical","data-state":nu(O.open),"data-radix-menu-content":"",dir:E.dir,...C,...S,ref:L,style:{outline:"none",...S.style},onKeyDown:(0,o.M)(S.onKeyDown,e=>{let t=e.target.closest("[data-radix-menu-content]")===e.currentTarget,n=e.ctrlKey||e.altKey||e.metaKey,r=1===e.key.length;t&&("Tab"===e.key&&e.preventDefault(),!n&&r&&_(e.key));let o=T.current;if(e.target!==o||!ty.includes(e.key))return;e.preventDefault();let i=P().filter(e=>!e.disabled).map(e=>e.ref.current);tm.includes(e.key)&&i.reverse(),function(e){let t=document.activeElement;for(let n of e)if(n===t||(n.focus(),document.activeElement!==t))return}(i)}),onBlur:(0,o.M)(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(M.current),N.current="")}),onPointerMove:(0,o.M)(e.onPointerMove,nf(e=>{let t=e.target,n=F.current!==e.clientX;e.currentTarget.contains(t)&&n&&(A.current=e.clientX>F.current?"right":"left",F.current=e.clientX)}))})})})})})})});tV.displayName=t$;var tK=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,c.jsx)(l.WV.div,{role:"group",...r,ref:t})});tK.displayName="MenuGroup";var tq=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,c.jsx)(l.WV.div,{...r,ref:t})});tq.displayName="MenuLabel";var tX="MenuItem",tY="menu.itemSelect",tJ=r.forwardRef((e,t)=>{let{disabled:n=!1,onSelect:a,...s}=e,u=r.useRef(null),d=tL(tX,e.__scopeMenu),f=tW(tX,e.__scopeMenu),p=(0,i.e)(t,u),h=r.useRef(!1);return(0,c.jsx)(tG,{...s,ref:p,disabled:n,onClick:(0,o.M)(e.onClick,()=>{let e=u.current;if(!n&&e){let t=new CustomEvent(tY,{bubbles:!0,cancelable:!0});e.addEventListener(tY,e=>a?.(e),{once:!0}),(0,l.jH)(e,t),t.defaultPrevented?h.current=!1:d.onClose()}}),onPointerDown:t=>{e.onPointerDown?.(t),h.current=!0},onPointerUp:(0,o.M)(e.onPointerUp,e=>{h.current||e.currentTarget?.click()}),onKeyDown:(0,o.M)(e.onKeyDown,e=>{let t=""!==f.searchRef.current;n||t&&" "===e.key||tg.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});tJ.displayName=tX;var tG=r.forwardRef((e,t)=>{let{__scopeMenu:n,disabled:a=!1,textValue:s,...u}=e,d=tW(tX,n),f=tP(n),p=r.useRef(null),h=(0,i.e)(t,p),[g,m]=r.useState(!1),[y,v]=r.useState("");return r.useEffect(()=>{let e=p.current;e&&v((e.textContent??"").trim())},[u.children]),(0,c.jsx)(tx.ItemSlot,{scope:n,disabled:a,textValue:s??y,children:(0,c.jsx)(tc,{asChild:!0,...f,focusable:!a,children:(0,c.jsx)(l.WV.div,{role:"menuitem","data-highlighted":g?"":void 0,"aria-disabled":a||void 0,"data-disabled":a?"":void 0,...u,ref:h,onPointerMove:(0,o.M)(e.onPointerMove,nf(e=>{a?d.onItemLeave(e):(d.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:(0,o.M)(e.onPointerLeave,nf(e=>d.onItemLeave(e))),onFocus:(0,o.M)(e.onFocus,()=>m(!0)),onBlur:(0,o.M)(e.onBlur,()=>m(!1))})})})}),tZ=r.forwardRef((e,t)=>{let{checked:n=!1,onCheckedChange:r,...i}=e;return(0,c.jsx)(t4,{scope:e.__scopeMenu,checked:n,children:(0,c.jsx)(tJ,{role:"menuitemcheckbox","aria-checked":nc(n)?"mixed":n,...i,ref:t,"data-state":nd(n),onSelect:(0,o.M)(i.onSelect,()=>r?.(!!nc(n)||!n),{checkForDefaultPrevented:!1})})})});tZ.displayName="MenuCheckboxItem";var tQ="MenuRadioGroup",[t0,t1]=tE(tQ,{value:void 0,onValueChange:()=>{}}),t2=r.forwardRef((e,t)=>{let{value:n,onValueChange:r,...o}=e,i=(0,e$.W)(r);return(0,c.jsx)(t0,{scope:e.__scopeMenu,value:n,onValueChange:i,children:(0,c.jsx)(tK,{...o,ref:t})})});t2.displayName=tQ;var t8="MenuRadioItem",t3=r.forwardRef((e,t)=>{let{value:n,...r}=e,i=t1(t8,e.__scopeMenu),a=n===i.value;return(0,c.jsx)(t4,{scope:e.__scopeMenu,checked:a,children:(0,c.jsx)(tJ,{role:"menuitemradio","aria-checked":a,...r,ref:t,"data-state":nd(a),onSelect:(0,o.M)(r.onSelect,()=>i.onValueChange?.(n),{checkForDefaultPrevented:!1})})})});t3.displayName=t8;var t6="MenuItemIndicator",[t4,t5]=tE(t6,{checked:!1}),t7=r.forwardRef((e,t)=>{let{__scopeMenu:n,forceMount:r,...o}=e,i=t5(t6,n);return(0,c.jsx)(e4.z,{present:r||nc(i.checked)||!0===i.checked,children:(0,c.jsx)(l.WV.span,{...o,ref:t,"data-state":nd(i.checked)})})});t7.displayName=t6;var t9=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,c.jsx)(l.WV.div,{role:"separator","aria-orientation":"horizontal",...r,ref:t})});t9.displayName="MenuSeparator";var ne=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e,o=tR(n);return(0,c.jsx)(e1,{...o,...r,ref:t})});ne.displayName="MenuArrow";var nt="MenuSub",[nn,nr]=tE(nt),no=e=>{let{__scopeMenu:t,children:n,open:o=!1,onOpenChange:i}=e,a=tj(nt,t),s=tR(t),[l,u]=r.useState(null),[d,f]=r.useState(null),p=(0,e$.W)(i);return r.useEffect(()=>(!1===a.open&&p(!1),()=>p(!1)),[a.open,p]),(0,c.jsx)(eK,{...s,children:(0,c.jsx)(tk,{scope:t,open:o,onOpenChange:p,content:d,onContentChange:f,children:(0,c.jsx)(nn,{scope:t,contentId:(0,y.M)(),triggerId:(0,y.M)(),trigger:l,onTriggerChange:u,children:n})})})};no.displayName=nt;var ni="MenuSubTrigger",na=r.forwardRef((e,t)=>{let n=tj(ni,e.__scopeMenu),a=tL(ni,e.__scopeMenu),s=nr(ni,e.__scopeMenu),l=tW(ni,e.__scopeMenu),u=r.useRef(null),{pointerGraceTimerRef:d,onPointerGraceIntentChange:f}=l,p={__scopeMenu:e.__scopeMenu},h=r.useCallback(()=>{u.current&&window.clearTimeout(u.current),u.current=null},[]);return r.useEffect(()=>h,[h]),r.useEffect(()=>{let e=d.current;return()=>{window.clearTimeout(e),f(null)}},[d,f]),(0,c.jsx)(tN,{asChild:!0,...p,children:(0,c.jsx)(tG,{id:s.triggerId,"aria-haspopup":"menu","aria-expanded":n.open,"aria-controls":s.contentId,"data-state":nu(n.open),...e,ref:(0,i.F)(t,s.onTriggerChange),onClick:t=>{e.onClick?.(t),e.disabled||t.defaultPrevented||(t.currentTarget.focus(),n.open||n.onOpenChange(!0))},onPointerMove:(0,o.M)(e.onPointerMove,nf(t=>{l.onItemEnter(t),!t.defaultPrevented&&(e.disabled||n.open||u.current||(l.onPointerGraceIntentChange(null),u.current=window.setTimeout(()=>{n.onOpenChange(!0),h()},100)))})),onPointerLeave:(0,o.M)(e.onPointerLeave,nf(e=>{h();let t=n.content?.getBoundingClientRect();if(t){let r=n.content?.dataset.side,o="right"===r,i=t[o?"left":"right"],a=t[o?"right":"left"];l.onPointerGraceIntentChange({area:[{x:e.clientX+(o?-5:5),y:e.clientY},{x:i,y:t.top},{x:a,y:t.top},{x:a,y:t.bottom},{x:i,y:t.bottom}],side:r}),window.clearTimeout(d.current),d.current=window.setTimeout(()=>l.onPointerGraceIntentChange(null),300)}else{if(l.onTriggerLeave(e),e.defaultPrevented)return;l.onPointerGraceIntentChange(null)}})),onKeyDown:(0,o.M)(e.onKeyDown,t=>{let r=""!==l.searchRef.current;e.disabled||r&&" "===t.key||tv[a.dir].includes(t.key)&&(n.onOpenChange(!0),n.content?.focus(),t.preventDefault())})})})});na.displayName=ni;var ns="MenuSubContent",nl=r.forwardRef((e,t)=>{let n=tA(t$,e.__scopeMenu),{forceMount:a=n.forceMount,...s}=e,l=tj(t$,e.__scopeMenu),u=tL(t$,e.__scopeMenu),d=nr(ns,e.__scopeMenu),f=r.useRef(null),p=(0,i.e)(t,f);return(0,c.jsx)(tx.Provider,{scope:e.__scopeMenu,children:(0,c.jsx)(e4.z,{present:a||l.open,children:(0,c.jsx)(tx.Slot,{scope:e.__scopeMenu,children:(0,c.jsx)(tU,{id:d.contentId,"aria-labelledby":d.triggerId,...s,ref:p,align:"start",side:"rtl"===u.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{u.isUsingKeyboardRef.current&&f.current?.focus(),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:(0,o.M)(e.onFocusOutside,e=>{e.target!==d.trigger&&l.onOpenChange(!1)}),onEscapeKeyDown:(0,o.M)(e.onEscapeKeyDown,e=>{u.onClose(),e.preventDefault()}),onKeyDown:(0,o.M)(e.onKeyDown,e=>{let t=e.currentTarget.contains(e.target),n=tb[u.dir].includes(e.key);t&&n&&(l.onOpenChange(!1),d.trigger?.focus(),e.preventDefault())})})})})})});function nu(e){return e?"open":"closed"}function nc(e){return"indeterminate"===e}function nd(e){return nc(e)?"indeterminate":e?"checked":"unchecked"}function nf(e){return t=>"mouse"===t.pointerType?e(t):void 0}nl.displayName=ns;var np="DropdownMenu",[nh,ng]=(0,a.b)(np,[tC]),nm=tC(),[ny,nv]=nh(np),nb=e=>{let{__scopeDropdownMenu:t,children:n,dir:o,open:i,defaultOpen:a,onOpenChange:l,modal:u=!0}=e,d=nm(t),f=r.useRef(null),[p,h]=(0,s.T)({prop:i,defaultProp:a??!1,onChange:l,caller:np});return(0,c.jsx)(ny,{scope:t,triggerId:(0,y.M)(),triggerRef:f,contentId:(0,y.M)(),open:p,onOpenChange:h,onOpenToggle:r.useCallback(()=>h(e=>!e),[h]),modal:u,children:(0,c.jsx)(tM,{...d,open:p,onOpenChange:h,dir:o,modal:u,children:n})})};nb.displayName=np;var nw="DropdownMenuTrigger",nx=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,disabled:r=!1,...a}=e,s=nv(nw,n),u=nm(n);return(0,c.jsx)(tN,{asChild:!0,...u,children:(0,c.jsx)(l.WV.button,{type:"button",id:s.triggerId,"aria-haspopup":"menu","aria-expanded":s.open,"aria-controls":s.open?s.contentId:void 0,"data-state":s.open?"open":"closed","data-disabled":r?"":void 0,disabled:r,...a,ref:(0,i.F)(t,s.triggerRef),onPointerDown:(0,o.M)(e.onPointerDown,e=>{!r&&0===e.button&&!1===e.ctrlKey&&(s.onOpenToggle(),s.open||e.preventDefault())}),onKeyDown:(0,o.M)(e.onKeyDown,e=>{!r&&(["Enter"," "].includes(e.key)&&s.onOpenToggle(),"ArrowDown"===e.key&&s.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});nx.displayName=nw;var nS=e=>{let{__scopeDropdownMenu:t,...n}=e,r=nm(t);return(0,c.jsx)(tF,{...r,...n})};nS.displayName="DropdownMenuPortal";var nO="DropdownMenuContent",nE=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...i}=e,a=nv(nO,n),s=nm(n),l=r.useRef(!1);return(0,c.jsx)(tV,{id:a.contentId,"aria-labelledby":a.triggerId,...s,...i,ref:t,onCloseAutoFocus:(0,o.M)(e.onCloseAutoFocus,e=>{l.current||a.triggerRef.current?.focus(),l.current=!1,e.preventDefault()}),onInteractOutside:(0,o.M)(e.onInteractOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey,r=2===t.button||n;(!a.modal||r)&&(l.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});nE.displayName=nO,r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=nm(n);return(0,c.jsx)(tK,{...o,...r,ref:t})}).displayName="DropdownMenuGroup",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=nm(n);return(0,c.jsx)(tq,{...o,...r,ref:t})}).displayName="DropdownMenuLabel";var nC=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=nm(n);return(0,c.jsx)(tJ,{...o,...r,ref:t})});nC.displayName="DropdownMenuItem",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=nm(n);return(0,c.jsx)(tZ,{...o,...r,ref:t})}).displayName="DropdownMenuCheckboxItem",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=nm(n);return(0,c.jsx)(t2,{...o,...r,ref:t})}).displayName="DropdownMenuRadioGroup",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=nm(n);return(0,c.jsx)(t3,{...o,...r,ref:t})}).displayName="DropdownMenuRadioItem",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=nm(n);return(0,c.jsx)(t7,{...o,...r,ref:t})}).displayName="DropdownMenuItemIndicator";var nR=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=nm(n);return(0,c.jsx)(t9,{...o,...r,ref:t})});nR.displayName="DropdownMenuSeparator",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=nm(n);return(0,c.jsx)(ne,{...o,...r,ref:t})}).displayName="DropdownMenuArrow";var nP=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=nm(n);return(0,c.jsx)(na,{...o,...r,ref:t})});nP.displayName="DropdownMenuSubTrigger";var nk=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=nm(n);return(0,c.jsx)(nl,{...o,...r,ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});nk.displayName="DropdownMenuSubContent";var nj=nb,nT=nx,nL=nS,nM=nE,nN=nC,nD=nR,nI=e=>{let{__scopeDropdownMenu:t,children:n,open:r,onOpenChange:o,defaultOpen:i}=e,a=nm(t),[l,u]=(0,s.T)({prop:r,defaultProp:i??!1,onChange:o,caller:"DropdownMenuSub"});return(0,c.jsx)(no,{...a,open:l,onOpenChange:u,children:n})},nA=nP,nF=nk},70653:function(e,t,n){"use strict";n.d(t,{EW:()=>i});var r=n(2784),o=0;function i(){r.useEffect(()=>{let e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??a()),document.body.insertAdjacentElement("beforeend",e[1]??a()),o++,()=>{1===o&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),o--}},[])}function a(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}},65340:function(e,t,n){"use strict";let r;n.d(t,{M:()=>f});var o=n(2784),i=n(59656),a=n(3436),s=n(64148),l=n(52322),u="focusScope.autoFocusOnMount",c="focusScope.autoFocusOnUnmount",d={bubbles:!1,cancelable:!0},f=o.forwardRef((e,t)=>{let{loop:n=!1,trapped:r=!1,onMountAutoFocus:f,onUnmountAutoFocus:y,...v}=e,[b,w]=o.useState(null),x=(0,s.W)(f),S=(0,s.W)(y),O=o.useRef(null),E=(0,i.e)(t,e=>w(e)),C=o.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;o.useEffect(()=>{if(r){let e=function(e){if(C.paused||!b)return;let t=e.target;b.contains(t)?O.current=t:g(O.current,{select:!0})},t=function(e){if(C.paused||!b)return;let t=e.relatedTarget;null!==t&&(b.contains(t)||g(O.current,{select:!0}))};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&g(b)});return b&&n.observe(b,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[r,b,C.paused]),o.useEffect(()=>{if(b){m.add(C);let e=document.activeElement;if(!b.contains(e)){let t=new CustomEvent(u,d);b.addEventListener(u,x),b.dispatchEvent(t),t.defaultPrevented||(function(e,{select:t=!1}={}){let n=document.activeElement;for(let r of e)if(g(r,{select:t}),document.activeElement!==n)return}(p(b).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&g(b))}return()=>{b.removeEventListener(u,x),setTimeout(()=>{let t=new CustomEvent(c,d);b.addEventListener(c,S),b.dispatchEvent(t),t.defaultPrevented||g(e??document.body,{select:!0}),b.removeEventListener(c,S),m.remove(C)},0)}}},[b,x,S,C]);let R=o.useCallback(e=>{if(!n&&!r||C.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,o=document.activeElement;if(t&&o){let t=e.currentTarget,[r,i]=function(e){let t=p(e);return[h(t,e),h(t.reverse(),e)]}(t);r&&i?e.shiftKey||o!==i?e.shiftKey&&o===r&&(e.preventDefault(),n&&g(i,{select:!0})):(e.preventDefault(),n&&g(r,{select:!0})):o===t&&e.preventDefault()}},[n,r,C.paused]);return(0,l.jsx)(a.WV.div,{tabIndex:-1,...v,ref:E,onKeyDown:R})});function p(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function h(e,t){for(let n of e)if(!function(e,{upTo:t}){if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===t||e!==t);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function g(e,{select:t=!1}={}){if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}f.displayName="FocusScope";var m=(r=[],{add(e){let t=r[0];e!==t&&t?.pause(),(r=y(r,e)).unshift(e)},remove(e){r=y(r,e),r[0]?.resume()}});function y(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}},34890:function(e,t,n){"use strict";n.d(t,{M:()=>l});var r,o=n(2784),i=n(34896),a=(r||(r=n.t(o,2)))[" useId ".trim().toString()]||(()=>void 0),s=0;function l(e){let[t,n]=o.useState(a());return(0,i.b)(()=>{e||n(e=>e??String(s++))},[e]),e||(t?`radix-${t}`:"")}},55827:function(e,t,n){"use strict";n.d(t,{h:()=>l});var r=n(2784),o=n(28316),i=n(3436),a=n(34896),s=n(52322),l=r.forwardRef((e,t)=>{let{container:n,...l}=e,[u,c]=r.useState(!1);(0,a.b)(()=>c(!0),[]);let d=n||u&&globalThis?.document?.body;return d?o.createPortal((0,s.jsx)(i.WV.div,{...l,ref:t}),d):null});l.displayName="Portal"},42951:function(e,t,n){"use strict";n.d(t,{z:()=>a});var r=n(2784),o=n(59656),i=n(34896),a=e=>{var t;let n,a,{present:l,children:u}=e,c=function(e){var t,n;let[o,a]=r.useState(),l=r.useRef(null),u=r.useRef(e),c=r.useRef("none"),[d,f]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>n[e][t]??e,t));return r.useEffect(()=>{let e=s(l.current);c.current="mounted"===d?e:"none"},[d]),(0,i.b)(()=>{let t=l.current,n=u.current;if(n!==e){let r=c.current,o=s(t);e?f("MOUNT"):"none"===o||t?.display==="none"?f("UNMOUNT"):n&&r!==o?f("ANIMATION_OUT"):f("UNMOUNT"),u.current=e}},[e,f]),(0,i.b)(()=>{if(o){let e,t=o.ownerDocument.defaultView??window,n=n=>{let r=s(l.current).includes(n.animationName);if(n.target===o&&r&&(f("ANIMATION_END"),!u.current)){let n=o.style.animationFillMode;o.style.animationFillMode="forwards",e=t.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=n)})}},r=e=>{e.target===o&&(c.current=s(l.current))};return o.addEventListener("animationstart",r),o.addEventListener("animationcancel",n),o.addEventListener("animationend",n),()=>{t.clearTimeout(e),o.removeEventListener("animationstart",r),o.removeEventListener("animationcancel",n),o.removeEventListener("animationend",n)}}f("ANIMATION_END")},[o,f]),{isPresent:["mounted","unmountSuspended"].includes(d),ref:r.useCallback(e=>{l.current=e?getComputedStyle(e):null,a(e)},[])}}(l),d="function"==typeof u?u({present:c.isPresent}):r.Children.only(u),f=(0,o.e)(c.ref,(t=d,(a=(n=Object.getOwnPropertyDescriptor(t.props,"ref")?.get)&&"isReactWarning"in n&&n.isReactWarning)?t.ref:(a=(n=Object.getOwnPropertyDescriptor(t,"ref")?.get)&&"isReactWarning"in n&&n.isReactWarning)?t.props.ref:t.props.ref||t.ref));return"function"==typeof u||c.isPresent?r.cloneElement(d,{ref:f}):null};function s(e){return e?.animationName||"none"}a.displayName="Presence"},3436:function(e,t,n){"use strict";n.d(t,{WV:()=>s,jH:()=>l});var r=n(2784),o=n(28316),i=n(33883),a=n(52322),s=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=(0,i.Z8)(`Primitive.${t}`),o=r.forwardRef((e,r)=>{let{asChild:o,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,a.jsx)(o?n:t,{...i,ref:r})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{});function l(e,t){e&&o.flushSync(()=>e.dispatchEvent(t))}},33883:function(e,t,n){"use strict";n.d(t,{Z8:()=>a,sA:()=>l});var r=n(2784),o=n(59656),i=n(52322);function a(e){let t=function(e){let t=r.forwardRef((e,t)=>{let{children:n,...i}=e;if(r.isValidElement(n)){var a;let e,s,l=(a=n,(s=(e=Object.getOwnPropertyDescriptor(a.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?a.ref:(s=(e=Object.getOwnPropertyDescriptor(a,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?a.props.ref:a.props.ref||a.ref),u=function(e,t){let n={...t};for(let r in t){let o=e[r],i=t[r];/^on[A-Z]/.test(r)?o&&i?n[r]=(...e)=>{let t=i(...e);return o(...e),t}:o&&(n[r]=o):"style"===r?n[r]={...o,...i}:"className"===r&&(n[r]=[o,i].filter(Boolean).join(" "))}return{...e,...n}}(i,n.props);return n.type!==r.Fragment&&(u.ref=t?(0,o.F)(t,l):l),r.cloneElement(n,u)}return r.Children.count(n)>1?r.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),n=r.forwardRef((e,n)=>{let{children:o,...a}=e,s=r.Children.toArray(o),l=s.find(u);if(l){let e=l.props.children,o=s.map(t=>t!==l?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,i.jsx)(t,{...a,ref:n,children:r.isValidElement(e)?r.cloneElement(e,void 0,o):null})}return(0,i.jsx)(t,{...a,ref:n,children:o})});return n.displayName=`${e}.Slot`,n}var s=Symbol("radix.slottable");function l(e){let t=({children:e})=>(0,i.jsx)(i.Fragment,{children:e});return t.displayName=`${e}.Slottable`,t.__radixId=s,t}function u(e){return r.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===s}},64148:function(e,t,n){"use strict";n.d(t,{W:()=>o});var r=n(2784);function o(e){let t=r.useRef(e);return r.useEffect(()=>{t.current=e}),r.useMemo(()=>(...e)=>t.current?.(...e),[])}},69153:function(e,t,n){"use strict";n.d(t,{T:()=>s});var r,o=n(2784),i=n(34896),a=(r||(r=n.t(o,2)))[" useInsertionEffect ".trim().toString()]||i.b;function s({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){let[i,s,l]=function({defaultProp:e,onChange:t}){let[n,r]=o.useState(e),i=o.useRef(n),s=o.useRef(t);return a(()=>{s.current=t},[t]),o.useEffect(()=>{i.current!==n&&(s.current?.(n),i.current=n)},[n,i]),[n,r,s]}({defaultProp:t,onChange:n}),u=void 0!==e,c=u?e:i;{let t=o.useRef(void 0!==e);o.useEffect(()=>{let e=t.current;if(e!==u){let t=u?"controlled":"uncontrolled";console.warn(`${r} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=u},[u,r])}return[c,o.useCallback(t=>{if(u){let n="function"==typeof t?t(e):t;n!==e&&l.current?.(n)}else s(t)},[u,e,s,l])]}Symbol("RADIX:SYNC_STATE")},34896:function(e,t,n){"use strict";n.d(t,{b:()=>o});var r=n(2784),o=globalThis?.document?r.useLayoutEffect:()=>{}},65534:function(e,t,n){"use strict";n.d(t,{t:()=>i});var r=n(2784),o=n(34896);function i(e){let[t,n]=r.useState(void 0);return(0,o.b)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,o;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,o=t.blockSize}else r=e.offsetWidth,o=e.offsetHeight;n({width:r,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}},93407:function(e,t,n){"use strict";function r(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}n.d(t,{_:()=>r})},67239:function(e,t,n){"use strict";n.d(t,{Z:()=>r});let r=function(){for(var e,t,n=0,r="",o=arguments.length;n<o;n++)(e=arguments[n])&&(t=function e(t){var n,r,o="";if("string"==typeof t||"number"==typeof t)o+=t;else if("object"==typeof t)if(Array.isArray(t)){var i=t.length;for(n=0;n<i;n++)t[n]&&(r=e(t[n]))&&(o&&(o+=" "),o+=r)}else for(r in t)t[r]&&(o&&(o+=" "),o+=r);return o}(e))&&(r&&(r+=" "),r+=t);return r}},11690:function(e,t,n){"use strict";function r(e){return(r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function o(){return"function"==typeof XMLHttpRequest||("undefined"==typeof XMLHttpRequest?"undefined":r(XMLHttpRequest))==="object"}function i(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function a(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?i(Object(n),!0).forEach(function(t){var r,o,i;r=e,o=t,i=n[t],(o=function(e){var t=function(e,t){if("object"!=s(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=s(r))return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==s(t)?t:t+""}(o))in r?Object.defineProperty(r,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):r[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):i(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function s(e){return(s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}n.d(t,{Z:()=>C});var l,u,c,d,f="function"==typeof fetch?fetch:void 0;if("undefined"!=typeof global&&global.fetch?f=global.fetch:"undefined"!=typeof window&&window.fetch&&(f=window.fetch),o()&&("undefined"!=typeof global&&global.XMLHttpRequest?c=global.XMLHttpRequest:"undefined"!=typeof window&&window.XMLHttpRequest&&(c=window.XMLHttpRequest)),"function"==typeof ActiveXObject&&("undefined"!=typeof global&&global.ActiveXObject?d=global.ActiveXObject:"undefined"!=typeof window&&window.ActiveXObject&&(d=window.ActiveXObject)),"function"!=typeof f&&(f=void 0),!f&&!c&&!d)try{n.e("31629").then(n.t.bind(n,96279,19)).then(function(e){f=e.default}).catch(function(){})}catch(e){}var p=function(e,t){if(t&&"object"===s(t)){var n="";for(var r in t)n+="&"+encodeURIComponent(r)+"="+encodeURIComponent(t[r]);if(!n)return e;e=e+(-1!==e.indexOf("?")?"&":"?")+n.slice(1)}return e},h=function(e,t,n,r){var o=function(e){if(!e.ok)return n(e.statusText||"Error",{status:e.status});e.text().then(function(t){n(null,{status:e.status,data:t})}).catch(n)};if(r){var i=r(e,t);if(i instanceof Promise)return void i.then(o).catch(n)}"function"==typeof fetch?fetch(e,t).then(o).catch(n):f(e,t).then(o).catch(n)},g=!1,m=function(e,t,n,r){e.queryStringParams&&(t=p(t,e.queryStringParams));var o=a({},"function"==typeof e.customHeaders?e.customHeaders():e.customHeaders);"undefined"==typeof window&&"undefined"!=typeof global&&void 0!==global.process&&global.process.versions&&global.process.versions.node&&(o["User-Agent"]="i18next-http-backend (node/".concat(global.process.version,"; ").concat(global.process.platform," ").concat(global.process.arch,")")),n&&(o["Content-Type"]="application/json");var i="function"==typeof e.requestOptions?e.requestOptions(n):e.requestOptions,s=a({method:n?"POST":"GET",body:n?e.stringify(n):void 0,headers:o},g?{}:i),l="function"==typeof e.alternateFetch&&e.alternateFetch.length>=1?e.alternateFetch:void 0;try{h(t,s,r,l)}catch(e){if(!i||0===Object.keys(i).length||!e.message||0>e.message.indexOf("not implemented"))return r(e);try{Object.keys(i).forEach(function(e){delete s[e]}),h(t,s,r,l),g=!0}catch(e){r(e)}}},y=function(e,t,n,r){n&&"object"===s(n)&&(n=p("",n).slice(1)),e.queryStringParams&&(t=p(t,e.queryStringParams));try{var o=c?new c:new d("MSXML2.XMLHTTP.3.0");o.open(n?"POST":"GET",t,1),e.crossDomain||o.setRequestHeader("X-Requested-With","XMLHttpRequest"),o.withCredentials=!!e.withCredentials,n&&o.setRequestHeader("Content-Type","application/x-www-form-urlencoded"),o.overrideMimeType&&o.overrideMimeType("application/json");var i=e.customHeaders;if(i="function"==typeof i?i():i)for(var a in i)o.setRequestHeader(a,i[a]);o.onreadystatechange=function(){o.readyState>3&&r(o.status>=400?o.statusText:null,{status:o.status,data:o.responseText})},o.send(n)}catch(e){console&&console.log(e)}};let v=function(e,t,n,r){return("function"==typeof n&&(r=n,n=void 0),r=r||function(){},f&&0!==t.indexOf("file:"))?m(e,t,n,r):o()||"function"==typeof ActiveXObject?y(e,t,n,r):void r(Error("No fetch and no xhr implementation found!"))};function b(e){return(b="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function w(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function x(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?w(Object(n),!0).forEach(function(t){S(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):w(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function S(e,t,n){return(t=O(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function O(e){var t=function(e,t){if("object"!=b(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=b(r))return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==b(t)?t:t+""}var E=(l=function e(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(!(this instanceof e))throw TypeError("Cannot call a class as a function");this.services=t,this.options=n,this.allOptions=r,this.type="backend",this.init(t,n,r)},u=[{key:"init",value:function(e){var t=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(this.services=e,this.options=x(x(x({},{loadPath:"/locales/{{lng}}/{{ns}}.json",addPath:"/locales/add/{{lng}}/{{ns}}",parse:function(e){return JSON.parse(e)},stringify:JSON.stringify,parsePayload:function(e,t,n){return S({},t,n||"")},parseLoadPayload:function(e,t){},request:v,reloadInterval:"undefined"==typeof window&&36e5,customHeaders:{},queryStringParams:{},crossDomain:!1,withCredentials:!1,overrideMimeType:!1,requestOptions:{mode:"cors",credentials:"same-origin",cache:"default"}}),this.options||{}),n),this.allOptions=r,this.services&&this.options.reloadInterval){var o=setInterval(function(){return t.reload()},this.options.reloadInterval);"object"===b(o)&&"function"==typeof o.unref&&o.unref()}}},{key:"readMulti",value:function(e,t,n){this._readAny(e,e,t,t,n)}},{key:"read",value:function(e,t,n){this._readAny([e],e,[t],t,n)}},{key:"_readAny",value:function(e,t,n,r,o){var i,a,s=this,l=this.options.loadPath;"function"==typeof this.options.loadPath&&(l=this.options.loadPath(e,n)),(l=(a=i=l)&&"function"==typeof a.then?i:Promise.resolve(i)).then(function(i){if(!i)return o(null,{});var a=s.services.interpolator.interpolate(i,{lng:e.join("+"),ns:n.join("+")});s.loadUrl(a,o,t,r)})}},{key:"loadUrl",value:function(e,t,n,r){var o=this,i=this.options.parseLoadPayload("string"==typeof n?[n]:n,"string"==typeof r?[r]:r);this.options.request(this.options,e,i,function(i,a){if(a&&(a.status>=500&&a.status<600||!a.status))return t("failed loading "+e+"; status code: "+a.status,!0);if(a&&a.status>=400&&a.status<500)return t("failed loading "+e+"; status code: "+a.status,!1);if(!a&&i&&i.message){var s,l,u=i.message.toLowerCase();if(["failed","fetch","network","load"].find(function(e){return u.indexOf(e)>-1}))return t("failed loading "+e+": "+i.message,!0)}if(i)return t(i,!1);try{s="string"==typeof a.data?o.options.parse(a.data,n,r):a.data}catch(t){l="failed parsing "+e+" to json"}if(l)return t(l,!1);t(null,s)})}},{key:"create",value:function(e,t,n,r,o){var i=this;if(this.options.addPath){"string"==typeof e&&(e=[e]);var a=this.options.parsePayload(t,n,r),s=0,l=[],u=[];e.forEach(function(n){var r=i.options.addPath;"function"==typeof i.options.addPath&&(r=i.options.addPath(n,t));var c=i.services.interpolator.interpolate(r,{lng:n,ns:t});i.options.request(i.options,c,a,function(t,n){s+=1,l.push(t),u.push(n),s===e.length&&"function"==typeof o&&o(l,u)})})}}},{key:"reload",value:function(){var e=this,t=this.services,n=t.backendConnector,r=t.languageUtils,o=t.logger,i=n.language;if(!i||"cimode"!==i.toLowerCase()){var a=[],s=function(e){r.toResolveHierarchy(e).forEach(function(e){0>a.indexOf(e)&&a.push(e)})};s(i),this.allOptions.preload&&this.allOptions.preload.forEach(function(e){return s(e)}),a.forEach(function(t){e.allOptions.ns.forEach(function(e){n.read(t,e,"read",null,null,function(r,i){r&&o.warn("loading namespace ".concat(e," for language ").concat(t," failed"),r),!r&&i&&o.log("loaded namespace ".concat(e," for language ").concat(t),i),n.loaded("".concat(t,"|").concat(e),r,i)})})})}}}],function(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,O(r.key),r)}}(l.prototype,u),Object.defineProperty(l,"prototype",{writable:!1}),l);E.type="backend";let C=E},1347:function(e,t,n){"use strict";n.d(t,{ZP:()=>G});let r=e=>"string"==typeof e,o=()=>{let e,t,n=new Promise((n,r)=>{e=n,t=r});return n.resolve=e,n.reject=t,n},i=e=>null==e?"":""+e,a=(e,t,n)=>{e.forEach(e=>{t[e]&&(n[e]=t[e])})},s=/###/g,l=e=>e&&e.indexOf("###")>-1?e.replace(s,"."):e,u=e=>!e||r(e),c=(e,t,n)=>{let o=r(t)?t.split("."):t,i=0;for(;i<o.length-1;){if(u(e))return{};let t=l(o[i]);!e[t]&&n&&(e[t]=new n),e=Object.prototype.hasOwnProperty.call(e,t)?e[t]:{},++i}return u(e)?{}:{obj:e,k:l(o[i])}},d=(e,t,n)=>{let{obj:r,k:o}=c(e,t,Object);if(void 0!==r||1===t.length){r[o]=n;return}let i=t[t.length-1],a=t.slice(0,t.length-1),s=c(e,a,Object);for(;void 0===s.obj&&a.length;)i=`${a[a.length-1]}.${i}`,s=c(e,a=a.slice(0,a.length-1),Object),s?.obj&&void 0!==s.obj[`${s.k}.${i}`]&&(s.obj=void 0);s.obj[`${s.k}.${i}`]=n},f=(e,t,n,r)=>{let{obj:o,k:i}=c(e,t,Object);o[i]=o[i]||[],o[i].push(n)},p=(e,t)=>{let{obj:n,k:r}=c(e,t);if(n&&Object.prototype.hasOwnProperty.call(n,r))return n[r]},h=(e,t,n)=>{let r=p(e,n);return void 0!==r?r:p(t,n)},g=(e,t,n)=>{for(let o in t)"__proto__"!==o&&"constructor"!==o&&(o in e?r(e[o])||e[o]instanceof String||r(t[o])||t[o]instanceof String?n&&(e[o]=t[o]):g(e[o],t[o],n):e[o]=t[o]);return e},m=e=>e.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,"\\$&");var y={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;","/":"&#x2F;"};let v=e=>r(e)?e.replace(/[&<>"'\/]/g,e=>y[e]):e,b=[" ",",","?","!",";"],w=new class{constructor(e){this.capacity=e,this.regExpMap=new Map,this.regExpQueue=[]}getRegExp(e){let t=this.regExpMap.get(e);if(void 0!==t)return t;let n=new RegExp(e);return this.regExpQueue.length===this.capacity&&this.regExpMap.delete(this.regExpQueue.shift()),this.regExpMap.set(e,n),this.regExpQueue.push(e),n}}(20),x=(e,t,n)=>{t=t||"",n=n||"";let r=b.filter(e=>0>t.indexOf(e)&&0>n.indexOf(e));if(0===r.length)return!0;let o=w.getRegExp(`(${r.map(e=>"?"===e?"\\?":e).join("|")})`),i=!o.test(e);if(!i){let t=e.indexOf(n);t>0&&!o.test(e.substring(0,t))&&(i=!0)}return i},S=(e,t,n=".")=>{if(!e)return;if(e[t]){if(!Object.prototype.hasOwnProperty.call(e,t))return;return e[t]}let r=t.split(n),o=e;for(let e=0;e<r.length;){let t;if(!o||"object"!=typeof o)return;let i="";for(let a=e;a<r.length;++a)if(a!==e&&(i+=n),i+=r[a],void 0!==(t=o[i])){if(["string","number","boolean"].indexOf(typeof t)>-1&&a<r.length-1)continue;e+=a-e+1;break}o=t}return o},O=e=>e?.replace("_","-"),E={type:"logger",log(e){this.output("log",e)},warn(e){this.output("warn",e)},error(e){this.output("error",e)},output(e,t){console?.[e]?.apply?.(console,t)}};class C{constructor(e,t={}){this.init(e,t)}init(e,t={}){this.prefix=t.prefix||"i18next:",this.logger=e||E,this.options=t,this.debug=t.debug}log(...e){return this.forward(e,"log","",!0)}warn(...e){return this.forward(e,"warn","",!0)}error(...e){return this.forward(e,"error","")}deprecate(...e){return this.forward(e,"warn","WARNING DEPRECATED: ",!0)}forward(e,t,n,o){return o&&!this.debug?null:(r(e[0])&&(e[0]=`${n}${this.prefix} ${e[0]}`),this.logger[t](e))}create(e){return new C(this.logger,{...{prefix:`${this.prefix}:${e}:`},...this.options})}clone(e){return(e=e||this.options).prefix=e.prefix||this.prefix,new C(this.logger,e)}}var R=new C;class P{constructor(){this.observers={}}on(e,t){return e.split(" ").forEach(e=>{this.observers[e]||(this.observers[e]=new Map);let n=this.observers[e].get(t)||0;this.observers[e].set(t,n+1)}),this}off(e,t){if(this.observers[e]){if(!t)return void delete this.observers[e];this.observers[e].delete(t)}}emit(e,...t){this.observers[e]&&Array.from(this.observers[e].entries()).forEach(([e,n])=>{for(let r=0;r<n;r++)e(...t)}),this.observers["*"]&&Array.from(this.observers["*"].entries()).forEach(([n,r])=>{for(let o=0;o<r;o++)n.apply(n,[e,...t])})}}class k extends P{constructor(e,t={ns:["translation"],defaultNS:"translation"}){super(),this.data=e||{},this.options=t,void 0===this.options.keySeparator&&(this.options.keySeparator="."),void 0===this.options.ignoreJSONStructure&&(this.options.ignoreJSONStructure=!0)}addNamespaces(e){0>this.options.ns.indexOf(e)&&this.options.ns.push(e)}removeNamespaces(e){let t=this.options.ns.indexOf(e);t>-1&&this.options.ns.splice(t,1)}getResource(e,t,n,o={}){let i,a=void 0!==o.keySeparator?o.keySeparator:this.options.keySeparator,s=void 0!==o.ignoreJSONStructure?o.ignoreJSONStructure:this.options.ignoreJSONStructure;e.indexOf(".")>-1?i=e.split("."):(i=[e,t],n&&(Array.isArray(n)?i.push(...n):r(n)&&a?i.push(...n.split(a)):i.push(n)));let l=p(this.data,i);return(!l&&!t&&!n&&e.indexOf(".")>-1&&(e=i[0],t=i[1],n=i.slice(2).join(".")),!l&&s&&r(n))?S(this.data?.[e]?.[t],n,a):l}addResource(e,t,n,r,o={silent:!1}){let i=void 0!==o.keySeparator?o.keySeparator:this.options.keySeparator,a=[e,t];n&&(a=a.concat(i?n.split(i):n)),e.indexOf(".")>-1&&(a=e.split("."),r=t,t=a[1]),this.addNamespaces(t),d(this.data,a,r),o.silent||this.emit("added",e,t,n,r)}addResources(e,t,n,o={silent:!1}){for(let o in n)(r(n[o])||Array.isArray(n[o]))&&this.addResource(e,t,o,n[o],{silent:!0});o.silent||this.emit("added",e,t,n)}addResourceBundle(e,t,n,r,o,i={silent:!1,skipCopy:!1}){let a=[e,t];e.indexOf(".")>-1&&(a=e.split("."),r=n,n=t,t=a[1]),this.addNamespaces(t);let s=p(this.data,a)||{};i.skipCopy||(n=JSON.parse(JSON.stringify(n))),r?g(s,n,o):s={...s,...n},d(this.data,a,s),i.silent||this.emit("added",e,t,n)}removeResourceBundle(e,t){this.hasResourceBundle(e,t)&&delete this.data[e][t],this.removeNamespaces(t),this.emit("removed",e,t)}hasResourceBundle(e,t){return void 0!==this.getResource(e,t)}getResourceBundle(e,t){return t||(t=this.options.defaultNS),this.getResource(e,t)}getDataByLanguage(e){return this.data[e]}hasLanguageSomeTranslations(e){let t=this.getDataByLanguage(e);return!!(t&&Object.keys(t)||[]).find(e=>t[e]&&Object.keys(t[e]).length>0)}toJSON(){return this.data}}var j={processors:{},addPostProcessor(e){this.processors[e.name]=e},handle(e,t,n,r,o){return e.forEach(e=>{t=this.processors[e]?.process(t,n,r,o)??t}),t}};let T={},L=e=>!r(e)&&"boolean"!=typeof e&&"number"!=typeof e;class M extends P{constructor(e,t={}){super(),a(["resourceStore","languageUtils","pluralResolver","interpolator","backendConnector","i18nFormat","utils"],e,this),this.options=t,void 0===this.options.keySeparator&&(this.options.keySeparator="."),this.logger=R.create("translator")}changeLanguage(e){e&&(this.language=e)}exists(e,t={interpolation:{}}){let n={...t};if(null==e)return!1;let r=this.resolve(e,n);return r?.res!==void 0}extractFromKey(e,t){let n=void 0!==t.nsSeparator?t.nsSeparator:this.options.nsSeparator;void 0===n&&(n=":");let o=void 0!==t.keySeparator?t.keySeparator:this.options.keySeparator,i=t.ns||this.options.defaultNS||[],a=n&&e.indexOf(n)>-1,s=!this.options.userDefinedKeySeparator&&!t.keySeparator&&!this.options.userDefinedNsSeparator&&!t.nsSeparator&&!x(e,n,o);if(a&&!s){let t=e.match(this.interpolator.nestingRegexp);if(t&&t.length>0)return{key:e,namespaces:r(i)?[i]:i};let a=e.split(n);(n!==o||n===o&&this.options.ns.indexOf(a[0])>-1)&&(i=a.shift()),e=a.join(o)}return{key:e,namespaces:r(i)?[i]:i}}translate(e,t,n){let o="object"==typeof t?{...t}:t;if("object"!=typeof o&&this.options.overloadTranslationOptionHandler&&(o=this.options.overloadTranslationOptionHandler(arguments)),"object"==typeof options&&(o={...o}),o||(o={}),null==e)return"";Array.isArray(e)||(e=[String(e)]);let i=void 0!==o.returnDetails?o.returnDetails:this.options.returnDetails,a=void 0!==o.keySeparator?o.keySeparator:this.options.keySeparator,{key:s,namespaces:l}=this.extractFromKey(e[e.length-1],o),u=l[l.length-1],c=void 0!==o.nsSeparator?o.nsSeparator:this.options.nsSeparator;void 0===c&&(c=":");let d=o.lng||this.language,f=o.appendNamespaceToCIMode||this.options.appendNamespaceToCIMode;if(d?.toLowerCase()==="cimode")return f?i?{res:`${u}${c}${s}`,usedKey:s,exactUsedKey:s,usedLng:d,usedNS:u,usedParams:this.getUsedParamsDetails(o)}:`${u}${c}${s}`:i?{res:s,usedKey:s,exactUsedKey:s,usedLng:d,usedNS:u,usedParams:this.getUsedParamsDetails(o)}:s;let p=this.resolve(e,o),h=p?.res,g=p?.usedKey||s,m=p?.exactUsedKey||s,y=void 0!==o.joinArrays?o.joinArrays:this.options.joinArrays,v=!this.i18nFormat||this.i18nFormat.handleAsObject,b=void 0!==o.count&&!r(o.count),w=M.hasDefaultValue(o),x=b?this.pluralResolver.getSuffix(d,o.count,o):"",S=o.ordinal&&b?this.pluralResolver.getSuffix(d,o.count,{ordinal:!1}):"",O=b&&!o.ordinal&&0===o.count,E=O&&o[`defaultValue${this.options.pluralSeparator}zero`]||o[`defaultValue${x}`]||o[`defaultValue${S}`]||o.defaultValue,C=h;v&&!h&&w&&(C=E);let R=L(C),P=Object.prototype.toString.apply(C);if(v&&C&&R&&0>["[object Number]","[object Function]","[object RegExp]"].indexOf(P)&&!(r(y)&&Array.isArray(C))){if(!o.returnObjects&&!this.options.returnObjects){this.options.returnedObjectHandler||this.logger.warn("accessing an object - but returnObjects options is not enabled!");let e=this.options.returnedObjectHandler?this.options.returnedObjectHandler(g,C,{...o,ns:l}):`key '${s} (${this.language})' returned an object instead of string.`;return i?(p.res=e,p.usedParams=this.getUsedParamsDetails(o),p):e}if(a){let e=Array.isArray(C),t=e?[]:{},n=e?m:g;for(let e in C)if(Object.prototype.hasOwnProperty.call(C,e)){let r=`${n}${a}${e}`;w&&!h?t[e]=this.translate(r,{...o,defaultValue:L(E)?E[e]:void 0,...{joinArrays:!1,ns:l}}):t[e]=this.translate(r,{...o,joinArrays:!1,ns:l}),t[e]===r&&(t[e]=C[e])}h=t}}else if(v&&r(y)&&Array.isArray(h))(h=h.join(y))&&(h=this.extendTranslation(h,e,o,n));else{let t=!1,r=!1;!this.isValidLookup(h)&&w&&(t=!0,h=E),this.isValidLookup(h)||(r=!0,h=s);let i=(o.missingKeyNoValueFallbackToKey||this.options.missingKeyNoValueFallbackToKey)&&r?void 0:h,l=w&&E!==h&&this.options.updateMissing;if(r||t||l){if(this.logger.log(l?"updateKey":"missingKey",d,u,s,l?E:h),a){let e=this.resolve(s,{...o,keySeparator:!1});e&&e.res&&this.logger.warn("Seems the loaded translations were in flat JSON format instead of nested. Either set keySeparator: false on init or make sure your translations are published in nested format.")}let e=[],t=this.languageUtils.getFallbackCodes(this.options.fallbackLng,o.lng||this.language);if("fallback"===this.options.saveMissingTo&&t&&t[0])for(let n=0;n<t.length;n++)e.push(t[n]);else"all"===this.options.saveMissingTo?e=this.languageUtils.toResolveHierarchy(o.lng||this.language):e.push(o.lng||this.language);let n=(e,t,n)=>{let r=w&&n!==h?n:i;this.options.missingKeyHandler?this.options.missingKeyHandler(e,u,t,r,l,o):this.backendConnector?.saveMissing&&this.backendConnector.saveMissing(e,u,t,r,l,o),this.emit("missingKey",e,u,t,h)};this.options.saveMissing&&(this.options.saveMissingPlurals&&b?e.forEach(e=>{let t=this.pluralResolver.getSuffixes(e,o);O&&o[`defaultValue${this.options.pluralSeparator}zero`]&&0>t.indexOf(`${this.options.pluralSeparator}zero`)&&t.push(`${this.options.pluralSeparator}zero`),t.forEach(t=>{n([e],s+t,o[`defaultValue${t}`]||E)})}):n(e,s,E))}h=this.extendTranslation(h,e,o,p,n),r&&h===s&&this.options.appendNamespaceToMissingKey&&(h=`${u}${c}${s}`),(r||t)&&this.options.parseMissingKeyHandler&&(h=this.options.parseMissingKeyHandler(this.options.appendNamespaceToMissingKey?`${u}${c}${s}`:s,t?h:void 0,o))}return i?(p.res=h,p.usedParams=this.getUsedParamsDetails(o),p):h}extendTranslation(e,t,n,o,i){if(this.i18nFormat?.parse)e=this.i18nFormat.parse(e,{...this.options.interpolation.defaultVariables,...n},n.lng||this.language||o.usedLng,o.usedNS,o.usedKey,{resolved:o});else if(!n.skipInterpolation){let a;n.interpolation&&this.interpolator.init({...n,...{interpolation:{...this.options.interpolation,...n.interpolation}}});let s=r(e)&&(n?.interpolation?.skipOnVariables!==void 0?n.interpolation.skipOnVariables:this.options.interpolation.skipOnVariables);if(s){let t=e.match(this.interpolator.nestingRegexp);a=t&&t.length}let l=n.replace&&!r(n.replace)?n.replace:n;if(this.options.interpolation.defaultVariables&&(l={...this.options.interpolation.defaultVariables,...l}),e=this.interpolator.interpolate(e,l,n.lng||this.language||o.usedLng,n),s){let t=e.match(this.interpolator.nestingRegexp);a<(t&&t.length)&&(n.nest=!1)}!n.lng&&o&&o.res&&(n.lng=this.language||o.usedLng),!1!==n.nest&&(e=this.interpolator.nest(e,(...e)=>i?.[0]!==e[0]||n.context?this.translate(...e,t):(this.logger.warn(`It seems you are nesting recursively key: ${e[0]} in key: ${t[0]}`),null),n)),n.interpolation&&this.interpolator.reset()}let a=n.postProcess||this.options.postProcess,s=r(a)?[a]:a;return null!=e&&s?.length&&!1!==n.applyPostProcessor&&(e=j.handle(s,e,t,this.options&&this.options.postProcessPassResolved?{i18nResolved:{...o,usedParams:this.getUsedParamsDetails(n)},...n}:n,this)),e}resolve(e,t={}){let n,o,i,a,s;return r(e)&&(e=[e]),e.forEach(e=>{if(this.isValidLookup(n))return;let l=this.extractFromKey(e,t),u=l.key;o=u;let c=l.namespaces;this.options.fallbackNS&&(c=c.concat(this.options.fallbackNS));let d=void 0!==t.count&&!r(t.count),f=d&&!t.ordinal&&0===t.count,p=void 0!==t.context&&(r(t.context)||"number"==typeof t.context)&&""!==t.context,h=t.lngs?t.lngs:this.languageUtils.toResolveHierarchy(t.lng||this.language,t.fallbackLng);c.forEach(e=>{this.isValidLookup(n)||(s=e,!T[`${h[0]}-${e}`]&&this.utils?.hasLoadedNamespace&&!this.utils?.hasLoadedNamespace(s)&&(T[`${h[0]}-${e}`]=!0,this.logger.warn(`key "${o}" for languages "${h.join(", ")}" won't get resolved as namespace "${s}" was not yet loaded`,"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!")),h.forEach(r=>{let o;if(this.isValidLookup(n))return;a=r;let s=[u];if(this.i18nFormat?.addLookupKeys)this.i18nFormat.addLookupKeys(s,u,r,e,t);else{let e;d&&(e=this.pluralResolver.getSuffix(r,t.count,t));let n=`${this.options.pluralSeparator}zero`,o=`${this.options.pluralSeparator}ordinal${this.options.pluralSeparator}`;if(d&&(s.push(u+e),t.ordinal&&0===e.indexOf(o)&&s.push(u+e.replace(o,this.options.pluralSeparator)),f&&s.push(u+n)),p){let r=`${u}${this.options.contextSeparator}${t.context}`;s.push(r),d&&(s.push(r+e),t.ordinal&&0===e.indexOf(o)&&s.push(r+e.replace(o,this.options.pluralSeparator)),f&&s.push(r+n))}}for(;o=s.pop();)this.isValidLookup(n)||(i=o,n=this.getResource(r,e,o,t))}))})}),{res:n,usedKey:o,exactUsedKey:i,usedLng:a,usedNS:s}}isValidLookup(e){return void 0!==e&&!(!this.options.returnNull&&null===e)&&!(!this.options.returnEmptyString&&""===e)}getResource(e,t,n,r={}){return this.i18nFormat?.getResource?this.i18nFormat.getResource(e,t,n,r):this.resourceStore.getResource(e,t,n,r)}getUsedParamsDetails(e={}){let t=e.replace&&!r(e.replace),n=t?e.replace:e;if(t&&void 0!==e.count&&(n.count=e.count),this.options.interpolation.defaultVariables&&(n={...this.options.interpolation.defaultVariables,...n}),!t)for(let e of(n={...n},["defaultValue","ordinal","context","replace","lng","lngs","fallbackLng","ns","keySeparator","nsSeparator","returnObjects","returnDetails","joinArrays","postProcess","interpolation"]))delete n[e];return n}static hasDefaultValue(e){let t="defaultValue";for(let n in e)if(Object.prototype.hasOwnProperty.call(e,n)&&t===n.substring(0,t.length)&&void 0!==e[n])return!0;return!1}}class N{constructor(e){this.options=e,this.supportedLngs=this.options.supportedLngs||!1,this.logger=R.create("languageUtils")}getScriptPartFromCode(e){if(!(e=O(e))||0>e.indexOf("-"))return null;let t=e.split("-");return 2===t.length||(t.pop(),"x"===t[t.length-1].toLowerCase())?null:this.formatLanguageCode(t.join("-"))}getLanguagePartFromCode(e){if(!(e=O(e))||0>e.indexOf("-"))return e;let t=e.split("-");return this.formatLanguageCode(t[0])}formatLanguageCode(e){if(r(e)&&e.indexOf("-")>-1){let t;try{t=Intl.getCanonicalLocales(e)[0]}catch(e){}return(t&&this.options.lowerCaseLng&&(t=t.toLowerCase()),t)?t:this.options.lowerCaseLng?e.toLowerCase():e}return this.options.cleanCode||this.options.lowerCaseLng?e.toLowerCase():e}isSupportedCode(e){return("languageOnly"===this.options.load||this.options.nonExplicitSupportedLngs)&&(e=this.getLanguagePartFromCode(e)),!this.supportedLngs||!this.supportedLngs.length||this.supportedLngs.indexOf(e)>-1}getBestMatchFromCodes(e){let t;return e?(e.forEach(e=>{if(t)return;let n=this.formatLanguageCode(e);(!this.options.supportedLngs||this.isSupportedCode(n))&&(t=n)}),!t&&this.options.supportedLngs&&e.forEach(e=>{if(t)return;let n=this.getScriptPartFromCode(e);if(this.isSupportedCode(n))return t=n;let r=this.getLanguagePartFromCode(e);if(this.isSupportedCode(r))return t=r;t=this.options.supportedLngs.find(e=>{if(e===r||!(0>e.indexOf("-")&&0>r.indexOf("-"))&&(e.indexOf("-")>0&&0>r.indexOf("-")&&e.substring(0,e.indexOf("-"))===r||0===e.indexOf(r)&&r.length>1))return e})}),t||(t=this.getFallbackCodes(this.options.fallbackLng)[0]),t):null}getFallbackCodes(e,t){if(!e)return[];if("function"==typeof e&&(e=e(t)),r(e)&&(e=[e]),Array.isArray(e))return e;if(!t)return e.default||[];let n=e[t];return n||(n=e[this.getScriptPartFromCode(t)]),n||(n=e[this.formatLanguageCode(t)]),n||(n=e[this.getLanguagePartFromCode(t)]),n||(n=e.default),n||[]}toResolveHierarchy(e,t){let n=this.getFallbackCodes((!1===t?[]:t)||this.options.fallbackLng||[],e),o=[],i=e=>{e&&(this.isSupportedCode(e)?o.push(e):this.logger.warn(`rejecting language code not found in supportedLngs: ${e}`))};return r(e)&&(e.indexOf("-")>-1||e.indexOf("_")>-1)?("languageOnly"!==this.options.load&&i(this.formatLanguageCode(e)),"languageOnly"!==this.options.load&&"currentOnly"!==this.options.load&&i(this.getScriptPartFromCode(e)),"currentOnly"!==this.options.load&&i(this.getLanguagePartFromCode(e))):r(e)&&i(this.formatLanguageCode(e)),n.forEach(e=>{0>o.indexOf(e)&&i(this.formatLanguageCode(e))}),o}}let D={zero:0,one:1,two:2,few:3,many:4,other:5},I={select:e=>1===e?"one":"other",resolvedOptions:()=>({pluralCategories:["one","other"]})};class A{constructor(e,t={}){this.languageUtils=e,this.options=t,this.logger=R.create("pluralResolver"),this.pluralRulesCache={}}addRule(e,t){this.rules[e]=t}clearCache(){this.pluralRulesCache={}}getRule(e,t={}){let n,r=O("dev"===e?"en":e),o=t.ordinal?"ordinal":"cardinal",i=JSON.stringify({cleanedCode:r,type:o});if(i in this.pluralRulesCache)return this.pluralRulesCache[i];try{n=new Intl.PluralRules(r,{type:o})}catch(o){if(!Intl)return this.logger.error("No Intl support, please use an Intl polyfill!"),I;if(!e.match(/-|_/))return I;let r=this.languageUtils.getLanguagePartFromCode(e);n=this.getRule(r,t)}return this.pluralRulesCache[i]=n,n}needsPlural(e,t={}){let n=this.getRule(e,t);return n||(n=this.getRule("dev",t)),n?.resolvedOptions().pluralCategories.length>1}getPluralFormsOfKey(e,t,n={}){return this.getSuffixes(e,n).map(e=>`${t}${e}`)}getSuffixes(e,t={}){let n=this.getRule(e,t);return(n||(n=this.getRule("dev",t)),n)?n.resolvedOptions().pluralCategories.sort((e,t)=>D[e]-D[t]).map(e=>`${this.options.prepend}${t.ordinal?`ordinal${this.options.prepend}`:""}${e}`):[]}getSuffix(e,t,n={}){let r=this.getRule(e,n);return r?`${this.options.prepend}${n.ordinal?`ordinal${this.options.prepend}`:""}${r.select(t)}`:(this.logger.warn(`no plural rule found for: ${e}`),this.getSuffix("dev",t,n))}}let F=(e,t,n,o=".",i=!0)=>{let a=h(e,t,n);return!a&&i&&r(n)&&void 0===(a=S(e,n,o))&&(a=S(t,n,o)),a},$=e=>e.replace(/\$/g,"$$$$");class _{constructor(e={}){this.logger=R.create("interpolator"),this.options=e,this.format=e?.interpolation?.format||(e=>e),this.init(e)}init(e={}){e.interpolation||(e.interpolation={escapeValue:!0});let{escape:t,escapeValue:n,useRawValueToEscape:r,prefix:o,prefixEscaped:i,suffix:a,suffixEscaped:s,formatSeparator:l,unescapeSuffix:u,unescapePrefix:c,nestingPrefix:d,nestingPrefixEscaped:f,nestingSuffix:p,nestingSuffixEscaped:h,nestingOptionsSeparator:g,maxReplaces:y,alwaysFormat:b}=e.interpolation;this.escape=void 0!==t?t:v,this.escapeValue=void 0===n||n,this.useRawValueToEscape=void 0!==r&&r,this.prefix=o?m(o):i||"{{",this.suffix=a?m(a):s||"}}",this.formatSeparator=l||",",this.unescapePrefix=u?"":c||"-",this.unescapeSuffix=this.unescapePrefix?"":u||"",this.nestingPrefix=d?m(d):f||m("$t("),this.nestingSuffix=p?m(p):h||m(")"),this.nestingOptionsSeparator=g||",",this.maxReplaces=y||1e3,this.alwaysFormat=void 0!==b&&b,this.resetRegExp()}reset(){this.options&&this.init(this.options)}resetRegExp(){let e=(e,t)=>e?.source===t?(e.lastIndex=0,e):RegExp(t,"g");this.regexp=e(this.regexp,`${this.prefix}(.+?)${this.suffix}`),this.regexpUnescape=e(this.regexpUnescape,`${this.prefix}${this.unescapePrefix}(.+?)${this.unescapeSuffix}${this.suffix}`),this.nestingRegexp=e(this.nestingRegexp,`${this.nestingPrefix}(.+?)${this.nestingSuffix}`)}interpolate(e,t,n,o){let a,s,l,u=this.options&&this.options.interpolation&&this.options.interpolation.defaultVariables||{},c=e=>{if(0>e.indexOf(this.formatSeparator)){let r=F(t,u,e,this.options.keySeparator,this.options.ignoreJSONStructure);return this.alwaysFormat?this.format(r,void 0,n,{...o,...t,interpolationkey:e}):r}let r=e.split(this.formatSeparator),i=r.shift().trim(),a=r.join(this.formatSeparator).trim();return this.format(F(t,u,i,this.options.keySeparator,this.options.ignoreJSONStructure),a,n,{...o,...t,interpolationkey:i})};this.resetRegExp();let d=o?.missingInterpolationHandler||this.options.missingInterpolationHandler,f=o?.interpolation?.skipOnVariables!==void 0?o.interpolation.skipOnVariables:this.options.interpolation.skipOnVariables;return[{regex:this.regexpUnescape,safeValue:e=>$(e)},{regex:this.regexp,safeValue:e=>this.escapeValue?$(this.escape(e)):$(e)}].forEach(t=>{for(l=0;a=t.regex.exec(e);){let n=a[1].trim();if(void 0===(s=c(n)))if("function"==typeof d){let t=d(e,a,o);s=r(t)?t:""}else if(o&&Object.prototype.hasOwnProperty.call(o,n))s="";else if(f){s=a[0];continue}else this.logger.warn(`missed to pass in variable ${n} for interpolating ${e}`),s="";else r(s)||this.useRawValueToEscape||(s=i(s));let u=t.safeValue(s);if(e=e.replace(a[0],u),f?(t.regex.lastIndex+=s.length,t.regex.lastIndex-=a[0].length):t.regex.lastIndex=0,++l>=this.maxReplaces)break}}),e}nest(e,t,n={}){let o,a,s,l=(e,t)=>{let n=this.nestingOptionsSeparator;if(0>e.indexOf(n))return e;let r=e.split(RegExp(`${n}[ ]*{`)),o=`{${r[1]}`;e=r[0];let i=(o=this.interpolate(o,s)).match(/'/g),a=o.match(/"/g);((i?.length??0)%2!=0||a)&&a.length%2==0||(o=o.replace(/'/g,'"'));try{s=JSON.parse(o),t&&(s={...t,...s})}catch(t){return this.logger.warn(`failed parsing options string in nesting for key ${e}`,t),`${e}${n}${o}`}return s.defaultValue&&s.defaultValue.indexOf(this.prefix)>-1&&delete s.defaultValue,e};for(;o=this.nestingRegexp.exec(e);){let u=[];(s=(s={...n}).replace&&!r(s.replace)?s.replace:s).applyPostProcessor=!1,delete s.defaultValue;let c=!1;if(-1!==o[0].indexOf(this.formatSeparator)&&!/{.*}/.test(o[1])){let e=o[1].split(this.formatSeparator).map(e=>e.trim());o[1]=e.shift(),u=e,c=!0}if((a=t(l.call(this,o[1].trim(),s),s))&&o[0]===e&&!r(a))return a;r(a)||(a=i(a)),a||(this.logger.warn(`missed to resolve ${o[1]} for nesting ${e}`),a=""),c&&(a=u.reduce((e,t)=>this.format(e,t,n.lng,{...n,interpolationkey:o[1].trim()}),a.trim())),e=e.replace(o[0],a),this.regexp.lastIndex=0}return e}}let W=e=>{let t=e.toLowerCase().trim(),n={};if(e.indexOf("(")>-1){let r=e.split("(");t=r[0].toLowerCase().trim();let o=r[1].substring(0,r[1].length-1);"currency"===t&&0>o.indexOf(":")?n.currency||(n.currency=o.trim()):"relativetime"===t&&0>o.indexOf(":")?n.range||(n.range=o.trim()):o.split(";").forEach(e=>{if(e){let[t,...r]=e.split(":"),o=r.join(":").trim().replace(/^'+|'+$/g,""),i=t.trim();n[i]||(n[i]=o),"false"===o&&(n[i]=!1),"true"===o&&(n[i]=!0),isNaN(o)||(n[i]=parseInt(o,10))}})}return{formatName:t,formatOptions:n}},V=e=>{let t={};return(n,r,o)=>{let i=o;o&&o.interpolationkey&&o.formatParams&&o.formatParams[o.interpolationkey]&&o[o.interpolationkey]&&(i={...i,[o.interpolationkey]:void 0});let a=r+JSON.stringify(i),s=t[a];return s||(s=e(O(r),o),t[a]=s),s(n)}},H=e=>(t,n,r)=>e(O(n),r)(t);class B{constructor(e={}){this.logger=R.create("formatter"),this.options=e,this.init(e)}init(e,t={interpolation:{}}){this.formatSeparator=t.interpolation.formatSeparator||",";let n=t.cacheInBuiltFormats?V:H;this.formats={number:n((e,t)=>{let n=new Intl.NumberFormat(e,{...t});return e=>n.format(e)}),currency:n((e,t)=>{let n=new Intl.NumberFormat(e,{...t,style:"currency"});return e=>n.format(e)}),datetime:n((e,t)=>{let n=new Intl.DateTimeFormat(e,{...t});return e=>n.format(e)}),relativetime:n((e,t)=>{let n=new Intl.RelativeTimeFormat(e,{...t});return e=>n.format(e,t.range||"day")}),list:n((e,t)=>{let n=new Intl.ListFormat(e,{...t});return e=>n.format(e)})}}add(e,t){this.formats[e.toLowerCase().trim()]=t}addCached(e,t){this.formats[e.toLowerCase().trim()]=V(t)}format(e,t,n,r={}){let o=t.split(this.formatSeparator);if(o.length>1&&o[0].indexOf("(")>1&&0>o[0].indexOf(")")&&o.find(e=>e.indexOf(")")>-1)){let e=o.findIndex(e=>e.indexOf(")")>-1);o[0]=[o[0],...o.splice(1,e)].join(this.formatSeparator)}return o.reduce((e,t)=>{let{formatName:o,formatOptions:i}=W(t);if(this.formats[o]){let t=e;try{let a=r?.formatParams?.[r.interpolationkey]||{},s=a.locale||a.lng||r.locale||r.lng||n;t=this.formats[o](e,s,{...i,...r,...a})}catch(e){this.logger.warn(e)}return t}return this.logger.warn(`there was no format function for ${o}`),e},e)}}let z=(e,t)=>{void 0!==e.pending[t]&&(delete e.pending[t],e.pendingCount--)};class U extends P{constructor(e,t,n,r={}){super(),this.backend=e,this.store=t,this.services=n,this.languageUtils=n.languageUtils,this.options=r,this.logger=R.create("backendConnector"),this.waitingReads=[],this.maxParallelReads=r.maxParallelReads||10,this.readingCalls=0,this.maxRetries=r.maxRetries>=0?r.maxRetries:5,this.retryTimeout=r.retryTimeout>=1?r.retryTimeout:350,this.state={},this.queue=[],this.backend?.init?.(n,r.backend,r)}queueLoad(e,t,n,r){let o={},i={},a={},s={};return e.forEach(e=>{let r=!0;t.forEach(t=>{let a=`${e}|${t}`;!n.reload&&this.store.hasResourceBundle(e,t)?this.state[a]=2:this.state[a]<0||(1===this.state[a]?void 0===i[a]&&(i[a]=!0):(this.state[a]=1,r=!1,void 0===i[a]&&(i[a]=!0),void 0===o[a]&&(o[a]=!0),void 0===s[t]&&(s[t]=!0)))}),r||(a[e]=!0)}),(Object.keys(o).length||Object.keys(i).length)&&this.queue.push({pending:i,pendingCount:Object.keys(i).length,loaded:{},errors:[],callback:r}),{toLoad:Object.keys(o),pending:Object.keys(i),toLoadLanguages:Object.keys(a),toLoadNamespaces:Object.keys(s)}}loaded(e,t,n){let r=e.split("|"),o=r[0],i=r[1];t&&this.emit("failedLoading",o,i,t),!t&&n&&this.store.addResourceBundle(o,i,n,void 0,void 0,{skipCopy:!0}),this.state[e]=t?-1:2,t&&n&&(this.state[e]=0);let a={};this.queue.forEach(n=>{f(n.loaded,[o],i),z(n,e),t&&n.errors.push(t),0!==n.pendingCount||n.done||(Object.keys(n.loaded).forEach(e=>{a[e]||(a[e]={});let t=n.loaded[e];t.length&&t.forEach(t=>{void 0===a[e][t]&&(a[e][t]=!0)})}),n.done=!0,n.errors.length?n.callback(n.errors):n.callback())}),this.emit("loaded",a),this.queue=this.queue.filter(e=>!e.done)}read(e,t,n,r=0,o=this.retryTimeout,i){if(!e.length)return i(null,{});if(this.readingCalls>=this.maxParallelReads)return void this.waitingReads.push({lng:e,ns:t,fcName:n,tried:r,wait:o,callback:i});this.readingCalls++;let a=(a,s)=>{if(this.readingCalls--,this.waitingReads.length>0){let e=this.waitingReads.shift();this.read(e.lng,e.ns,e.fcName,e.tried,e.wait,e.callback)}if(a&&s&&r<this.maxRetries)return void setTimeout(()=>{this.read.call(this,e,t,n,r+1,2*o,i)},o);i(a,s)},s=this.backend[n].bind(this.backend);if(2===s.length){try{let n=s(e,t);n&&"function"==typeof n.then?n.then(e=>a(null,e)).catch(a):a(null,n)}catch(e){a(e)}return}return s(e,t,a)}prepareLoading(e,t,n={},o){if(!this.backend)return this.logger.warn("No backend was added via i18next.use. Will not load resources."),o&&o();r(e)&&(e=this.languageUtils.toResolveHierarchy(e)),r(t)&&(t=[t]);let i=this.queueLoad(e,t,n,o);if(!i.toLoad.length)return i.pending.length||o(),null;i.toLoad.forEach(e=>{this.loadOne(e)})}load(e,t,n){this.prepareLoading(e,t,{},n)}reload(e,t,n){this.prepareLoading(e,t,{reload:!0},n)}loadOne(e,t=""){let n=e.split("|"),r=n[0],o=n[1];this.read(r,o,"read",void 0,void 0,(n,i)=>{n&&this.logger.warn(`${t}loading namespace ${o} for language ${r} failed`,n),!n&&i&&this.logger.log(`${t}loaded namespace ${o} for language ${r}`,i),this.loaded(e,n,i)})}saveMissing(e,t,n,r,o,i={},a=()=>{}){if(this.services?.utils?.hasLoadedNamespace&&!this.services?.utils?.hasLoadedNamespace(t))return void this.logger.warn(`did not save key "${n}" as the namespace "${t}" was not yet loaded`,"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!");if(null!=n&&""!==n){if(this.backend?.create){let s={...i,isUpdate:o},l=this.backend.create.bind(this.backend);if(l.length<6)try{let o;(o=5===l.length?l(e,t,n,r,s):l(e,t,n,r))&&"function"==typeof o.then?o.then(e=>a(null,e)).catch(a):a(null,o)}catch(e){a(e)}else l(e,t,n,r,a,s)}e&&e[0]&&this.store.addResource(e[0],t,n,r)}}}let K=()=>({debug:!1,initAsync:!0,ns:["translation"],defaultNS:["translation"],fallbackLng:["dev"],fallbackNS:!1,supportedLngs:!1,nonExplicitSupportedLngs:!1,load:"all",preload:!1,simplifyPluralSuffix:!0,keySeparator:".",nsSeparator:":",pluralSeparator:"_",contextSeparator:"_",partialBundledLanguages:!1,saveMissing:!1,updateMissing:!1,saveMissingTo:"fallback",saveMissingPlurals:!0,missingKeyHandler:!1,missingInterpolationHandler:!1,postProcess:!1,postProcessPassResolved:!1,returnNull:!1,returnEmptyString:!0,returnObjects:!1,joinArrays:!1,returnedObjectHandler:!1,parseMissingKeyHandler:!1,appendNamespaceToMissingKey:!1,appendNamespaceToCIMode:!1,overloadTranslationOptionHandler:e=>{let t={};if("object"==typeof e[1]&&(t=e[1]),r(e[1])&&(t.defaultValue=e[1]),r(e[2])&&(t.tDescription=e[2]),"object"==typeof e[2]||"object"==typeof e[3]){let n=e[3]||e[2];Object.keys(n).forEach(e=>{t[e]=n[e]})}return t},interpolation:{escapeValue:!0,format:e=>e,prefix:"{{",suffix:"}}",formatSeparator:",",unescapePrefix:"-",nestingPrefix:"$t(",nestingSuffix:")",nestingOptionsSeparator:",",maxReplaces:1e3,skipOnVariables:!0},cacheInBuiltFormats:!0}),q=e=>(r(e.ns)&&(e.ns=[e.ns]),r(e.fallbackLng)&&(e.fallbackLng=[e.fallbackLng]),r(e.fallbackNS)&&(e.fallbackNS=[e.fallbackNS]),e.supportedLngs?.indexOf?.("cimode")<0&&(e.supportedLngs=e.supportedLngs.concat(["cimode"])),"boolean"==typeof e.initImmediate&&(e.initAsync=e.initImmediate),e),X=()=>{},Y=e=>{Object.getOwnPropertyNames(Object.getPrototypeOf(e)).forEach(t=>{"function"==typeof e[t]&&(e[t]=e[t].bind(e))})};class J extends P{constructor(e={},t){if(super(),this.options=q(e),this.services={},this.logger=R,this.modules={external:[]},Y(this),t&&!this.isInitialized&&!e.isClone){if(!this.options.initAsync)return this.init(e,t),this;setTimeout(()=>{this.init(e,t)},0)}}init(e={},t){this.isInitializing=!0,"function"==typeof e&&(t=e,e={}),null==e.defaultNS&&e.ns&&(r(e.ns)?e.defaultNS=e.ns:0>e.ns.indexOf("translation")&&(e.defaultNS=e.ns[0]));let n=K();this.options={...n,...this.options,...q(e)},this.options.interpolation={...n.interpolation,...this.options.interpolation},void 0!==e.keySeparator&&(this.options.userDefinedKeySeparator=e.keySeparator),void 0!==e.nsSeparator&&(this.options.userDefinedNsSeparator=e.nsSeparator);let i=e=>e?"function"==typeof e?new e:e:null;if(!this.options.isClone){let e;this.modules.logger?R.init(i(this.modules.logger),this.options):R.init(null,this.options),e=this.modules.formatter?this.modules.formatter:B;let t=new N(this.options);this.store=new k(this.options.resources,this.options);let r=this.services;r.logger=R,r.resourceStore=this.store,r.languageUtils=t,r.pluralResolver=new A(t,{prepend:this.options.pluralSeparator,simplifyPluralSuffix:this.options.simplifyPluralSuffix}),e&&(!this.options.interpolation.format||this.options.interpolation.format===n.interpolation.format)&&(r.formatter=i(e),r.formatter.init(r,this.options),this.options.interpolation.format=r.formatter.format.bind(r.formatter)),r.interpolator=new _(this.options),r.utils={hasLoadedNamespace:this.hasLoadedNamespace.bind(this)},r.backendConnector=new U(i(this.modules.backend),r.resourceStore,r,this.options),r.backendConnector.on("*",(e,...t)=>{this.emit(e,...t)}),this.modules.languageDetector&&(r.languageDetector=i(this.modules.languageDetector),r.languageDetector.init&&r.languageDetector.init(r,this.options.detection,this.options)),this.modules.i18nFormat&&(r.i18nFormat=i(this.modules.i18nFormat),r.i18nFormat.init&&r.i18nFormat.init(this)),this.translator=new M(this.services,this.options),this.translator.on("*",(e,...t)=>{this.emit(e,...t)}),this.modules.external.forEach(e=>{e.init&&e.init(this)})}if(this.format=this.options.interpolation.format,t||(t=X),this.options.fallbackLng&&!this.services.languageDetector&&!this.options.lng){let e=this.services.languageUtils.getFallbackCodes(this.options.fallbackLng);e.length>0&&"dev"!==e[0]&&(this.options.lng=e[0])}this.services.languageDetector||this.options.lng||this.logger.warn("init: no languageDetector is used and no lng is defined"),["getResource","hasResourceBundle","getResourceBundle","getDataByLanguage"].forEach(e=>{this[e]=(...t)=>this.store[e](...t)}),["addResource","addResources","addResourceBundle","removeResourceBundle"].forEach(e=>{this[e]=(...t)=>(this.store[e](...t),this)});let a=o(),s=()=>{let e=(e,n)=>{this.isInitializing=!1,this.isInitialized&&!this.initializedStoreOnce&&this.logger.warn("init: i18next is already initialized. You should call init just once!"),this.isInitialized=!0,this.options.isClone||this.logger.log("initialized",this.options),this.emit("initialized",this.options),a.resolve(n),t(e,n)};if(this.languages&&!this.isInitialized)return e(null,this.t.bind(this));this.changeLanguage(this.options.lng,e)};return this.options.resources||!this.options.initAsync?s():setTimeout(s,0),a}loadResources(e,t=X){let n=t,o=r(e)?e:this.language;if("function"==typeof e&&(n=e),!this.options.resources||this.options.partialBundledLanguages){if(o?.toLowerCase()==="cimode"&&(!this.options.preload||0===this.options.preload.length))return n();let e=[],t=t=>{t&&"cimode"!==t&&this.services.languageUtils.toResolveHierarchy(t).forEach(t=>{"cimode"!==t&&0>e.indexOf(t)&&e.push(t)})};o?t(o):this.services.languageUtils.getFallbackCodes(this.options.fallbackLng).forEach(e=>t(e)),this.options.preload?.forEach?.(e=>t(e)),this.services.backendConnector.load(e,this.options.ns,e=>{e||this.resolvedLanguage||!this.language||this.setResolvedLanguage(this.language),n(e)})}else n(null)}reloadResources(e,t,n){let r=o();return"function"==typeof e&&(n=e,e=void 0),"function"==typeof t&&(n=t,t=void 0),e||(e=this.languages),t||(t=this.options.ns),n||(n=X),this.services.backendConnector.reload(e,t,e=>{r.resolve(),n(e)}),r}use(e){if(!e)throw Error("You are passing an undefined module! Please check the object you are passing to i18next.use()");if(!e.type)throw Error("You are passing a wrong module! Please check the object you are passing to i18next.use()");return"backend"===e.type&&(this.modules.backend=e),("logger"===e.type||e.log&&e.warn&&e.error)&&(this.modules.logger=e),"languageDetector"===e.type&&(this.modules.languageDetector=e),"i18nFormat"===e.type&&(this.modules.i18nFormat=e),"postProcessor"===e.type&&j.addPostProcessor(e),"formatter"===e.type&&(this.modules.formatter=e),"3rdParty"===e.type&&this.modules.external.push(e),this}setResolvedLanguage(e){if(e&&this.languages&&!(["cimode","dev"].indexOf(e)>-1)){for(let e=0;e<this.languages.length;e++){let t=this.languages[e];if(!(["cimode","dev"].indexOf(t)>-1)&&this.store.hasLanguageSomeTranslations(t)){this.resolvedLanguage=t;break}}!this.resolvedLanguage&&0>this.languages.indexOf(e)&&this.store.hasLanguageSomeTranslations(e)&&(this.resolvedLanguage=e,this.languages.unshift(e))}}changeLanguage(e,t){this.isLanguageChangingTo=e;let n=o();this.emit("languageChanging",e);let i=e=>{this.language=e,this.languages=this.services.languageUtils.toResolveHierarchy(e),this.resolvedLanguage=void 0,this.setResolvedLanguage(e)},a=(r,o)=>{o?this.isLanguageChangingTo===e&&(i(o),this.translator.changeLanguage(o),this.isLanguageChangingTo=void 0,this.emit("languageChanged",o),this.logger.log("languageChanged",o)):this.isLanguageChangingTo=void 0,n.resolve((...e)=>this.t(...e)),t&&t(r,(...e)=>this.t(...e))},s=t=>{e||t||!this.services.languageDetector||(t=[]);let n=r(t)?t:t&&t[0],o=this.store.hasLanguageSomeTranslations(n)?n:this.services.languageUtils.getBestMatchFromCodes(r(t)?[t]:t);o&&(this.language||i(o),this.translator.language||this.translator.changeLanguage(o),this.services.languageDetector?.cacheUserLanguage?.(o)),this.loadResources(o,e=>{a(e,o)})};return e||!this.services.languageDetector||this.services.languageDetector.async?!e&&this.services.languageDetector&&this.services.languageDetector.async?0===this.services.languageDetector.detect.length?this.services.languageDetector.detect().then(s):this.services.languageDetector.detect(s):s(e):s(this.services.languageDetector.detect()),n}getFixedT(e,t,n){let o=(e,t,...r)=>{let i,a;(i="object"!=typeof t?this.options.overloadTranslationOptionHandler([e,t].concat(r)):{...t}).lng=i.lng||o.lng,i.lngs=i.lngs||o.lngs,i.ns=i.ns||o.ns,""!==i.keyPrefix&&(i.keyPrefix=i.keyPrefix||n||o.keyPrefix);let s=this.options.keySeparator||".";return a=i.keyPrefix&&Array.isArray(e)?e.map(e=>`${i.keyPrefix}${s}${e}`):i.keyPrefix?`${i.keyPrefix}${s}${e}`:e,this.t(a,i)};return r(e)?o.lng=e:o.lngs=e,o.ns=t,o.keyPrefix=n,o}t(...e){return this.translator?.translate(...e)}exists(...e){return this.translator?.exists(...e)}setDefaultNamespace(e){this.options.defaultNS=e}hasLoadedNamespace(e,t={}){if(!this.isInitialized)return this.logger.warn("hasLoadedNamespace: i18next was not initialized",this.languages),!1;if(!this.languages||!this.languages.length)return this.logger.warn("hasLoadedNamespace: i18n.languages were undefined or empty",this.languages),!1;let n=t.lng||this.resolvedLanguage||this.languages[0],r=!!this.options&&this.options.fallbackLng,o=this.languages[this.languages.length-1];if("cimode"===n.toLowerCase())return!0;let i=(e,t)=>{let n=this.services.backendConnector.state[`${e}|${t}`];return -1===n||0===n||2===n};if(t.precheck){let e=t.precheck(this,i);if(void 0!==e)return e}return!!(this.hasResourceBundle(n,e)||!this.services.backendConnector.backend||this.options.resources&&!this.options.partialBundledLanguages||i(n,e)&&(!r||i(o,e)))}loadNamespaces(e,t){let n=o();return this.options.ns?(r(e)&&(e=[e]),e.forEach(e=>{0>this.options.ns.indexOf(e)&&this.options.ns.push(e)}),this.loadResources(e=>{n.resolve(),t&&t(e)}),n):(t&&t(),Promise.resolve())}loadLanguages(e,t){let n=o();r(e)&&(e=[e]);let i=this.options.preload||[],a=e.filter(e=>0>i.indexOf(e)&&this.services.languageUtils.isSupportedCode(e));return a.length?(this.options.preload=i.concat(a),this.loadResources(e=>{n.resolve(),t&&t(e)}),n):(t&&t(),Promise.resolve())}dir(e){return(e||(e=this.resolvedLanguage||(this.languages?.length>0?this.languages[0]:this.language)),e)?["ar","shu","sqr","ssh","xaa","yhd","yud","aao","abh","abv","acm","acq","acw","acx","acy","adf","ads","aeb","aec","afb","ajp","apc","apd","arb","arq","ars","ary","arz","auz","avl","ayh","ayl","ayn","ayp","bbz","pga","he","iw","ps","pbt","pbu","pst","prp","prd","ug","ur","ydd","yds","yih","ji","yi","hbo","men","xmn","fa","jpr","peo","pes","prs","dv","sam","ckb"].indexOf((this.services?.languageUtils||new N(K())).getLanguagePartFromCode(e))>-1||e.toLowerCase().indexOf("-arab")>1?"rtl":"ltr":"rtl"}static createInstance(e={},t){return new J(e,t)}cloneInstance(e={},t=X){let n=e.forkResourceStore;n&&delete e.forkResourceStore;let r={...this.options,...e,isClone:!0},o=new J(r);return(void 0!==e.debug||void 0!==e.prefix)&&(o.logger=o.logger.clone(e)),["store","services","language"].forEach(e=>{o[e]=this[e]}),o.services={...this.services},o.services.utils={hasLoadedNamespace:o.hasLoadedNamespace.bind(o)},n&&(o.store=new k(Object.keys(this.store.data).reduce((e,t)=>(e[t]={...this.store.data[t]},e[t]=Object.keys(e[t]).reduce((n,r)=>(n[r]={...e[t][r]},n),e[t]),e),{}),r),o.services.resourceStore=o.store),o.translator=new M(o.services,r),o.translator.on("*",(e,...t)=>{o.emit(e,...t)}),o.init(r,t),o.translator.options=r,o.translator.backendConnector.services.utils={hasLoadedNamespace:o.hasLoadedNamespace.bind(o)},o}toJSON(){return{options:this.options,store:this.store,language:this.language,languages:this.languages,resolvedLanguage:this.resolvedLanguage}}}let G=J.createInstance();G.createInstance=J.createInstance,G.createInstance,G.dir,G.init,G.loadResources,G.reloadResources,G.use,G.changeLanguage,G.getFixedT,G.t,G.exists,G.setDefaultNamespace,G.hasLoadedNamespace,G.loadNamespaces,G.loadLanguages},59382:function(e,t,n){"use strict";let r;n.d(t,{Db:()=>p,$G:()=>k});var o=n(2784);n(64896),Object.create(null);let i=/&(?:amp|#38|lt|#60|gt|#62|apos|#39|quot|#34|nbsp|#160|copy|#169|reg|#174|hellip|#8230|#x2F|#47);/g,a={"&amp;":"&","&#38;":"&","&lt;":"<","&#60;":"<","&gt;":">","&#62;":">","&apos;":"'","&#39;":"'","&quot;":'"',"&#34;":'"',"&nbsp;":" ","&#160;":" ","&copy;":"\xa9","&#169;":"\xa9","&reg;":"\xae","&#174;":"\xae","&hellip;":"…","&#8230;":"…","&#x2F;":"/","&#47;":"/"},s=e=>a[e],l={bindI18n:"languageChanged",bindI18nStore:"",transEmptyNodeValue:"",transSupportBasicHtmlNodes:!0,transWrapTextNodes:"",transKeepBasicHtmlNodesFor:["br","strong","i","p"],useSuspense:!0,unescape:e=>e.replace(i,s)},u=(e={})=>{l={...l,...e}},c=()=>l,d=e=>{r=e},f=()=>r,p={type:"3rdParty",init(e){u(e.options.react),d(e)}},h=(0,o.createContext)();class g{constructor(){this.usedNamespaces={}}addUsedNamespaces(e){e.forEach(e=>{this.usedNamespaces[e]||(this.usedNamespaces[e]=!0)})}getUsedNamespaces(){return Object.keys(this.usedNamespaces)}}let m=(e,t,n,r)=>{let o=[n,{code:t,...r||{}}];if(e?.services?.logger?.forward)return e.services.logger.forward(o,"warn","react-i18next::",!0);O(o[0])&&(o[0]=`react-i18next:: ${o[0]}`),e?.services?.logger?.warn?e.services.logger.warn(...o):console?.warn&&console.warn(...o)},y={},v=(e,t,n,r)=>{O(n)&&y[n]||(O(n)&&(y[n]=new Date),m(e,t,n,r))},b=(e,t)=>()=>{if(e.isInitialized)t();else{let n=()=>{setTimeout(()=>{e.off("initialized",n)},0),t()};e.on("initialized",n)}},w=(e,t,n)=>{e.loadNamespaces(t,b(e,n))},x=(e,t,n,r)=>{if(O(n)&&(n=[n]),e.options.preload&&e.options.preload.indexOf(t)>-1)return w(e,n,r);n.forEach(t=>{0>e.options.ns.indexOf(t)&&e.options.ns.push(t)}),e.loadLanguages(t,b(e,r))},S=(e,t,n={})=>t.languages&&t.languages.length?t.hasLoadedNamespace(e,{lng:n.lng,precheck:(t,r)=>{if(n.bindI18n?.indexOf("languageChanging")>-1&&t.services.backendConnector.backend&&t.isLanguageChangingTo&&!r(t.isLanguageChangingTo,e))return!1}}):(v(t,"NO_LANGUAGES","i18n.languages were undefined or empty",{languages:t.languages}),!0),O=e=>"string"==typeof e,E=e=>"object"==typeof e&&null!==e,C=(e,t)=>{let n=(0,o.useRef)();return(0,o.useEffect)(()=>{n.current=t?n.current:e},[e,t]),n.current},R=(e,t,n,r)=>e.getFixedT(t,n,r),P=(e,t,n,r)=>(0,o.useCallback)(R(e,t,n,r),[e,t,n,r]),k=(e,t={})=>{let{i18n:n}=t,{i18n:r,defaultNS:i}=(0,o.useContext)(h)||{},a=n||r||f();if(a&&!a.reportNamespaces&&(a.reportNamespaces=new g),!a){v(a,"NO_I18NEXT_INSTANCE","useTranslation: You will need to pass in an i18next instance by using initReactI18next");let e=(e,t)=>O(t)?t:E(t)&&O(t.defaultValue)?t.defaultValue:Array.isArray(e)?e[e.length-1]:e,t=[e,{},!1];return t.t=e,t.i18n={},t.ready=!1,t}a.options.react?.wait&&v(a,"DEPRECATED_OPTION","useTranslation: It seems you are still using the old wait option, you may migrate to the new useSuspense behaviour.");let s={...c(),...a.options.react,...t},{useSuspense:l,keyPrefix:u}=s,d=e||i||a.options?.defaultNS;d=O(d)?[d]:d||["translation"],a.reportNamespaces.addUsedNamespaces?.(d);let p=(a.isInitialized||a.initializedStoreOnce)&&d.every(e=>S(e,a,s)),m=P(a,t.lng||null,"fallback"===s.nsMode?d:d[0],u),y=()=>m,b=()=>R(a,t.lng||null,"fallback"===s.nsMode?d:d[0],u),[k,j]=(0,o.useState)(y),T=d.join();t.lng&&(T=`${t.lng}${T}`);let L=C(T),M=(0,o.useRef)(!0);(0,o.useEffect)(()=>{let{bindI18n:e,bindI18nStore:n}=s;M.current=!0,p||l||(t.lng?x(a,t.lng,d,()=>{M.current&&j(b)}):w(a,d,()=>{M.current&&j(b)})),p&&L&&L!==T&&M.current&&j(b);let r=()=>{M.current&&j(b)};return e&&a?.on(e,r),n&&a?.store.on(n,r),()=>{M.current=!1,a&&e?.split(" ").forEach(e=>a.off(e,r)),n&&a&&n.split(" ").forEach(e=>a.store.off(e,r))}},[a,T]),(0,o.useEffect)(()=>{M.current&&p&&j(y)},[a,u,p]);let N=[k,a,p];if(N.t=k,N.i18n=a,N.ready=p,p||!p&&!l)return N;throw new Promise(e=>{t.lng?x(a,t.lng,d,()=>e()):w(a,d,()=>e())})}},44965:function(e,t,n){"use strict";n.d(t,{Z:()=>r});function r(e,t){if(!e)throw Error("Invariant failed")}},9210:function(e,t,n){"use strict";n.d(t,{Ue:()=>f});let r=e=>{let t,n=new Set,r=(e,r)=>{let o="function"==typeof e?e(t):e;if(!Object.is(o,t)){let e=t;t=(null!=r?r:"object"!=typeof o||null===o)?o:Object.assign({},t,o),n.forEach(n=>n(t,e))}},o=()=>t,i={setState:r,getState:o,getInitialState:()=>a,subscribe:e=>(n.add(e),()=>n.delete(e)),destroy:()=>{console.warn("[DEPRECATED] The `destroy` method will be unsupported in a future version. Instead use unsubscribe function returned by subscribe. Everything will be garbage-collected if store is garbage-collected."),n.clear()}},a=t=e(r,o,i);return i},o=e=>e?r(e):r;var i=n(2784),a=n(41110);let{useDebugValue:s}=i,{useSyncExternalStoreWithSelector:l}=a,u=!1,c=e=>e,d=e=>{"function"!=typeof e&&console.warn("[DEPRECATED] Passing a vanilla store will be unsupported in a future version. Instead use `import { useStore } from 'zustand'`.");let t="function"==typeof e?o(e):e,n=(e,n)=>(function(e,t=c,n){n&&!u&&(console.warn("[DEPRECATED] Use `createWithEqualityFn` instead of `create` or use `useStoreWithEqualityFn` instead of `useStore`. They can be imported from 'zustand/traditional'. https://github.com/pmndrs/zustand/discussions/1937"),u=!0);let r=l(e.subscribe,e.getState,e.getServerState||e.getInitialState,t,n);return s(r),r})(t,e,n);return Object.assign(n,t),n},f=e=>e?d(e):d},85330:function(e,t,n){"use strict";n.d(t,{tJ:()=>a});let r=e=>t=>{try{let n=e(t);if(n instanceof Promise)return n;return{then:e=>r(e)(n),catch(e){return this}}}catch(e){return{then(e){return this},catch:t=>r(t)(e)}}},o=(e,t)=>(n,o,i)=>{let a,s,l={getStorage:()=>localStorage,serialize:JSON.stringify,deserialize:JSON.parse,partialize:e=>e,version:0,merge:(e,t)=>({...t,...e}),...t},u=!1,c=new Set,d=new Set;try{a=l.getStorage()}catch(e){}if(!a)return e((...e)=>{console.warn(`[zustand persist middleware] Unable to update item '${l.name}', the given storage is currently unavailable.`),n(...e)},o,i);let f=r(l.serialize),p=()=>{let e,t=f({state:l.partialize({...o()}),version:l.version}).then(e=>a.setItem(l.name,e)).catch(t=>{e=t});if(e)throw e;return t},h=i.setState;i.setState=(e,t)=>{h(e,t),p()};let g=e((...e)=>{n(...e),p()},o,i),m=()=>{var e;if(!a)return;u=!1,c.forEach(e=>e(o()));let t=(null==(e=l.onRehydrateStorage)?void 0:e.call(l,o()))||void 0;return r(a.getItem.bind(a))(l.name).then(e=>{if(e)return l.deserialize(e)}).then(e=>{if(e)if("number"!=typeof e.version||e.version===l.version)return e.state;else{if(l.migrate)return l.migrate(e.state,e.version);console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}}).then(e=>{var t;return n(s=l.merge(e,null!=(t=o())?t:g),!0),p()}).then(()=>{null==t||t(s,void 0),u=!0,d.forEach(e=>e(s))}).catch(e=>{null==t||t(void 0,e)})};return i.persist={setOptions:e=>{l={...l,...e},e.getStorage&&(a=e.getStorage())},clearStorage:()=>{null==a||a.removeItem(l.name)},getOptions:()=>l,rehydrate:()=>m(),hasHydrated:()=>u,onHydrate:e=>(c.add(e),()=>{c.delete(e)}),onFinishHydration:e=>(d.add(e),()=>{d.delete(e)})},m(),s||g},i=(e,t)=>(n,o,i)=>{let a,s={storage:function(e,t){let n;try{n=e()}catch(e){return}return{getItem:e=>{var t;let r=e=>null===e?null:JSON.parse(e,void 0),o=null!=(t=n.getItem(e))?t:null;return o instanceof Promise?o.then(r):r(o)},setItem:(e,t)=>n.setItem(e,JSON.stringify(t,void 0)),removeItem:e=>n.removeItem(e)}}(()=>localStorage),partialize:e=>e,version:0,merge:(e,t)=>({...t,...e}),...t},l=!1,u=new Set,c=new Set,d=s.storage;if(!d)return e((...e)=>{console.warn(`[zustand persist middleware] Unable to update item '${s.name}', the given storage is currently unavailable.`),n(...e)},o,i);let f=()=>{let e=s.partialize({...o()});return d.setItem(s.name,{state:e,version:s.version})},p=i.setState;i.setState=(e,t)=>{p(e,t),f()};let h=e((...e)=>{n(...e),f()},o,i);i.getInitialState=()=>h;let g=()=>{var e,t;if(!d)return;l=!1,u.forEach(e=>{var t;return e(null!=(t=o())?t:h)});let i=(null==(t=s.onRehydrateStorage)?void 0:t.call(s,null!=(e=o())?e:h))||void 0;return r(d.getItem.bind(d))(s.name).then(e=>{if(e)if("number"!=typeof e.version||e.version===s.version)return[!1,e.state];else{if(s.migrate)return[!0,s.migrate(e.state,e.version)];console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}return[!1,void 0]}).then(e=>{var t;let[r,i]=e;if(n(a=s.merge(i,null!=(t=o())?t:h),!0),r)return f()}).then(()=>{null==i||i(a,void 0),a=o(),l=!0,c.forEach(e=>e(a))}).catch(e=>{null==i||i(void 0,e)})};return i.persist={setOptions:e=>{s={...s,...e},e.storage&&(d=e.storage)},clearStorage:()=>{null==d||d.removeItem(s.name)},getOptions:()=>s,rehydrate:()=>g(),hasHydrated:()=>l,onHydrate:e=>(u.add(e),()=>{u.delete(e)}),onFinishHydration:e=>(c.add(e),()=>{c.delete(e)})},s.skipHydration||g(),a||h},a=(e,t)=>"getStorage"in t||"serialize"in t||"deserialize"in t?(console.warn("[DEPRECATED] `getStorage`, `serialize` and `deserialize` options are deprecated. Use `storage` option instead."),o(e,t)):i(e,t)}}]);