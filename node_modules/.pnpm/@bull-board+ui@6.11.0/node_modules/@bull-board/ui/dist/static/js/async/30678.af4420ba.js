(self.webpackChunk_bull_board_ui=self.webpackChunk_bull_board_ui||[]).push([["30678"],{22348:function(e,a){"use strict";Object.defineProperty(a,"__esModule",{value:!0}),a.default=function(e){return function(){var a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=a.width?String(a.width):e.defaultWidth;return e.formats[t]||e.formats[e.defaultWidth]}},e.exports=a.default},84738:function(e,a){"use strict";Object.defineProperty(a,"__esModule",{value:!0}),a.default=function(e){return function(a,t){var n;if("formatting"===(null!=t&&t.context?String(t.context):"standalone")&&e.formattingValues){var i=e.defaultFormattingWidth||e.defaultWidth,r=null!=t&&t.width?String(t.width):i;n=e.formattingValues[r]||e.formattingValues[i]}else{var d=e.defaultWidth,o=null!=t&&t.width?String(t.width):e.defaultWidth;n=e.values[o]||e.values[d]}return n[e.argumentCallback?e.argumentCallback(a):a]}},e.exports=a.default},89653:function(e,a){"use strict";Object.defineProperty(a,"__esModule",{value:!0}),a.default=function(e){return function(a){var t,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=n.width,r=i&&e.matchPatterns[i]||e.matchPatterns[e.defaultMatchWidth],d=a.match(r);if(!d)return null;var o=d[0],u=i&&e.parsePatterns[i]||e.parsePatterns[e.defaultParseWidth],l=Array.isArray(u)?function(e,a){for(var t=0;t<e.length;t++)if(a(e[t]))return t}(u,function(e){return e.test(o)}):function(e,a){for(var t in e)if(e.hasOwnProperty(t)&&a(e[t]))return t}(u,function(e){return e.test(o)});return t=e.valueCallback?e.valueCallback(l):l,{value:t=n.valueCallback?n.valueCallback(t):t,rest:a.slice(o.length)}}},e.exports=a.default},71604:function(e,a){"use strict";Object.defineProperty(a,"__esModule",{value:!0}),a.default=function(e){return function(a){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=a.match(e.matchPattern);if(!n)return null;var i=n[0],r=a.match(e.parsePattern);if(!r)return null;var d=e.valueCallback?e.valueCallback(r[0]):r[0];return{value:d=t.valueCallback?t.valueCallback(d):d,rest:a.slice(i.length)}}},e.exports=a.default},33959:function(e,a){"use strict";Object.defineProperty(a,"__esModule",{value:!0}),a.default=void 0;var t={lessThanXSeconds:{one:"mwens pase yon segond",other:"mwens pase {{count}} segond"},xSeconds:{one:"1 segond",other:"{{count}} segond"},halfAMinute:"30 segond",lessThanXMinutes:{one:"mwens pase yon minit",other:"mwens pase {{count}} minit"},xMinutes:{one:"1 minit",other:"{{count}} minit"},aboutXHours:{one:"anviwon in\xe8",other:"anviwon {{count}} \xe8"},xHours:{one:"1 l\xe8",other:"{{count}} l\xe8"},xDays:{one:"1 jou",other:"{{count}} jou"},aboutXWeeks:{one:"anviwon 1 sem\xe8n",other:"anviwon {{count}} sem\xe8n"},xWeeks:{one:"1 sem\xe8n",other:"{{count}} sem\xe8n"},aboutXMonths:{one:"anviwon 1 mwa",other:"anviwon {{count}} mwa"},xMonths:{one:"1 mwa",other:"{{count}} mwa"},aboutXYears:{one:"anviwon 1 an",other:"anviwon {{count}} an"},xYears:{one:"1 an",other:"{{count}} an"},overXYears:{one:"plis pase 1 an",other:"plis pase {{count}} an"},almostXYears:{one:"pr\xe8ske 1 an",other:"pr\xe8ske {{count}} an"}};a.default=function(e,a,n){var i,r=t[e];if(i="string"==typeof r?r:1===a?r.one:r.other.replace("{{count}}",String(a)),null!=n&&n.addSuffix)if(n.comparison&&n.comparison>0)return"nan "+i;else return"sa f\xe8 "+i;return i},e.exports=a.default},940:function(e,a,t){"use strict";var n=t(15755).default;Object.defineProperty(a,"__esModule",{value:!0}),a.default=void 0;var i=n(t(22348));a.default={date:(0,i.default)({formats:{full:"EEEE d MMMM y",long:"d MMMM y",medium:"d MMM y",short:"dd/MM/y"},defaultWidth:"full"}),time:(0,i.default)({formats:{full:"HH:mm:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"HH:mm"},defaultWidth:"full"}),dateTime:(0,i.default)({formats:{full:"{{date}} 'nan l\xe8' {{time}}",long:"{{date}} 'nan l\xe8' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},defaultWidth:"full"})},e.exports=a.default},83050:function(e,a){"use strict";Object.defineProperty(a,"__esModule",{value:!0}),a.default=void 0;var t={lastWeek:"eeee 'pase nan l\xe8' p",yesterday:"'y\xe8 nan l\xe8' p",today:"'jodi a' p",tomorrow:"'demen nan l\xe8' p'",nextWeek:"eeee 'pwochen nan l\xe8' p",other:"P"};a.default=function(e,a,n,i){return t[e]},e.exports=a.default},63030:function(e,a,t){"use strict";var n=t(15755).default;Object.defineProperty(a,"__esModule",{value:!0}),a.default=void 0;var i=n(t(84738));a.default={ordinalNumber:function(e,a){var t=Number(e);return 0===t?String(t):t+(1===t?"ye":"y\xe8m")},era:(0,i.default)({values:{narrow:["av. J.-K","ap. J.-K"],abbreviated:["av. J.-K","ap. J.-K"],wide:["anvan Jezi Kris","apre Jezi Kris"]},defaultWidth:"wide"}),quarter:(0,i.default)({values:{narrow:["T1","T2","T3","T4"],abbreviated:["1ye trim.","2y\xe8m trim.","3y\xe8m trim.","4y\xe8m trim."],wide:["1ye trim\xe8s","2y\xe8m trim\xe8s","3y\xe8m trim\xe8s","4y\xe8m trim\xe8s"]},defaultWidth:"wide",argumentCallback:function(e){return e-1}}),month:(0,i.default)({values:{narrow:["J","F","M","A","M","J","J","O","S","O","N","D"],abbreviated:["janv.","fevr.","mas","avr.","me","jen","jiy\xe8","out","sept.","okt.","nov.","des."],wide:["janvye","fevrye","mas","avril","me","jen","jiy\xe8","out","septanm","okt\xf2b","novanm","desanm"]},defaultWidth:"wide"}),day:(0,i.default)({values:{narrow:["D","L","M","M","J","V","S"],short:["di","le","ma","m\xe8","je","va","sa"],abbreviated:["dim.","len.","mad.","m\xe8k.","jed.","van.","sam."],wide:["dimanch","lendi","madi","m\xe8kredi","jedi","vandredi","samdi"]},defaultWidth:"wide"}),dayPeriod:(0,i.default)({values:{narrow:{am:"AM",pm:"PM",midnight:"minwit",noon:"midi",morning:"mat.",afternoon:"ap.m.",evening:"swa",night:"mat."},abbreviated:{am:"AM",pm:"PM",midnight:"minwit",noon:"midi",morning:"maten",afternoon:"apr\xe8midi",evening:"swa",night:"maten"},wide:{am:"AM",pm:"PM",midnight:"minwit",noon:"midi",morning:"nan maten",afternoon:"nan apr\xe8midi",evening:"nan asw\xe8",night:"nan maten"}},defaultWidth:"wide"})},e.exports=a.default},15632:function(e,a,t){"use strict";var n=t(15755).default;Object.defineProperty(a,"__esModule",{value:!0}),a.default=void 0;var i=n(t(89653));a.default={ordinalNumber:(0,n(t(71604)).default)({matchPattern:/^(\d+)(ye|yèm)?/i,parsePattern:/\d+/i,valueCallback:function(e){return parseInt(e,10)}}),era:(0,i.default)({matchPatterns:{narrow:/^(av\.J\.K|ap\.J\.K|ap\.J\.-K)/i,abbreviated:/^(av\.J\.-K|av\.J-K|apr\.J\.-K|apr\.J-K|ap\.J-K)/i,wide:/^(avan Jezi Kris|apre Jezi Kris)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^av/i,/^ap/i]},defaultParseWidth:"any"}),quarter:(0,i.default)({matchPatterns:{narrow:/^[1234]/i,abbreviated:/^t[1234]/i,wide:/^[1234](ye|yèm)? trimès/i},defaultMatchWidth:"wide",parsePatterns:{any:[/1/i,/2/i,/3/i,/4/i]},defaultParseWidth:"any",valueCallback:function(e){return e+1}}),month:(0,i.default)({matchPatterns:{narrow:/^[jfmasond]/i,abbreviated:/^(janv|fevr|mas|avr|me|jen|jiyè|out|sept|okt|nov|des)\.?/i,wide:/^(janvye|fevrye|mas|avril|me|jen|jiyè|out|septanm|oktòb|novanm|desanm)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^o/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^ma/i,/^av/i,/^me/i,/^je/i,/^ji/i,/^ou/i,/^s/i,/^ok/i,/^n/i,/^d/i]},defaultParseWidth:"any"}),day:(0,i.default)({matchPatterns:{narrow:/^[lmjvsd]/i,short:/^(di|le|ma|me|je|va|sa)/i,abbreviated:/^(dim|len|mad|mèk|jed|van|sam)\.?/i,wide:/^(dimanch|lendi|madi|mèkredi|jedi|vandredi|samdi)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^d/i,/^l/i,/^m/i,/^m/i,/^j/i,/^v/i,/^s/i],any:[/^di/i,/^le/i,/^ma/i,/^mè/i,/^je/i,/^va/i,/^sa/i]},defaultParseWidth:"any"}),dayPeriod:(0,i.default)({matchPatterns:{narrow:/^(a|p|minwit|midi|mat\.?|ap\.?m\.?|swa)/i,any:/^([ap]\.?\s?m\.?|nan maten|nan aprèmidi|nan aswè)/i},defaultMatchWidth:"any",parsePatterns:{any:{am:/^a/i,pm:/^p/i,midnight:/^min/i,noon:/^mid/i,morning:/mat/i,afternoon:/ap/i,evening:/sw/i,night:/nwit/i}},defaultParseWidth:"any"})},e.exports=a.default},73797:function(e,a,t){"use strict";var n=t(15755).default;Object.defineProperty(a,"__esModule",{value:!0}),a.default=void 0;var i=n(t(33959)),r=n(t(940)),d=n(t(83050)),o=n(t(63030)),u=n(t(15632));a.default={code:"ht",formatDistance:i.default,formatLong:r.default,formatRelative:d.default,localize:o.default,match:u.default,options:{weekStartsOn:1,firstWeekContainsDate:4}},e.exports=a.default},15755:function(e){e.exports=function(e){return e&&e.__esModule?e:{default:e}},e.exports.__esModule=!0,e.exports.default=e.exports}}]);