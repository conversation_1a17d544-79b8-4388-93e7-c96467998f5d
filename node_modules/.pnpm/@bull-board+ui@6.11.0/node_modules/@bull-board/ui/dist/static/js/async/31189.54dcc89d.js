"use strict";(self.webpackChunk_bull_board_ui=self.webpackChunk_bull_board_ui||[]).push([["31189"],{60967:function(e,n){Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var o={lessThanXSeconds:{standalone:{one:"manner w\xe9i eng Sekonn",other:"manner w\xe9i {{count}} Sekonnen"},withPreposition:{one:"manner w\xe9i enger Sekonn",other:"manner w\xe9i {{count}} Sekonnen"}},xSeconds:{standalone:{one:"eng <PERSON>konn",other:"{{count}} Sekonnen"},withPreposition:{one:"enger Sekonn",other:"{{count}} Sekonnen"}},halfAMinute:{standalone:"eng hallef Minutt",withPreposition:"enger hallwer Minutt"},lessThanXMinutes:{standalone:{one:"manner w\xe9i eng Minutt",other:"manner w\xe9i {{count}} Minutten"},withPreposition:{one:"manner w\xe9i enger Minutt",other:"manner w\xe9i {{count}} Minutten"}},xMinutes:{standalone:{one:"eng Minutt",other:"{{count}} Minutten"},withPreposition:{one:"enger Minutt",other:"{{count}} Minutten"}},aboutXHours:{standalone:{one:"ongef\xe9ier eng Stonn",other:"ongef\xe9ier {{count}} Stonnen"},withPreposition:{one:"ongef\xe9ier enger Stonn",other:"ongef\xe9ier {{count}} Stonnen"}},xHours:{standalone:{one:"eng Stonn",other:"{{count}} Stonnen"},withPreposition:{one:"enger Stonn",other:"{{count}} Stonnen"}},xDays:{standalone:{one:"een Dag",other:"{{count}} Deeg"},withPreposition:{one:"engem Dag",other:"{{count}} Deeg"}},aboutXWeeks:{standalone:{one:"ongef\xe9ier eng Woch",other:"ongef\xe9ier {{count}} Wochen"},withPreposition:{one:"ongef\xe9ier enger Woche",other:"ongef\xe9ier {{count}} Wochen"}},xWeeks:{standalone:{one:"eng Woch",other:"{{count}} Wochen"},withPreposition:{one:"enger Woch",other:"{{count}} Wochen"}},aboutXMonths:{standalone:{one:"ongef\xe9ier ee Mount",other:"ongef\xe9ier {{count}} M\xe9int"},withPreposition:{one:"ongef\xe9ier engem Mount",other:"ongef\xe9ier {{count}} M\xe9int"}},xMonths:{standalone:{one:"ee Mount",other:"{{count}} M\xe9int"},withPreposition:{one:"engem Mount",other:"{{count}} M\xe9int"}},aboutXYears:{standalone:{one:"ongef\xe9ier ee Joer",other:"ongef\xe9ier {{count}} Joer"},withPreposition:{one:"ongef\xe9ier engem Joer",other:"ongef\xe9ier {{count}} Joer"}},xYears:{standalone:{one:"ee Joer",other:"{{count}} Joer"},withPreposition:{one:"engem Joer",other:"{{count}} Joer"}},overXYears:{standalone:{one:"m\xe9i w\xe9i ee Joer",other:"m\xe9i w\xe9i {{count}} Joer"},withPreposition:{one:"m\xe9i w\xe9i engem Joer",other:"m\xe9i w\xe9i {{count}} Joer"}},almostXYears:{standalone:{one:"bal ee Joer",other:"bal {{count}} Joer"},withPreposition:{one:"bal engem Joer",other:"bal {{count}} Joer"}}},t=["d","h","n","t","z"],r=["a,","e","i","o","u"],i=[0,1,2,3,8,9],a=[40,50,60,70];function u(e){var n=e.charAt(0).toLowerCase();if(-1!=r.indexOf(n)||-1!=t.indexOf(n))return!0;var o=e.split(" ")[0],u=parseInt(o);return!isNaN(u)&&-1!=i.indexOf(u%10)&&-1==a.indexOf(parseInt(o.substring(0,2)))}n.default=function(e,n,t){var r,i=o[e],a=null!=t&&t.addSuffix?i.withPreposition:i.standalone;if(r="string"==typeof a?a:1===n?a.one:a.other.replace("{{count}}",String(n)),null!=t&&t.addSuffix)if(t.comparison&&t.comparison>0)return"a"+(u(r)?"n":"")+" "+r;else return"viru"+(u(r)?"n":"")+" "+r;return r},e.exports=n.default}}]);