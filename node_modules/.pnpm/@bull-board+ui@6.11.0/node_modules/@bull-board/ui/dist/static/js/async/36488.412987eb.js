"use strict";(self.webpackChunk_bull_board_ui=self.webpackChunk_bull_board_ui||[]).push([["36488"],{86626:function(t,e,n){n.d(e,{Z:()=>E});var r=n(95300),a=n(19785),i=n(66700);function o(t){if(null===t||!0===t||!1===t)return NaN;var e=Number(t);return isNaN(e)?e:e<0?Math.ceil(e):Math.floor(e)}function u(t){(0,a.Z)(1,arguments);var e=(0,i.Z)(t),n=e.getUTCDay();return e.setUTCDate(e.getUTCDate()-(7*(n<1)+n-1)),e.setUTCHours(0,0,0,0),e}function l(t){(0,a.Z)(1,arguments);var e=(0,i.Z)(t),n=e.getUTCFullYear(),r=new Date(0);r.setUTCFullYear(n+1,0,4),r.setUTCHours(0,0,0,0);var o=u(r),l=new Date(0);l.setUTCFullYear(n,0,4),l.setUTCHours(0,0,0,0);var s=u(l);return e.getTime()>=o.getTime()?n+1:e.getTime()>=s.getTime()?n:n-1}var s=n(18667);function c(t,e){(0,a.Z)(1,arguments);var n,r,u,l,c,d,f,h,g=(0,s.j)(),m=o(null!=(n=null!=(r=null!=(u=null!=(l=null==e?void 0:e.weekStartsOn)?l:null==e||null==(c=e.locale)||null==(d=c.options)?void 0:d.weekStartsOn)?u:g.weekStartsOn)?r:null==(f=g.locale)||null==(h=f.options)?void 0:h.weekStartsOn)?n:0);if(!(m>=0&&m<=6))throw RangeError("weekStartsOn must be between 0 and 6 inclusively");var w=(0,i.Z)(t),b=w.getUTCDay();return w.setUTCDate(w.getUTCDate()-(7*(b<m)+b-m)),w.setUTCHours(0,0,0,0),w}function d(t,e){(0,a.Z)(1,arguments);var n,r,u,l,d,f,h,g,m=(0,i.Z)(t),w=m.getUTCFullYear(),b=(0,s.j)(),v=o(null!=(n=null!=(r=null!=(u=null!=(l=null==e?void 0:e.firstWeekContainsDate)?l:null==e||null==(d=e.locale)||null==(f=d.options)?void 0:f.firstWeekContainsDate)?u:b.firstWeekContainsDate)?r:null==(h=b.locale)||null==(g=h.options)?void 0:g.firstWeekContainsDate)?n:1);if(!(v>=1&&v<=7))throw RangeError("firstWeekContainsDate must be between 1 and 7 inclusively");var T=new Date(0);T.setUTCFullYear(w+1,0,v),T.setUTCHours(0,0,0,0);var C=c(T,e),p=new Date(0);p.setUTCFullYear(w,0,v),p.setUTCHours(0,0,0,0);var y=c(p,e);return m.getTime()>=C.getTime()?w+1:m.getTime()>=y.getTime()?w:w-1}function f(t,e){for(var n=Math.abs(t).toString();n.length<e;)n="0"+n;return(t<0?"-":"")+n}let h={y:function(t,e){var n=t.getUTCFullYear(),r=n>0?n:1-n;return f("yy"===e?r%100:r,e.length)},M:function(t,e){var n=t.getUTCMonth();return"M"===e?String(n+1):f(n+1,2)},d:function(t,e){return f(t.getUTCDate(),e.length)},h:function(t,e){return f(t.getUTCHours()%12||12,e.length)},H:function(t,e){return f(t.getUTCHours(),e.length)},m:function(t,e){return f(t.getUTCMinutes(),e.length)},s:function(t,e){return f(t.getUTCSeconds(),e.length)},S:function(t,e){var n=e.length;return f(Math.floor(t.getUTCMilliseconds()*Math.pow(10,n-3)),e.length)}};var g={midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"};function m(t,e){var n=t>0?"-":"+",r=Math.abs(t),a=Math.floor(r/60),i=r%60;return 0===i?n+String(a):n+String(a)+(e||"")+f(i,2)}function w(t,e){return t%60==0?(t>0?"-":"+")+f(Math.abs(t)/60,2):b(t,e)}function b(t,e){var n=Math.abs(t);return(t>0?"-":"+")+f(Math.floor(n/60),2)+(e||"")+f(n%60,2)}let v={G:function(t,e,n){var r=+(t.getUTCFullYear()>0);switch(e){case"G":case"GG":case"GGG":return n.era(r,{width:"abbreviated"});case"GGGGG":return n.era(r,{width:"narrow"});default:return n.era(r,{width:"wide"})}},y:function(t,e,n){if("yo"===e){var r=t.getUTCFullYear();return n.ordinalNumber(r>0?r:1-r,{unit:"year"})}return h.y(t,e)},Y:function(t,e,n,r){var a=d(t,r),i=a>0?a:1-a;return"YY"===e?f(i%100,2):"Yo"===e?n.ordinalNumber(i,{unit:"year"}):f(i,e.length)},R:function(t,e){return f(l(t),e.length)},u:function(t,e){return f(t.getUTCFullYear(),e.length)},Q:function(t,e,n){var r=Math.ceil((t.getUTCMonth()+1)/3);switch(e){case"Q":return String(r);case"QQ":return f(r,2);case"Qo":return n.ordinalNumber(r,{unit:"quarter"});case"QQQ":return n.quarter(r,{width:"abbreviated",context:"formatting"});case"QQQQQ":return n.quarter(r,{width:"narrow",context:"formatting"});default:return n.quarter(r,{width:"wide",context:"formatting"})}},q:function(t,e,n){var r=Math.ceil((t.getUTCMonth()+1)/3);switch(e){case"q":return String(r);case"qq":return f(r,2);case"qo":return n.ordinalNumber(r,{unit:"quarter"});case"qqq":return n.quarter(r,{width:"abbreviated",context:"standalone"});case"qqqqq":return n.quarter(r,{width:"narrow",context:"standalone"});default:return n.quarter(r,{width:"wide",context:"standalone"})}},M:function(t,e,n){var r=t.getUTCMonth();switch(e){case"M":case"MM":return h.M(t,e);case"Mo":return n.ordinalNumber(r+1,{unit:"month"});case"MMM":return n.month(r,{width:"abbreviated",context:"formatting"});case"MMMMM":return n.month(r,{width:"narrow",context:"formatting"});default:return n.month(r,{width:"wide",context:"formatting"})}},L:function(t,e,n){var r=t.getUTCMonth();switch(e){case"L":return String(r+1);case"LL":return f(r+1,2);case"Lo":return n.ordinalNumber(r+1,{unit:"month"});case"LLL":return n.month(r,{width:"abbreviated",context:"standalone"});case"LLLLL":return n.month(r,{width:"narrow",context:"standalone"});default:return n.month(r,{width:"wide",context:"standalone"})}},w:function(t,e,n,r){var u=function(t,e){(0,a.Z)(1,arguments);var n=(0,i.Z)(t);return Math.round((c(n,e).getTime()-(function(t,e){(0,a.Z)(1,arguments);var n,r,i,u,l,f,h,g,m=(0,s.j)(),w=o(null!=(n=null!=(r=null!=(i=null!=(u=null==e?void 0:e.firstWeekContainsDate)?u:null==e||null==(l=e.locale)||null==(f=l.options)?void 0:f.firstWeekContainsDate)?i:m.firstWeekContainsDate)?r:null==(h=m.locale)||null==(g=h.options)?void 0:g.firstWeekContainsDate)?n:1),b=d(t,e),v=new Date(0);return v.setUTCFullYear(b,0,w),v.setUTCHours(0,0,0,0),c(v,e)})(n,e).getTime())/6048e5)+1}(t,r);return"wo"===e?n.ordinalNumber(u,{unit:"week"}):f(u,e.length)},I:function(t,e,n){var r=function(t){(0,a.Z)(1,arguments);var e=(0,i.Z)(t);return Math.round((u(e).getTime()-(function(t){(0,a.Z)(1,arguments);var e=l(t),n=new Date(0);return n.setUTCFullYear(e,0,4),n.setUTCHours(0,0,0,0),u(n)})(e).getTime())/6048e5)+1}(t);return"Io"===e?n.ordinalNumber(r,{unit:"week"}):f(r,e.length)},d:function(t,e,n){return"do"===e?n.ordinalNumber(t.getUTCDate(),{unit:"date"}):h.d(t,e)},D:function(t,e,n){var r=function(t){(0,a.Z)(1,arguments);var e=(0,i.Z)(t),n=e.getTime();return e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0),Math.floor((n-e.getTime())/864e5)+1}(t);return"Do"===e?n.ordinalNumber(r,{unit:"dayOfYear"}):f(r,e.length)},E:function(t,e,n){var r=t.getUTCDay();switch(e){case"E":case"EE":case"EEE":return n.day(r,{width:"abbreviated",context:"formatting"});case"EEEEE":return n.day(r,{width:"narrow",context:"formatting"});case"EEEEEE":return n.day(r,{width:"short",context:"formatting"});default:return n.day(r,{width:"wide",context:"formatting"})}},e:function(t,e,n,r){var a=t.getUTCDay(),i=(a-r.weekStartsOn+8)%7||7;switch(e){case"e":return String(i);case"ee":return f(i,2);case"eo":return n.ordinalNumber(i,{unit:"day"});case"eee":return n.day(a,{width:"abbreviated",context:"formatting"});case"eeeee":return n.day(a,{width:"narrow",context:"formatting"});case"eeeeee":return n.day(a,{width:"short",context:"formatting"});default:return n.day(a,{width:"wide",context:"formatting"})}},c:function(t,e,n,r){var a=t.getUTCDay(),i=(a-r.weekStartsOn+8)%7||7;switch(e){case"c":return String(i);case"cc":return f(i,e.length);case"co":return n.ordinalNumber(i,{unit:"day"});case"ccc":return n.day(a,{width:"abbreviated",context:"standalone"});case"ccccc":return n.day(a,{width:"narrow",context:"standalone"});case"cccccc":return n.day(a,{width:"short",context:"standalone"});default:return n.day(a,{width:"wide",context:"standalone"})}},i:function(t,e,n){var r=t.getUTCDay(),a=0===r?7:r;switch(e){case"i":return String(a);case"ii":return f(a,e.length);case"io":return n.ordinalNumber(a,{unit:"day"});case"iii":return n.day(r,{width:"abbreviated",context:"formatting"});case"iiiii":return n.day(r,{width:"narrow",context:"formatting"});case"iiiiii":return n.day(r,{width:"short",context:"formatting"});default:return n.day(r,{width:"wide",context:"formatting"})}},a:function(t,e,n){var r=t.getUTCHours()/12>=1?"pm":"am";switch(e){case"a":case"aa":return n.dayPeriod(r,{width:"abbreviated",context:"formatting"});case"aaa":return n.dayPeriod(r,{width:"abbreviated",context:"formatting"}).toLowerCase();case"aaaaa":return n.dayPeriod(r,{width:"narrow",context:"formatting"});default:return n.dayPeriod(r,{width:"wide",context:"formatting"})}},b:function(t,e,n){var r,a=t.getUTCHours();switch(r=12===a?g.noon:0===a?g.midnight:a/12>=1?"pm":"am",e){case"b":case"bb":return n.dayPeriod(r,{width:"abbreviated",context:"formatting"});case"bbb":return n.dayPeriod(r,{width:"abbreviated",context:"formatting"}).toLowerCase();case"bbbbb":return n.dayPeriod(r,{width:"narrow",context:"formatting"});default:return n.dayPeriod(r,{width:"wide",context:"formatting"})}},B:function(t,e,n){var r,a=t.getUTCHours();switch(r=a>=17?g.evening:a>=12?g.afternoon:a>=4?g.morning:g.night,e){case"B":case"BB":case"BBB":return n.dayPeriod(r,{width:"abbreviated",context:"formatting"});case"BBBBB":return n.dayPeriod(r,{width:"narrow",context:"formatting"});default:return n.dayPeriod(r,{width:"wide",context:"formatting"})}},h:function(t,e,n){if("ho"===e){var r=t.getUTCHours()%12;return 0===r&&(r=12),n.ordinalNumber(r,{unit:"hour"})}return h.h(t,e)},H:function(t,e,n){return"Ho"===e?n.ordinalNumber(t.getUTCHours(),{unit:"hour"}):h.H(t,e)},K:function(t,e,n){var r=t.getUTCHours()%12;return"Ko"===e?n.ordinalNumber(r,{unit:"hour"}):f(r,e.length)},k:function(t,e,n){var r=t.getUTCHours();return(0===r&&(r=24),"ko"===e)?n.ordinalNumber(r,{unit:"hour"}):f(r,e.length)},m:function(t,e,n){return"mo"===e?n.ordinalNumber(t.getUTCMinutes(),{unit:"minute"}):h.m(t,e)},s:function(t,e,n){return"so"===e?n.ordinalNumber(t.getUTCSeconds(),{unit:"second"}):h.s(t,e)},S:function(t,e){return h.S(t,e)},X:function(t,e,n,r){var a=(r._originalDate||t).getTimezoneOffset();if(0===a)return"Z";switch(e){case"X":return w(a);case"XXXX":case"XX":return b(a);default:return b(a,":")}},x:function(t,e,n,r){var a=(r._originalDate||t).getTimezoneOffset();switch(e){case"x":return w(a);case"xxxx":case"xx":return b(a);default:return b(a,":")}},O:function(t,e,n,r){var a=(r._originalDate||t).getTimezoneOffset();switch(e){case"O":case"OO":case"OOO":return"GMT"+m(a,":");default:return"GMT"+b(a,":")}},z:function(t,e,n,r){var a=(r._originalDate||t).getTimezoneOffset();switch(e){case"z":case"zz":case"zzz":return"GMT"+m(a,":");default:return"GMT"+b(a,":")}},t:function(t,e,n,r){return f(Math.floor((r._originalDate||t).getTime()/1e3),e.length)},T:function(t,e,n,r){return f((r._originalDate||t).getTime(),e.length)}};var T=function(t,e){switch(t){case"P":return e.date({width:"short"});case"PP":return e.date({width:"medium"});case"PPP":return e.date({width:"long"});default:return e.date({width:"full"})}},C=function(t,e){switch(t){case"p":return e.time({width:"short"});case"pp":return e.time({width:"medium"});case"ppp":return e.time({width:"long"});default:return e.time({width:"full"})}};let p={p:C,P:function(t,e){var n,r=t.match(/(P+)(p+)?/)||[],a=r[1],i=r[2];if(!i)return T(t,e);switch(a){case"P":n=e.dateTime({width:"short"});break;case"PP":n=e.dateTime({width:"medium"});break;case"PPP":n=e.dateTime({width:"long"});break;default:n=e.dateTime({width:"full"})}return n.replace("{{date}}",T(a,e)).replace("{{time}}",C(i,e))}};var y=n(1645),D=["D","DD"],x=["YY","YYYY"];function U(t,e,n){if("YYYY"===t)throw RangeError("Use `yyyy` instead of `YYYY` (in `".concat(e,"`) for formatting years to the input `").concat(n,"`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md"));if("YY"===t)throw RangeError("Use `yy` instead of `YY` (in `".concat(e,"`) for formatting years to the input `").concat(n,"`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md"));if("D"===t)throw RangeError("Use `d` instead of `D` (in `".concat(e,"`) for formatting days of the month to the input `").concat(n,"`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md"));if("DD"===t)throw RangeError("Use `dd` instead of `DD` (in `".concat(e,"`) for formatting days of the month to the input `").concat(n,"`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md"))}var k=n(4725),M=/[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g,Y=/P+p+|P+|p+|''|'(''|[^'])+('|$)|./g,Z=/^'([^]*?)'?$/,N=/''/g,S=/[a-zA-Z]/;function E(t,e,n){(0,a.Z)(2,arguments);var u,l,c,d,f,h,g,m,w,b,T,C,E,O,P,H,R,q,L=String(e),W=(0,s.j)(),F=null!=(u=null!=(l=null==n?void 0:n.locale)?l:W.locale)?u:k.Z,z=o(null!=(c=null!=(d=null!=(f=null!=(h=null==n?void 0:n.firstWeekContainsDate)?h:null==n||null==(g=n.locale)||null==(m=g.options)?void 0:m.firstWeekContainsDate)?f:W.firstWeekContainsDate)?d:null==(w=W.locale)||null==(b=w.options)?void 0:b.firstWeekContainsDate)?c:1);if(!(z>=1&&z<=7))throw RangeError("firstWeekContainsDate must be between 1 and 7 inclusively");var G=o(null!=(T=null!=(C=null!=(E=null!=(O=null==n?void 0:n.weekStartsOn)?O:null==n||null==(P=n.locale)||null==(H=P.options)?void 0:H.weekStartsOn)?E:W.weekStartsOn)?C:null==(R=W.locale)||null==(q=R.options)?void 0:q.weekStartsOn)?T:0);if(!(G>=0&&G<=6))throw RangeError("weekStartsOn must be between 0 and 6 inclusively");if(!F.localize)throw RangeError("locale must contain localize property");if(!F.formatLong)throw RangeError("locale must contain formatLong property");var j=(0,i.Z)(t);if(!function(t){return(0,a.Z)(1,arguments),(!!function(t){return(0,a.Z)(1,arguments),t instanceof Date||"object"===(0,r.Z)(t)&&"[object Date]"===Object.prototype.toString.call(t)}(t)||"number"==typeof t)&&!isNaN(Number((0,i.Z)(t)))}(j))throw RangeError("Invalid time value");var _=(0,y.Z)(j),Q=function(t,e){return(0,a.Z)(2,arguments),function(t,e){return(0,a.Z)(2,arguments),new Date((0,i.Z)(t).getTime()+o(e))}(t,-o(e))}(j,_),B={firstWeekContainsDate:z,weekStartsOn:G,locale:F,_originalDate:j};return L.match(Y).map(function(t){var e=t[0];return"p"===e||"P"===e?(0,p[e])(t,F.formatLong):t}).join("").match(M).map(function(r){if("''"===r)return"'";var a,i,o=r[0];if("'"===o){return(i=(a=r).match(Z))?i[1].replace(N,"'"):a}var u=v[o];if(u)return null!=n&&n.useAdditionalWeekYearTokens||-1===x.indexOf(r)||U(r,e,String(t)),null!=n&&n.useAdditionalDayOfYearTokens||-1===D.indexOf(r)||U(r,e,String(t)),u(Q,r,F.localize,B);if(o.match(S))throw RangeError("Format string contains an unescaped latin alphabet character `"+o+"`");return r}).join("")}},31794:function(t,e,n){n.d(e,{Z:()=>i});var r=n(66700),a=n(19785);function i(t,e){(0,a.Z)(2,arguments);var n=(0,r.Z)(t),i=(0,r.Z)(e);return n.getFullYear()===i.getFullYear()}},89192:function(t,e,n){n.d(e,{Z:()=>o});var r=n(66700),a=n(19785);function i(t){(0,a.Z)(1,arguments);var e=(0,r.Z)(t);return e.setHours(0,0,0,0),e}function o(t){return(0,a.Z)(1,arguments),function(t,e){(0,a.Z)(2,arguments);var n=i(t),r=i(e);return n.getTime()===r.getTime()}(t,Date.now())}},17260:function(t,e,n){n.d(e,{VY:()=>U,fC:()=>x});var r=n(2784),a=n(56137),i=n(8210),o=n(69153),u=n(34896),l=n(59656),s=n(3436),c=n(42951),d=n(34890),f=n(52322),h="Collapsible",[g,m]=(0,i.b)(h),[w,b]=g(h),v=r.forwardRef((t,e)=>{let{__scopeCollapsible:n,open:a,defaultOpen:i,disabled:u,onOpenChange:l,...c}=t,[g,m]=(0,o.T)({prop:a,defaultProp:i??!1,onChange:l,caller:h});return(0,f.jsx)(w,{scope:n,disabled:u,contentId:(0,d.M)(),open:g,onOpenToggle:r.useCallback(()=>m(t=>!t),[m]),children:(0,f.jsx)(s.WV.div,{"data-state":D(g),"data-disabled":u?"":void 0,...c,ref:e})})});v.displayName=h;var T="CollapsibleTrigger";r.forwardRef((t,e)=>{let{__scopeCollapsible:n,...r}=t,i=b(T,n);return(0,f.jsx)(s.WV.button,{type:"button","aria-controls":i.contentId,"aria-expanded":i.open||!1,"data-state":D(i.open),"data-disabled":i.disabled?"":void 0,disabled:i.disabled,...r,ref:e,onClick:(0,a.M)(t.onClick,i.onOpenToggle)})}).displayName=T;var C="CollapsibleContent",p=r.forwardRef((t,e)=>{let{forceMount:n,...r}=t,a=b(C,t.__scopeCollapsible);return(0,f.jsx)(c.z,{present:n||a.open,children:({present:t})=>(0,f.jsx)(y,{...r,ref:e,present:t})})});p.displayName=C;var y=r.forwardRef((t,e)=>{let{__scopeCollapsible:n,present:a,children:i,...o}=t,c=b(C,n),[d,h]=r.useState(a),g=r.useRef(null),m=(0,l.e)(e,g),w=r.useRef(0),v=w.current,T=r.useRef(0),p=T.current,y=c.open||d,x=r.useRef(y),U=r.useRef(void 0);return r.useEffect(()=>{let t=requestAnimationFrame(()=>x.current=!1);return()=>cancelAnimationFrame(t)},[]),(0,u.b)(()=>{let t=g.current;if(t){U.current=U.current||{transitionDuration:t.style.transitionDuration,animationName:t.style.animationName},t.style.transitionDuration="0s",t.style.animationName="none";let e=t.getBoundingClientRect();w.current=e.height,T.current=e.width,x.current||(t.style.transitionDuration=U.current.transitionDuration,t.style.animationName=U.current.animationName),h(a)}},[c.open,a]),(0,f.jsx)(s.WV.div,{"data-state":D(c.open),"data-disabled":c.disabled?"":void 0,id:c.contentId,hidden:!y,...o,ref:m,style:{"--radix-collapsible-content-height":v?`${v}px`:void 0,"--radix-collapsible-content-width":p?`${p}px`:void 0,...t.style},children:y&&i})});function D(t){return t?"open":"closed"}var x=v,U=p},2563:function(t,e,n){n.d(e,{x0:()=>r});let r=(t=21)=>{let e="",n=crypto.getRandomValues(new Uint8Array(t|=0));for(;t--;)e+="useandom-26T198340PX75pxJACKVERYMINDBUSHWOLF_GQZbfghjklqvwyzrict"[63&n[t]];return e}}}]);