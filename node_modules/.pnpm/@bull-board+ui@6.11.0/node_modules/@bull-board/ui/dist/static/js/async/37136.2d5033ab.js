"use strict";(self.webpackChunk_bull_board_ui=self.webpackChunk_bull_board_ui||[]).push([["37136"],{58518:function(e,t,o){o.r(t),o.d(t,{AddJobModal:()=>h});var i=o(52322),r=o(2784),n=o(59382),a=o(9268),s=o(2575),p=JSON.parse('{"$ref":"#/definitions/Bull.JobOptions","$schema":"http://json-schema.org/draft-07/schema#","definitions":{"Bull.BackoffOptions":{"additionalProperties":false,"properties":{"delay":{"description":"Backoff delay, in milliseconds","type":"number"},"options":{"description":"Options for custom strategies"},"type":{"description":"Backoff type, which can be either `fixed` or `exponential`","type":"string"}},"required":["type"],"type":"object"},"Bull.JobId":{"type":["number","string"]},"Bull.JobOptions":{"additionalProperties":false,"properties":{"attempts":{"description":"The total number of attempts to try the job until it completes","type":"number"},"backoff":{"anyOf":[{"type":"number"},{"$ref":"#/definitions/Bull.BackoffOptions"}],"description":"Backoff setting for automatic retries if the job fails"},"delay":{"description":"An amount of miliseconds to wait until this job can be processed. Note that for accurate delays, both server and clients should have their clocks synchronized. [optional]","type":"number"},"jobId":{"$ref":"#/definitions/Bull.JobId","description":"Override the job ID - by default, the job ID is a unique integer, but you can use this setting to override it. If you use this option, it is up to you to ensure the jobId is unique. If you attempt to add a job with an id that already exists, it will not be added."},"lifo":{"description":"A boolean which, if true, adds the job to the right of the queue instead of the left (default false)","type":"boolean"},"preventParsingData":{"description":"Prevents JSON data from being parsed.","type":"boolean"},"priority":{"description":"Optional priority value. ranges from 1 (highest priority) to MAX_INT  (lowest priority). Note that using priorities has a slight impact on performance, so do not use it if not required","type":"number"},"removeOnComplete":{"anyOf":[{"type":"boolean"},{"type":"number"},{"$ref":"#/definitions/Bull.KeepJobsOptions"}],"description":"A boolean which, if true, removes the job when it successfully completes. When a number, it specifies the amount of jobs to keep. Default behavior is to keep the job in the completed set. See KeepJobsOptions if using that interface instead."},"removeOnFail":{"anyOf":[{"type":"boolean"},{"type":"number"},{"$ref":"#/definitions/Bull.KeepJobsOptions"}],"description":"A boolean which, if true, removes the job when it fails after all attempts. When a number, it specifies the amount of jobs to keep. Default behavior is to keep the job in the failed set. See KeepJobsOptions if using that interface instead."},"repeat":{"anyOf":[{"additionalProperties":false,"properties":{"count":{"description":"The start value for the repeat iteration count.","type":"number"},"cron":{"description":"Cron pattern specifying when the job should execute","type":"string"},"endDate":{"anyOf":[{"format":"date-time","type":"string"},{"type":"string"},{"type":"number"}],"description":"End date when the repeat job should stop repeating"},"key":{"description":"The key for the repeatable job metadata in Redis.","type":"string"},"limit":{"description":"Number of times the job should repeat at max.","type":"number"},"startDate":{"anyOf":[{"format":"date-time","type":"string"},{"type":"string"},{"type":"number"}],"description":"Start date when the repeat job should start repeating (only with cron)."},"tz":{"description":"Timezone","type":"string"}},"required":["cron"],"type":"object"},{"additionalProperties":false,"properties":{"count":{"description":"The start value for the repeat iteration count.","type":"number"},"endDate":{"anyOf":[{"format":"date-time","type":"string"},{"type":"string"},{"type":"number"}],"description":"End date when the repeat job should stop repeating"},"every":{"description":"Repeat every millis (cron setting cannot be used together with this setting.)","type":"number"},"key":{"description":"The key for the repeatable job metadata in Redis.","type":"string"},"limit":{"description":"Number of times the job should repeat at max.","type":"number"},"tz":{"description":"Timezone","type":"string"}},"required":["every"],"type":"object"}],"description":"Repeat job according to a cron specification"},"stackTraceLimit":{"description":"Limits the amount of stack trace lines that will be recorded in the stacktrace.","type":"number"},"timeout":{"description":"The number of milliseconds after which the job should be fail with a timeout error","type":"number"}},"type":"object"},"Bull.KeepJobsOptions":{"additionalProperties":false,"description":"Specify which jobs to keep after finishing processing this job. If both age and count are specified, then the jobs kept will be the ones that satisfies both properties.","properties":{"age":{"description":"Maximum age in *seconds* for job to be kept.","type":"number"},"count":{"description":"Maximum count of jobs to be kept.","type":"number"}},"type":"object"}}}'),d=JSON.parse('{"$ref":"#/definitions/JobsOptions","$schema":"http://json-schema.org/draft-07/schema#","definitions":{"BackoffOptions":{"additionalProperties":false,"description":"Settings for backing off failed jobs.","properties":{"delay":{"description":"Delay in milliseconds.","type":"number"},"type":{"anyOf":[{"type":"string"},{"enum":["fixed","exponential"],"type":"string"}],"description":"Name of the backoff strategy."}},"required":["type"],"type":"object"},"DateType":{"anyOf":[{"format":"date-time","type":"string"},{"type":"number"},{"type":"string"}]},"JobsOptions":{"additionalProperties":false,"properties":{"attempts":{"description":"The total number of attempts to try the job until it completes.","type":"number"},"backoff":{"anyOf":[{"type":"number"},{"$ref":"#/definitions/BackoffOptions"}],"description":"Backoff setting for automatic retries if the job fails"},"delay":{"description":"An amount of milliseconds to wait until this job can be processed. Note that for accurate delays, worker and producers should have their clocks synchronized.","type":"number"},"failParentOnFailure":{"description":"If true, moves parent to failed.","type":"boolean"},"jobId":{"description":"Override the job ID - by default, the job ID is a unique integer, but you can use this setting to override it. If you use this option, it is up to you to ensure the jobId is unique. If you attempt to add a job with an id that already exists, it will not be added.","type":"string"},"keepLogs":{"description":"Maximum amount of log entries that will be preserved","type":"number"},"lifo":{"description":"If true, adds the job to the right of the queue instead of the left (default false)","type":"boolean"},"parent":{"additionalProperties":false,"properties":{"id":{"type":"string"},"queue":{"description":"It includes the prefix, the namespace separator :, and queue name.","type":"string"}},"required":["id","queue"],"type":"object"},"prevMillis":{"description":"Internal property used by repeatable jobs.","type":"number"},"priority":{"description":"Ranges from 1 (highest priority) to 2 097 152 (lowest priority). Note that using priorities has a slight impact on performance, so do not use it if not required.","type":"number"},"removeDependencyOnFailure":{"description":"If true, removes the job from its parent dependencies when it fails after all attempts.","type":"boolean"},"removeOnComplete":{"anyOf":[{"type":"boolean"},{"type":"number"},{"$ref":"#/definitions/KeepJobs"}],"description":"If true, removes the job when it successfully completes When given a number, it specifies the maximum amount of jobs to keep, or you can provide an object specifying max age and/or count to keep. It overrides whatever setting is used in the worker. Default behavior is to keep the job in the completed set."},"removeOnFail":{"anyOf":[{"type":"boolean"},{"type":"number"},{"$ref":"#/definitions/KeepJobs"}],"description":"If true, removes the job when it fails after all attempts. When given a number, it specifies the maximum amount of jobs to keep, or you can provide an object specifying max age and/or count to keep. It overrides whatever setting is used in the worker. Default behavior is to keep the job in the failed set."},"repeat":{"$ref":"#/definitions/RepeatOptions","description":"Repeat this job, for example based on a `cron` schedule."},"repeatJobKey":{"description":"Internal property used by repeatable jobs to save base repeat job key.","type":"string"},"sizeLimit":{"description":"Limits the size in bytes of the job\'s data payload (as a JSON serialized string).","type":"number"},"stackTraceLimit":{"description":"Limits the amount of stack trace lines that will be recorded in the stacktrace.","type":"number"},"timestamp":{"description":"Timestamp when the job was created.","type":"number"}},"type":"object"},"KeepJobs":{"additionalProperties":false,"description":"KeepJobs\\n\\nSpecify which jobs to keep after finishing. If both age and count are specified, then the jobs kept will be the ones that satisfies both properties.","properties":{"age":{"description":"Maximum age in seconds for job to be kept.","type":"number"},"count":{"description":"Maximum count of jobs to be kept.","type":"number"}},"type":"object"},"RepeatOptions":{"additionalProperties":false,"description":"Settings for repeatable jobs","properties":{"count":{"description":"The start value for the repeat iteration count.","type":"number"},"currentDate":{"$ref":"#/definitions/DateType"},"endDate":{"$ref":"#/definitions/DateType"},"every":{"description":"Repeat after this amount of milliseconds (`pattern` setting cannot be used together with this setting.)","type":"number"},"immediately":{"description":"Repeated job should start right now ( work only with every settings)","type":"boolean"},"jobId":{"type":"string"},"limit":{"description":"Number of times the job should repeat at max.","type":"number"},"nthDayOfWeek":{"type":"number"},"offset":{"type":"number"},"pattern":{"description":"A repeat pattern","type":"string"},"prevMillis":{"type":"number"},"startDate":{"$ref":"#/definitions/DateType"},"tz":{"type":"string"},"utc":{"type":"boolean"}},"type":"object"}}}'),l=o(83576),c=o(73283),u=o(73389),b=o(84115),f=o(74672);let m={bull:p,bullmq:d},h=e=>{let{open:t,onClose:o,job:p}=e,{queues:d,actions:h}=(0,s.y)(),y=(0,a.O)(),[g,j]=(0,r.useState)(y),{t:v}=(0,n.$G)();if(!d||!y||!g)return null;let k=async e=>{e.preventDefault();let t=Object.fromEntries(Array.from(e.target.elements).filter(e=>e.name).map(e=>[e.name,e.value]));t.jobData=JSON.parse(t.jobData),t.jobOptions=JSON.parse(t.jobOptions),await h.addJob(t.queueName,t.jobName||"__default__",t.jobData,t.jobOptions)(),o()};return(0,i.jsx)(f.u,{width:"small",open:t,onClose:o,title:v("ADD_JOB.TITLE",{context:p?"duplicate":void 0}),actionButton:(0,i.jsx)(l.z,{type:"submit",theme:"primary",form:"add-job-form",children:v(`ADD_JOB.${p?"DUPLICATE":"ADD"}`)}),children:(0,i.jsxs)("form",{id:"add-job-form",onSubmit:k,children:[(0,i.jsx)(b.m,{label:v("ADD_JOB.QUEUE_NAME"),id:"queue-name",options:(d||[]).map(e=>({text:e.name,value:e.name})),name:"queueName",value:g.name||"",onChange:e=>j(d.find(t=>t.name===e.target.value))}),(0,i.jsx)(c.U,{label:v("ADD_JOB.JOB_NAME"),id:"job-name",name:"jobName",defaultValue:null==p?void 0:p.name,placeholder:"__default__"}),(0,i.jsx)(u.$,{label:v("ADD_JOB.JOB_DATA"),id:"job-data",name:"jobData",value:null==p?void 0:p.data}),(0,i.jsx)(u.$,{label:v("ADD_JOB.JOB_OPTIONS"),id:"job-options",name:"jobOptions",schema:m[g.type],value:null==p?void 0:p.opts})]})})}},73389:function(e,t,o){o.d(t,{$:()=>j});var i=o(52322),r=o(2784),n=o(54250),a=o(66203),s=o(85237),p=o(77730),d=o(35740),l=o(76621),c=o(42367),u=o(21372);let b=s.Qf.define([{tag:c.pJ.atom,color:"var(--hl-keyword)"},{tag:c.pJ.keyword,color:"var(--hl-keyword)"},{tag:c.pJ.bool,color:"var(--hl-keyword)"},{tag:c.pJ.string,color:"var(--hl-string)"},{tag:c.pJ.number,color:"var(--hl-number)"},{tag:c.pJ.brace,color:"var(--accent-color-d1)"},{tag:c.pJ.punctuation,color:"var(--accent-color-d1)"},{tag:c.pJ.propertyName,color:"var(--hl-type)"}]),f=l.tk.theme({"&":{height:"200px",backgroundColor:"var(--input-bg)",border:"1px var(--input-border) solid",borderRadius:"0.375rem",boxShadow:"0 1px 2px 0 rgba(0, 0, 0, 0.05)",marginTop:"0.25rem",fontSize:"0.875rem",transition:"border-color 0.2s ease-out, box-shadow 0.2s ease-out"},"&.cm-focused":{outline:"none",borderColor:"var(--input-focus-border)",boxShadow:"var(--input-focus-shadow)"},".cm-gutters":{borderRadius:"0.375rem 0 0 0.375rem",backgroundColor:"var(--json-edit-gutter-bg)",color:"inherit",borderRight:"var(--json-edit-gutter-border-color)"},".cm-cursor":{borderLeftColor:"var(--json-edit-cursor-color)"},".cm-activeLineGutter":{backgroundColor:"var(--json-edit-gutter-active-bg)"},".cm-scroller":{overflow:"auto"},".cm-tooltip":{padding:"0.25rem 0.5rem",borderRadius:"0.275rem",backgroundColor:"var(--json-edit-tooltip-bg)",border:"1px solid var(--json-edit-tooltip-border-color)"},".cm6-json-schema-hover--code > p":{margin:"0.5em 0"},".cm-tooltip-above .cm-tooltip-arrow:before":{borderTop:"7px solid var(--json-edit-tooltip-border-color)"},".cm-tooltip-above .cm-tooltip-arrow:after":{borderTop:"7px solid var(--json-edit-tooltip-bg)"},".cm-selectionBackground":{background:"var(--json-edit-selection-bg)!important"}}),m=[(0,l.v5)({class:"CodeMirror-lint-markers"}),(0,s.n$)(),(0,l.HQ)(),(0,n.vQ)(),(0,a.m8)(),(0,n.ys)(),(0,l.Eu)(),(0,p.Q2)(),(0,s.nY)(),(0,l.Uw)(),(0,s.mi)(),l.$f.of([...n.GA,...a.wQ,...a.f$,...s.e7,...n.B1,...p.Fv]),l.tk.lineWrapping,d.yy.tabSize.of(2),(0,s.nF)(b),f],h=d.q6.define(),y=e=>{let{doc:t,schema:o,...n}=e,a=(0,r.useRef)(null),s=(0,r.useRef)(null),[c,b]=(0,r.useState)(null);return(0,r.useEffect)(()=>{let e=l.tk.updateListener.of(e=>{if(!s.current)return;let t=(0,p.KS)(e.state);if(e.docChanged||e.transactions.some(e=>e.annotation(h))||0!==t)t>0&&(s.current.value="");else{let t=e.state.doc;s.current.value=t.toString()}}),i=d.yy.create({doc:JSON.stringify(t,null,2),extensions:[m,e,(0,u.fB)(o||{})]}),r=new l.tk({state:i,parent:a.current});return b(r),()=>r.destroy()},[]),(0,r.useEffect)(()=>{c&&(0,u.ov)(c,o||{})},o?[o]:[]),(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("div",{ref:a}),(0,i.jsx)("input",{type:"hidden",ref:s,...n})]})};var g=o(73250);let j=e=>{let{label:t,id:o,value:r,...n}=e;return(0,i.jsx)(g.g,{label:t,id:o,children:(0,i.jsx)(y,{doc:r||{},id:o,...n})})}},84115:function(e,t,o){o.d(t,{m:()=>n});var i=o(52322);o(2784);var r=o(73250);let n=e=>{let{label:t,id:o,options:n,...a}=e;return(0,i.jsx)(r.g,{label:t,id:o,children:(0,i.jsx)("select",{id:o,...a,children:n.map(e=>(0,i.jsx)("option",{value:e.value,children:e.text},e.value))})})}},74672:function(e,t,o){o.d(t,{u:()=>d});var i=o(52322),r=o(78364),n=o(67239);o(2784);var a=o(59382),s=o(83576),p=o(67755);let d=e=>{let{open:t,title:o,onClose:d,children:l,width:c,actionButton:u}=e,{t:b}=(0,a.$G)();return(0,i.jsx)(r.fC,{open:t,modal:!0,onOpenChange:e=>{e||d()},children:(0,i.jsxs)(r.h_,{children:[(0,i.jsx)(r.aV,{className:p.Z.overlay}),(0,i.jsx)(r.VY,{className:p.Z.contentWrapper,children:(0,i.jsxs)("div",{className:(0,n.Z)(p.Z.content,p.Z[c||""]),children:[!!o&&(0,i.jsx)(r.Dx,{children:o}),(0,i.jsx)(r.dk,{asChild:!0,children:(0,i.jsx)("div",{className:p.Z.description,children:l})}),(0,i.jsxs)("div",{className:p.Z.actions,children:[u,(0,i.jsx)(r.x8,{asChild:!0,children:(0,i.jsx)(s.z,{theme:"basic",children:b("MODAL.CLOSE_BTN")})})]})]})})]})})}}}]);