"use strict";(self.webpackChunk_bull_board_ui=self.webpackChunk_bull_board_ui||[]).push([["37432"],{96274:function(e,t,l){l.d(t,{n:()=>s});let s={latest:"latest",active:"active",waiting:"waiting",waitingChildren:"waiting-children",prioritized:"prioritized",completed:"completed",failed:"failed",delayed:"delayed",paused:"paused"}},70927:function(e,t,l){l.d(t,{Z:()=>r});var s=l(52322),n=l(67239);l(2784);let r=e=>{let{children:t,className:l}=e;return(0,s.jsx)("div",{className:(0,n.Z)("card-xqyZlH",l),children:t})}},73250:function(e,t,l){l.d(t,{g:()=>r});var s=l(52322),n=l(67239);l(2784);let r=e=>{let{label:t,id:l,inline:r,children:a}=e;return(0,s.jsxs)("div",{className:(0,n.Z)("field-JbAWJt",{"inline-vXG0Za":r}),children:[!!t&&!r&&(0,s.jsx)("label",{htmlFor:l,children:t}),a,!!t&&r&&(0,s.jsx)("label",{htmlFor:l,children:t})]})}},73283:function(e,t,l){l.d(t,{U:()=>r});var s=l(52322);l(2784);var n=l(73250);let r=e=>{let{label:t,id:l,...r}=e;return(0,s.jsx)(n.g,{label:t,id:l,children:(0,s.jsx)("input",{id:l,type:"text",...r})})}},10175:function(e,t,l){l.d(t,{Y:()=>n});var s=l(52322);l(2784);let n=()=>(0,s.jsx)("svg",{"aria-hidden":"true",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 256 512",children:(0,s.jsx)("path",{d:"M231.293 473.899l19.799-19.799c4.686-4.686 4.686-12.284 0-16.971L70.393 256 251.092 74.87c4.686-4.686 4.686-12.284 0-16.971L231.293 38.1c-4.686-4.686-12.284-4.686-16.971 0L4.908 247.515c-4.686 4.686-4.686 12.284 0 16.971L214.322 473.9c4.687 4.686 12.285 4.686 16.971-.001z"})})},42210:function(e,t,l){l.d(t,{f:()=>n});var s=l(52322);l(2784);let n=()=>(0,s.jsx)("svg",{"aria-hidden":"true",focusable:"false",role:"img",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 448 512",children:(0,s.jsx)("path",{d:"M144 479H48c-26.5 0-48-21.5-48-48V79c0-26.5 21.5-48 48-48h96c26.5 0 48 21.5 48 48v352c0 26.5-21.5 48-48 48zm304-48V79c0-26.5-21.5-48-48-48h-96c-26.5 0-48 21.5-48 48v352c0 26.5 21.5 48 48 48h96c26.5 0 48-21.5 48-48z"})})},63649:function(e,t,l){l.d(t,{o:()=>n});var s=l(52322);l(2784);let n=()=>(0,s.jsx)("svg",{"aria-hidden":"true",focusable:"false",role:"img",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 448 512",children:(0,s.jsx)("path",{d:"M424.4 214.7L72.4 6.6C43.8-10.3 0 6.1 0 47.9V464c0 37.5 40.7 60.1 72.4 41.3l352-208c31.4-18.5 31.5-64.1 0-82.6z"})})},61080:function(e,t,l){l.d(t,{m:()=>n});var s=l(52322);l(2784);let n=()=>(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",children:(0,s.jsx)("path",{d:"M15 17v-2.99A4 4 0 0 0 11 10H8v5L2 9l6-6v5h3a6 6 0 0 1 6 6v3h-2z"})})},9447:function(e,t,l){l.d(t,{O:()=>n});var s=l(52322);l(2784);let n=()=>(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",children:(0,s.jsx)("path",{d:"M5 4a2 2 0 0 0-2 2v6H0l4 4 4-4H5V6h7l2-2H5zm10 4h-3l4-4 4 4h-3v6a2 2 0 0 1-2 2H6l2-2h7V8z"})})},76509:function(e,t,l){l.d(t,{X:()=>n});var s=l(52322);l(2784);let n=()=>(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",children:(0,s.jsx)("path",{d:"M6 2l2-2h4l2 2h4v2H2V2h4zM3 6h14l-1 14H4L3 6zm5 2v10h1V8H8zm3 0v10h1V8h-1z"})})},58411:function(e,t,l){l.d(t,{t:()=>ei});var s=l(52322),n=l(96274),r=l(2784),a=l(59382),i=l(47933),c=l(70927);let o=()=>(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 448 512",children:(0,s.jsx)("path",{d:"M207.029 381.476 12.686 187.132c-9.373-9.373-9.373-24.569 0-33.941l22.667-22.667c9.357-9.357 24.522-9.375 33.901-.04L224 284.505l154.745-154.021c9.379-9.335 24.544-9.317 33.901.04l22.667 22.667c9.373 9.373 9.373 24.569 0 33.941L240.971 381.476c-9.373 9.372-24.569 9.372-33.942 0z"})}),d=()=>(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 512 512",children:(0,s.jsx)("path",{d:"M352 0c-12.9 0-24.6 7.8-29.6 19.8s-2.2 25.7 6.9 34.9L370.7 96 201.4 265.4c-12.5 12.5-12.5 32.8 0 45.3s32.8 12.5 45.3 0L416 141.3l41.4 41.4c9.2 9.2 22.9 11.9 34.9 6.9s19.8-16.6 19.8-29.6V32c0-17.7-14.3-32-32-32H352zM80 32C35.8 32 0 67.8 0 112v320c0 44.2 35.8 80 80 80h320c44.2 0 80-35.8 80-80V320c0-17.7-14.3-32-32-32s-32 14.3-32 32v112c0 8.8-7.2 16-16 16H80c-8.8 0-16-7.2-16-16V112c0-8.8 7.2-16 16-16h112c17.7 0 32-14.3 32-32s-14.3-32-32-32H80z"})});var u=l(6294);let h=()=>(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 448 512",children:(0,s.jsx)("path",{d:"M241 130.5l194.3 194.3c9.4 9.4 9.4 24.6 0 33.9l-22.7 22.7c-9.4 9.4-24.5 9.4-33.9 0L224 227.5 69.3 381.5c-9.4 9.3-24.5 9.3-33.9 0l-22.7-22.7c-9.4-9.4-9.4-24.6 0-33.9L207 130.5C216.4 121.2 231.6 121.2 241 130.5z"})});var p=l(83576),x=l(17260),m=l(1542);let j={details:"details-LkCTWS",tabActions:"tabActions-fF3v6_",tabContent:"tabContent-qmHNgq"};var g=l(67239),v=l(2563);let w=void 0!==window.Worker,f=null,b=new Map;async function L(e,t){if(w)return f||((f=new Worker(new URL(l.p+l.u("16651"),l.b),Object.assign({},{name:"highlight-worker"},{type:void 0}))).onmessage=e=>{let{data:t}=e,{id:l,code:s}=t;if(b.has(l)){let{resolve:e}=b.get(l);e(s)}}),new Promise((l,s)=>{let n=(0,v.x0)(5);null==f||f.postMessage({id:n,code:e,language:t}),b.set(n,{resolve:e=>{b.delete(n),l(e)},reject:()=>{b.delete(n),s()}}),setTimeout(()=>s(),6e4)});{let{highlighter:s}=await Promise.all([l.e("85093"),l.e("54183")]).then(l.bind(l,39429));return s.highlightAuto(e,[t]).value||""}}let y=()=>(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 384 512",children:(0,s.jsx)("path",{d:"M280 64l40 0c35.3 0 64 28.7 64 64l0 320c0 35.3-28.7 64-64 64L64 512c-35.3 0-64-28.7-64-64L0 128C0 92.7 28.7 64 64 64l40 0 9.6 0C121 27.5 153.3 0 192 0s71 27.5 78.4 64l9.6 0zM64 112c-8.8 0-16 7.2-16 16l0 320c0 8.8 7.2 16 16 16l256 0c8.8 0 16-7.2 16-16l0-320c0-8.8-7.2-16-16-16l-16 0 0 24c0 13.3-10.7 24-24 24l-88 0-88 0c-13.3 0-24-10.7-24-24l0-24-16 0zm128-8a24 24 0 1 0 0-48 24 24 0 1 0 0 48z"})}),N=e=>{let{language:t,text:l}=e,[n,a]=(0,r.useState)("");return(0,r.useEffect)(()=>{let e=!1;return L(l,t).then(t=>{e||a(t)}),()=>{e=!0}},[t,l]),(0,s.jsxs)("div",{className:"codeContainerWrapper-ZpYBAP",children:[(0,s.jsx)("pre",{children:(0,s.jsx)("code",{className:(0,g.Z)("hljs",t),dangerouslySetInnerHTML:{__html:n}})}),(0,s.jsx)(p.z,{onClick:()=>{navigator.clipboard.writeText(l??"")},className:"copyBtn-vLoi5u",children:(0,s.jsx)(y,{})})]})};var O=l(85950),B=l(73283),C=l(81731),J=l(42210),T=l(63649);let A={jobLogs:"jobLogs-MsL4aJ",preWrapper:"preWrapper-V9RpQs",toolbar:"toolbar-duDU2f",searchBar:"searchBar-N4o70g",logLineCopyButton:"logLineCopyButton-LnHhmG"},S=e=>{var t,l;let s=null==(l=e.message)||null==(t=l.match(/((info|warn|error)?):/i))?void 0:t[1];return null==s?void 0:s.toLowerCase()},D=e=>async()=>e&&document.fullscreenElement!==e?await e.requestFullscreen():document.exitFullscreen(),E=e=>{navigator.clipboard.writeText(e.message)},I=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";return!t||RegExp(`${t}`,"i").test(e.message)};function z(e){return e.map((e,t)=>({message:e,lineNumber:t+1}))}let k=e=>{let{actions:t,job:l}=e,{t:n}=(0,a.$G)(),[i,c]=(0,r.useState)([]),[o,d]=(0,r.useState)(!1),[u,h]=(0,r.useState)(""),x=(0,r.useRef)(null);(0,r.useEffect)(()=>{let e=!0;return t.getJobLogs().then(t=>{e&&c(z(t))}),()=>{e=!1}},[]),(0,O.Y)(async()=>{var e;let l=null==(e=x.current)?void 0:e.querySelector(`.${A.preWrapper}`);c(z(await t.getJobLogs())),requestAnimationFrame(()=>{null==l||l.scrollTo({top:null==l?void 0:l.scrollHeight,behavior:"smooth"})})},o?2500:null);let m=i.filter(e=>I(e,u));return(0,s.jsxs)("div",{className:A.jobLogs,ref:x,children:[(0,s.jsxs)("ul",{className:A.toolbar,children:[(0,s.jsx)("li",{children:(0,s.jsx)("form",{onSubmit:e=>{var t,l;h((null==(l=e.currentTarget)||null==(t=l.searchQuery)?void 0:t.value)||""),e.preventDefault()},children:(0,s.jsx)(B.U,{className:A.searchBar,name:"searchQuery",type:"search",placeholder:n("JOB.LOGS.FILTER_PLACEHOLDER"),onChange:e=>{var t;(null==(t=e.currentTarget)?void 0:t.value)||h("")}})})}),!l.finishedOn&&(0,s.jsx)("li",{children:(0,s.jsx)(p.z,{isActive:o,onClick:()=>{d(!o)},children:o?(0,s.jsx)(J.f,{}):(0,s.jsx)(T.o,{})})}),(0,s.jsx)("li",{children:(0,s.jsx)(p.z,{onClick:D(x.current),children:(0,s.jsx)(C.$,{})})}),(0,s.jsx)("li",{children:(0,s.jsx)(p.z,{onClick:()=>{let e=m.map(e=>e.message).join("\n");navigator.clipboard.writeText(e)},children:(0,s.jsx)(y,{})})})]}),(0,s.jsx)("div",{className:A.preWrapper,children:(0,s.jsx)("pre",{children:(0,s.jsx)("ol",{style:{paddingInlineStart:`${`${m.length}`.length+1}ch`},children:m.map(e=>(0,s.jsxs)("li",{className:S(e),"data-line-number":`${e.lineNumber}.`,children:[e.message,(0,s.jsx)(p.z,{onClick:()=>E(e),className:A.logLineCopyButton,tabIndex:-1,children:(0,s.jsx)(y,{})})]},e.lineNumber))})})})]})},M=e=>{let{selectedTab:t,job:l,actions:n}=e,{t:i}=(0,a.$G)(),{collapseJobData:c,collapseJobOptions:d,collapseJobError:h}=(0,u.F)(),[x,m]=(0,r.useState)({data:!1,options:!1,error:!1}),{stacktrace:j,data:g,returnValue:v,opts:w,failedReason:f}=l;switch(t){case"Data":return c&&!x.data?(0,s.jsxs)(p.z,{onClick:()=>m({...x,data:!0}),children:[i("JOB.SHOW_DATA_BTN")," ",(0,s.jsx)(o,{})]}):(0,s.jsx)(N,{language:"json",text:JSON.stringify({jobData:g,returnValue:v},null,2)});case"Options":return d&&!x.options?(0,s.jsxs)(p.z,{onClick:()=>m({...x,options:!0}),children:[i("JOB.SHOW_OPTIONS_BTN")," ",(0,s.jsx)(o,{})]}):(0,s.jsx)(N,{language:"json",text:JSON.stringify(w,null,2)});case"Error":if(0===j.length)return(0,s.jsx)("div",{className:"error",children:f||i("JOB.NA")});return h&&!x.error?(0,s.jsxs)(p.z,{onClick:()=>m({...x,error:!0}),children:[i("JOB.SHOW_ERRORS_BTN")," ",(0,s.jsx)(o,{})]}):(0,s.jsx)(N,{language:"stacktrace",text:j.join("\n")},"stacktrace");case"Logs":return(0,s.jsx)(k,{actions:n,job:l});default:return null}},R=e=>{let{status:t,job:l,actions:n}=e,{tabs:r,selectedTab:i}=(0,m.r)(t),{t:c}=(0,a.$G)();return 0===r.length?null:(0,s.jsxs)("div",{className:j.details,children:[(0,s.jsx)("ul",{className:j.tabActions,children:r.map(e=>(0,s.jsx)("li",{children:(0,s.jsx)(p.z,{onClick:e.selectTab,isActive:e.isActive,children:c(`JOB.TABS.${e.title.toUpperCase()}`)})},e.title))}),(0,s.jsx)("div",{className:j.tabContent,children:(0,s.jsx)(M,{selectedTab:i,job:l,actions:n})})]})};var _=l(61080),H=l(9447),F=l(76509);let W=e=>{let{title:t,children:l}=e;return(0,s.jsx)("span",{"data-title":t,className:"tooltip-QJB3Pz",children:l})},$={jobActions:"jobActions-XfoQK8",button:"button-GXX8BV"},U={updateData:{titleKey:"UPDATE_DATA",Icon:()=>(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 512 512",children:(0,s.jsx)("path",{d:"M471.6 21.7c-21.9-21.9-57.3-21.9-79.2 0L362.3 51.7l97.9 97.9 30.1-30.1c21.9-21.9 21.9-57.3 0-79.2L471.6 21.7zm-299.2 220c-6.1 6.1-10.8 13.6-13.5 21.9l-29.6 88.8c-2.9 8.6-.6 18.1 5.8 24.6s15.9 8.7 24.6 5.8l88.8-29.6c8.2-2.7 15.7-7.4 21.9-13.5L437.7 172.3 339.7 74.3 172.4 241.7zM96 64C43 64 0 107 0 160L0 416c0 53 43 96 96 96l256 0c53 0 96-43 96-96l0-96c0-17.7-14.3-32-32-32s-32 14.3-32 32l0 96c0 17.7-14.3 32-32 32L96 448c-17.7 0-32-14.3-32-32l0-256c0-17.7 14.3-32 32-32l96 0c17.7 0 32-14.3 32-32s-14.3-32-32-32L96 64z"})}),actionKey:"updateJobData"},promote:{titleKey:"PROMOTE",Icon:_.m,actionKey:"promoteJob"},clean:{titleKey:"CLEAN",Icon:F.X,actionKey:"cleanJob"},retry:{titleKey:"RETRY",Icon:H.O,actionKey:"retryJob"},duplicate:{titleKey:"DUPLICATE",Icon:()=>(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 448 512",children:(0,s.jsx)("path",{d:"M208 0L332.1 0c12.7 0 24.9 5.1 33.9 14.1l67.9 67.9c9 9 14.1 21.2 14.1 33.9L448 336c0 26.5-21.5 48-48 48l-192 0c-26.5 0-48-21.5-48-48l0-288c0-26.5 21.5-48 48-48zM48 128l80 0 0 64-64 0 0 256 192 0 0-32 64 0 0 48c0 26.5-21.5 48-48 48L48 512c-26.5 0-48-21.5-48-48L0 176c0-26.5 21.5-48 48-48z"})}),actionKey:"duplicateJob"}},Z={[n.n.failed]:[U.retry,U.duplicate,U.updateData,U.clean],[n.n.delayed]:[U.promote,U.duplicate,U.updateData,U.clean],[n.n.completed]:[U.duplicate,U.retry,U.clean],[n.n.waiting]:[U.duplicate,U.updateData,U.clean],[n.n.waitingChildren]:[U.duplicate,U.updateData,U.clean],[n.n.prioritized]:[U.duplicate,U.updateData,U.clean],[n.n.paused]:[U.duplicate,U.updateData,U.clean]},G=e=>{let{actions:t,status:l,allowRetries:n}=e,r=Z[l],{t:i}=(0,a.$G)();return r?(n||(r=r.filter(e=>"retryJob"!==e.actionKey)),(0,s.jsx)("ul",{className:$.jobActions,children:r.map(e=>(0,s.jsx)("li",{children:(0,s.jsx)(W,{title:i(`JOB.ACTIONS.${e.titleKey}`),children:(0,s.jsx)(p.z,{onClick:t[e.actionKey],className:$.button,children:(0,s.jsx)(e.Icon,{})})})},e.titleKey))})):null},K={progress:"progress-Rr7sCM",failed:"failed-rTUpGj",success:"success-RY6h2q"},P=e=>{var t;let{progress:l,status:r,className:a,strokeWidth:i=6}=e,c="number"==typeof(t=l)?t:"string"==typeof t?Number.isNaN(+t)?null:+t:t&&"boolean"!=typeof t&&"progress"in t&&"number"==typeof t.progress?t.progress:null;if(!c)return null;let o={cx:"50%",cy:"50%",r:`calc(50% - ${i/2}px)`,strokeWidth:i,"transform-origin":"center"};return(0,s.jsxs)("svg",{className:(0,g.Z)(K.progress,a),width:"100%",height:"100%",children:[(0,s.jsx)("circle",{...o}),(0,s.jsx)("circle",{className:(0,g.Z)({[K.failed]:r===n.n.failed,[K.success]:r!==n.n.failed}),pathLength:100,strokeDasharray:100,strokeDashoffset:100-c,strokeLinejoin:"round",strokeLinecap:"round",transform:"rotate(-90)",...o}),(0,s.jsx)("text",{x:"50%",y:"50%",textAnchor:"middle",dominantBaseline:"central",children:(0,s.jsx)("tspan",{dominantBaseline:"central",children:`${Math.round(c)}%`})})]})};var V=l(89192),Y=l(86626),q=l(31794),X=l(86544),Q=l(77184),ee=l(61314),et=l(4130);let el=(e,t,l)=>{let s;if((0,V.Z)(e)){if(null==l?void 0:l.short)return(0,Y.Z)(new Date(e),l.short);s={hour:"numeric",minute:"numeric",second:"numeric"}}else if((0,q.Z)(e,new Date)){if(null==l?void 0:l.common)return(0,Y.Z)(new Date(e),l.common);s={month:"numeric",day:"numeric",hour:"numeric",minute:"2-digit",second:"2-digit"}}else{if(null==l?void 0:l.full)return(0,Y.Z)(new Date(e),l.full);s={year:"numeric",month:"numeric",day:"numeric",hour:"numeric",minute:"2-digit",second:"2-digit"}}return new Intl.DateTimeFormat(t,s).format(e)},es=(e,t,l)=>{let s=(0,X.Z)(e,t),n=s/1e3;return n>5?(0,Q.Z)(e,t,{includeSeconds:!0,locale:ee.Y}):n>=1?l("JOB.DURATION.SECS",{duration:n.toFixed(2)}):l("JOB.DURATION.MILLI_SECS",{duration:s})},en=function(e){let{job:t,status:l}=e,{t:r,i18n:i}=(0,a.$G)(),c=(0,et.p)().dateFormats||{};return(0,s.jsx)("div",{className:"timelineWrapper-iZqG0C",children:(0,s.jsxs)("ul",{className:"timeline-lF9niS",children:[(0,s.jsxs)("li",{children:[(0,s.jsx)("small",{children:r("JOB.ADDED_AT")}),(0,s.jsx)("time",{children:el(t.timestamp||0,i.language,c)})]}),!!t.delay&&t.delay>0&&l===n.n.delayed&&(0,s.jsxs)("li",{children:[(0,s.jsx)("small",{children:r("JOB.WILL_RUN_AT")}),(0,s.jsx)("time",{children:el((t.timestamp||0)+(t.opts.delay||t.delay||0),i.language,c)}),t.delay!==t.opts.delay&&(0,s.jsxs)("small",{children:[r("JOB.DELAY_CHANGED")," "]})]}),!!t.processedOn&&(0,s.jsxs)("li",{children:[(0,s.jsxs)("small",{children:[!!t.delay&&t.delay>0&&r("JOB.DELAYED_FOR")+" ",es(t.processedOn,t.timestamp||0,r)]}),(0,s.jsx)("small",{children:r("JOB.PROCESS_STARTED_AT")}),(0,s.jsx)("time",{children:el(t.processedOn,i.language,c)}),!!t.processedBy&&(0,s.jsx)("small",{children:r("JOB.PROCESSED_BY",{processedBy:t.processedBy})})]}),!!t.finishedOn&&(0,s.jsxs)("li",{children:[(0,s.jsx)("small",{children:es(t.finishedOn,t.processedOn||0,r)}),(0,s.jsx)("small",{children:r(t.isFailed&&l!==n.n.active?"JOB.FAILED_AT":"JOB.FINISHED_AT")}),(0,s.jsx)("time",{children:el(t.finishedOn,i.language,c)})]})]})})},er={card:"card-BRjpw_",contentWrapper:"contentWrapper-Aj9Wmy",title:"title-wDT4dB",sideInfo:"sideInfo-a9IFbv",header:"header-Lw4QCc",details:"details-JRsI9B",collapseBtn:"collapseBtn-J6rMz7",content:"content-SHI77x",progress:"progress-POOya3",jobLink:"jobLink-wmCWQg",externalLink:"externalLink-dX0ReW",titleWithLink:"titleWithLink-UFeEb7"},ea=[n.n.active,n.n.completed],ei=e=>{var t,l,m,j,g,v;let{job:w,status:f,actions:b,readOnlyMode:L,allowRetries:y,jobUrl:N}=e,{t:O}=(0,a.$G)(),{collapseJob:B}=(0,u.F)(),[C,J]=r.useState(),T=!N||C||!B,A=B&&N,S=(0,s.jsxs)("h4",{children:[/^\d+$/.test(`${w.id}`)?"#":"",w.id]});return(0,s.jsx)(x.fC,{asChild:!0,open:T,children:(0,s.jsxs)(c.Z,{className:er.card,children:[(0,s.jsxs)("div",{className:er.header,children:[(0,s.jsxs)("div",{className:er.titleWithLink,children:[N?(0,s.jsx)(i.rU,{className:er.jobLink,to:N,children:S}):S,w.externalUrl&&(0,s.jsx)("a",{className:er.externalLink,href:w.externalUrl.href,target:"_blank",rel:"noopener noreferrer",children:w.externalUrl.displayText??(0,s.jsx)(d,{})})]}),A&&(0,s.jsx)(p.z,{className:er.collapseBtn,onClick:()=>J(!T),children:T?(0,s.jsx)(h,{}):(0,s.jsx)(o,{})})]}),(0,s.jsx)(x.VY,{asChild:!0,children:(0,s.jsxs)("div",{className:er.details,children:[(0,s.jsx)("div",{className:er.sideInfo,children:(0,s.jsx)(en,{job:w,status:f})}),(0,s.jsxs)("div",{className:er.contentWrapper,children:[(0,s.jsxs)("div",{className:er.title,children:[(0,s.jsxs)("h5",{children:[w.name,w.attempts>1&&(0,s.jsx)("span",{children:O("JOB.ATTEMPTS",{attempts:w.attempts})}),!!(null==(l=w.opts)||null==(t=l.repeat)?void 0:t.count)&&(0,s.jsx)("span",{children:O(`JOB.REPEAT${(null==(j=w.opts)||null==(m=j.repeat)?void 0:m.limit)?"_WITH_LIMIT":""}`,{count:w.opts.repeat.count,limit:null==(v=w.opts)||null==(g=v.repeat)?void 0:g.limit})})]}),!L&&(0,s.jsx)(G,{status:f,actions:b,allowRetries:y})]}),(0,s.jsxs)("div",{className:er.content,children:[(0,s.jsx)(R,{status:f,job:w,actions:b}),(0,s.jsx)(P,{progress:w.progress,status:w.isFailed&&!ea.includes(f)?n.n.failed:f,className:er.progress})]})]})]})})]})})}},1071:function(e,t,l){l.d(t,{M:()=>r});var s=l(52322);l(2784);let n={stickyHeader:"stickyHeader-Woc0cg",actionContainer:"actionContainer-gipYSV"},r=e=>{let{actions:t,children:l}=e;return(0,s.jsxs)("div",{className:n.stickyHeader,children:[l,!!t&&(0,s.jsx)("div",{className:n.actionContainer,children:t})]})}},1542:function(e,t,l){l.d(t,{X:()=>a,r:()=>i});var s=l(96274),n=l(2784),r=l(6294);let a=["Data","Options","Logs","Error"];function i(e){let[t,l]=(0,n.useState)([]),{defaultJobTab:i}=(0,r.F)(),[c,o]=(0,n.useState)(t.find(e=>e===i)||t[0]);return(0,n.useEffect)(()=>{let t=a.filter(e=>"Error"!==e);l(t=e===s.n.failed?["Error",...t]:[...t,"Error"])},[e]),(0,n.useEffect)(()=>{o(t.includes(i)?i:t[0])},[i,t]),{tabs:null==t?void 0:t.map(e=>({title:e,isActive:e===c,selectTab:()=>o(e)})),selectedTab:c}}},83089:function(e,t,l){l.d(t,{t:()=>x});var s=l(59382),n=l(9210),r=l(33237),a=l(7267),i=l(47386),c=l(22623),o=l(35529),d=l(85950),u=l(2575),h=l(6294);let p=(0,n.Ue)(e=>({job:null,status:"latest",loading:!0,updateJob:(t,l)=>e(()=>({job:t,status:l,loading:!1}))}));function x(){let e=(0,c.h)(),t=(0,i.u)(),l=function(){let{pathname:e}=(0,a.TH)(),t=(0,a.LX)(e,{path:["/queue/:name/:jobId"],exact:!1,strict:!1});return decodeURIComponent((null==t?void 0:t.params.jobId)||"")}(),{actions:{updateQueues:n}}=(0,u.y)(),{t:x}=(0,s.$G)(),{confirmJobActions:m,pollingInterval:j}=(0,h.F)(e=>{let{confirmJobActions:t,pollingInterval:l}=e;return{confirmJobActions:t,pollingInterval:l}}),{job:g,status:v,loading:w,updateJob:f}=p(e=>e),{openConfirm:b}=(0,o.N)(),L=()=>e.getJob(t,l).then(e=>{let{job:t,status:l}=e;return f(t,l)}),y=(0,r.G)(l?L:n,b);return{job:g,status:v,loading:w,actions:{getJob:L,pollJob:()=>(0,d.Y)(L,j>0?1e3*j:null,[t]),promoteJob:t=>l=>y(()=>e.promoteJob(t,l.id),x("JOB.ACTIONS.CONFIRM.PROMOTE"),m),cleanJob:t=>l=>y(()=>e.cleanJob(t,l.id),x("JOB.ACTIONS.CONFIRM.CLEAN"),m),getJobLogs:t=>l=>()=>e.getJobLogs(t,l.id),retryJob:(t,l)=>s=>y(()=>e.retryJob(t,s.id,l),x("JOB.ACTIONS.CONFIRM.RETRY"),m),updateJobData:(t,l,s)=>y(()=>e.updateJobData(t,l.id,s),"",!1)}}}}}]);