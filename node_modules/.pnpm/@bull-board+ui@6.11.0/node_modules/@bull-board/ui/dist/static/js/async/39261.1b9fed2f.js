(self.webpackChunk_bull_board_ui=self.webpackChunk_bull_board_ui||[]).push([["39261"],{22348:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){return function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=t.width?String(t.width):e.defaultWidth;return e.formats[n]||e.formats[e.defaultWidth]}},e.exports=t.default},84738:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){return function(t,n){var o;if("formatting"===(null!=n&&n.context?String(n.context):"standalone")&&e.formattingValues){var r=e.defaultFormattingWidth||e.defaultWidth,a=null!=n&&n.width?String(n.width):r;o=e.formattingValues[a]||e.formattingValues[r]}else{var i=e.defaultWidth,u=null!=n&&n.width?String(n.width):e.defaultWidth;o=e.values[u]||e.values[i]}return o[e.argumentCallback?e.argumentCallback(t):t]}},e.exports=t.default},89653:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){return function(t){var n,o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=o.width,a=r&&e.matchPatterns[r]||e.matchPatterns[e.defaultMatchWidth],i=t.match(a);if(!i)return null;var u=i[0],d=r&&e.parsePatterns[r]||e.parsePatterns[e.defaultParseWidth],s=Array.isArray(d)?function(e,t){for(var n=0;n<e.length;n++)if(t(e[n]))return n}(d,function(e){return e.test(u)}):function(e,t){for(var n in e)if(e.hasOwnProperty(n)&&t(e[n]))return n}(d,function(e){return e.test(u)});return n=e.valueCallback?e.valueCallback(s):s,{value:n=o.valueCallback?o.valueCallback(n):n,rest:t.slice(u.length)}}},e.exports=t.default},71604:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){return function(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},o=t.match(e.matchPattern);if(!o)return null;var r=o[0],a=t.match(e.parsePattern);if(!a)return null;var i=e.valueCallback?e.valueCallback(a[0]):a[0];return{value:i=n.valueCallback?n.valueCallback(i):i,rest:t.slice(r.length)}}},e.exports=t.default},60967:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n={lessThanXSeconds:{standalone:{one:"manner w\xe9i eng Sekonn",other:"manner w\xe9i {{count}} Sekonnen"},withPreposition:{one:"manner w\xe9i enger Sekonn",other:"manner w\xe9i {{count}} Sekonnen"}},xSeconds:{standalone:{one:"eng Sekonn",other:"{{count}} Sekonnen"},withPreposition:{one:"enger Sekonn",other:"{{count}} Sekonnen"}},halfAMinute:{standalone:"eng hallef Minutt",withPreposition:"enger hallwer Minutt"},lessThanXMinutes:{standalone:{one:"manner w\xe9i eng Minutt",other:"manner w\xe9i {{count}} Minutten"},withPreposition:{one:"manner w\xe9i enger Minutt",other:"manner w\xe9i {{count}} Minutten"}},xMinutes:{standalone:{one:"eng Minutt",other:"{{count}} Minutten"},withPreposition:{one:"enger Minutt",other:"{{count}} Minutten"}},aboutXHours:{standalone:{one:"ongef\xe9ier eng Stonn",other:"ongef\xe9ier {{count}} Stonnen"},withPreposition:{one:"ongef\xe9ier enger Stonn",other:"ongef\xe9ier {{count}} Stonnen"}},xHours:{standalone:{one:"eng Stonn",other:"{{count}} Stonnen"},withPreposition:{one:"enger Stonn",other:"{{count}} Stonnen"}},xDays:{standalone:{one:"een Dag",other:"{{count}} Deeg"},withPreposition:{one:"engem Dag",other:"{{count}} Deeg"}},aboutXWeeks:{standalone:{one:"ongef\xe9ier eng Woch",other:"ongef\xe9ier {{count}} Wochen"},withPreposition:{one:"ongef\xe9ier enger Woche",other:"ongef\xe9ier {{count}} Wochen"}},xWeeks:{standalone:{one:"eng Woch",other:"{{count}} Wochen"},withPreposition:{one:"enger Woch",other:"{{count}} Wochen"}},aboutXMonths:{standalone:{one:"ongef\xe9ier ee Mount",other:"ongef\xe9ier {{count}} M\xe9int"},withPreposition:{one:"ongef\xe9ier engem Mount",other:"ongef\xe9ier {{count}} M\xe9int"}},xMonths:{standalone:{one:"ee Mount",other:"{{count}} M\xe9int"},withPreposition:{one:"engem Mount",other:"{{count}} M\xe9int"}},aboutXYears:{standalone:{one:"ongef\xe9ier ee Joer",other:"ongef\xe9ier {{count}} Joer"},withPreposition:{one:"ongef\xe9ier engem Joer",other:"ongef\xe9ier {{count}} Joer"}},xYears:{standalone:{one:"ee Joer",other:"{{count}} Joer"},withPreposition:{one:"engem Joer",other:"{{count}} Joer"}},overXYears:{standalone:{one:"m\xe9i w\xe9i ee Joer",other:"m\xe9i w\xe9i {{count}} Joer"},withPreposition:{one:"m\xe9i w\xe9i engem Joer",other:"m\xe9i w\xe9i {{count}} Joer"}},almostXYears:{standalone:{one:"bal ee Joer",other:"bal {{count}} Joer"},withPreposition:{one:"bal engem Joer",other:"bal {{count}} Joer"}}},o=["d","h","n","t","z"],r=["a,","e","i","o","u"],a=[0,1,2,3,8,9],i=[40,50,60,70];function u(e){var t=e.charAt(0).toLowerCase();if(-1!=r.indexOf(t)||-1!=o.indexOf(t))return!0;var n=e.split(" ")[0],u=parseInt(n);return!isNaN(u)&&-1!=a.indexOf(u%10)&&-1==i.indexOf(parseInt(n.substring(0,2)))}t.default=function(e,t,o){var r,a=n[e],i=null!=o&&o.addSuffix?a.withPreposition:a.standalone;if(r="string"==typeof i?i:1===t?i.one:i.other.replace("{{count}}",String(t)),null!=o&&o.addSuffix)if(o.comparison&&o.comparison>0)return"a"+(u(r)?"n":"")+" "+r;else return"viru"+(u(r)?"n":"")+" "+r;return r},e.exports=t.default},91136:function(e,t,n){"use strict";var o=n(15755).default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=o(n(22348));t.default={date:(0,r.default)({formats:{full:"EEEE, do MMMM y",long:"do MMMM y",medium:"do MMM y",short:"dd.MM.yy"},defaultWidth:"full"}),time:(0,r.default)({formats:{full:"HH:mm:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"HH:mm"},defaultWidth:"full"}),dateTime:(0,r.default)({formats:{full:"{{date}} 'um' {{time}}",long:"{{date}} 'um' {{time}}",medium:"{{date}} {{time}}",short:"{{date}} {{time}}"},defaultWidth:"full"})},e.exports=t.default},56316:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n={lastWeek:function(e){var t=e.getUTCDay(),n="'l\xe4schte";return(2===t||4===t)&&(n+="n"),n+="' eeee 'um' p"},yesterday:"'g\xebschter um' p",today:"'haut um' p",tomorrow:"'moien um' p",nextWeek:"eeee 'um' p",other:"P"};t.default=function(e,t,o,r){var a=n[e];return"function"==typeof a?a(t):a},e.exports=t.default},58287:function(e,t,n){"use strict";var o=n(15755).default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=o(n(84738));t.default={ordinalNumber:function(e,t){return Number(e)+"."},era:(0,r.default)({values:{narrow:["v.Chr.","n.Chr."],abbreviated:["v.Chr.","n.Chr."],wide:["viru Christus","no Christus"]},defaultWidth:"wide"}),quarter:(0,r.default)({values:{narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1. Quartal","2. Quartal","3. Quartal","4. Quartal"]},defaultWidth:"wide",argumentCallback:function(e){return e-1}}),month:(0,r.default)({values:{narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","M\xe4e","Abr","Mee","Jun","Jul","Aug","Sep","Okt","Nov","Dez"],wide:["Januar","Februar","M\xe4erz","Abr\xebll","Mee","Juni","Juli","August","September","Oktober","November","Dezember"]},defaultWidth:"wide"}),day:(0,r.default)({values:{narrow:["S","M","D","M","D","F","S"],short:["So","M\xe9","D\xeb","M\xeb","Do","Fr","Sa"],abbreviated:["So.","M\xe9.","D\xeb.","M\xeb.","Do.","Fr.","Sa."],wide:["Sonndeg","M\xe9indeg","D\xebnschdeg","M\xebttwoch","Donneschdeg","Freideg","Samschdeg"]},defaultWidth:"wide"}),dayPeriod:(0,r.default)({values:{narrow:{am:"mo.",pm:"nom\xeb.",midnight:"M\xebtternuecht",noon:"M\xebtteg",morning:"Moien",afternoon:"Nom\xebtteg",evening:"Owend",night:"Nuecht"},abbreviated:{am:"moies",pm:"nom\xebttes",midnight:"M\xebtternuecht",noon:"M\xebtteg",morning:"Moien",afternoon:"Nom\xebtteg",evening:"Owend",night:"Nuecht"},wide:{am:"moies",pm:"nom\xebttes",midnight:"M\xebtternuecht",noon:"M\xebtteg",morning:"Moien",afternoon:"Nom\xebtteg",evening:"Owend",night:"Nuecht"}},defaultWidth:"wide",formattingValues:{narrow:{am:"mo.",pm:"nom.",midnight:"M\xebtternuecht",noon:"m\xebttes",morning:"moies",afternoon:"nom\xebttes",evening:"owes",night:"nuets"},abbreviated:{am:"moies",pm:"nom\xebttes",midnight:"M\xebtternuecht",noon:"m\xebttes",morning:"moies",afternoon:"nom\xebttes",evening:"owes",night:"nuets"},wide:{am:"moies",pm:"nom\xebttes",midnight:"M\xebtternuecht",noon:"m\xebttes",morning:"moies",afternoon:"nom\xebttes",evening:"owes",night:"nuets"}},defaultFormattingWidth:"wide"})},e.exports=t.default},90457:function(e,t,n){"use strict";var o=n(15755).default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=o(n(89653));t.default={ordinalNumber:(0,o(n(71604)).default)({matchPattern:/^(\d+)(\.)?/i,parsePattern:/\d+/i,valueCallback:function(e){return parseInt(e,10)}}),era:(0,r.default)({matchPatterns:{narrow:/^(v\.? ?Chr\.?|n\.? ?Chr\.?)/i,abbreviated:/^(v\.? ?Chr\.?|n\.? ?Chr\.?)/i,wide:/^(viru Christus|virun eiser Zäitrechnung|no Christus|eiser Zäitrechnung)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^v/i,/^n/i]},defaultParseWidth:"any"}),quarter:(0,r.default)({matchPatterns:{narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](\.)? Quartal/i},defaultMatchWidth:"wide",parsePatterns:{any:[/1/i,/2/i,/3/i,/4/i]},defaultParseWidth:"any",valueCallback:function(e){return e+1}}),month:(0,r.default)({matchPatterns:{narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mäe|abr|mee|jun|jul|aug|sep|okt|nov|dez)/i,wide:/^(januar|februar|mäerz|abrëll|mee|juni|juli|august|september|oktober|november|dezember)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mä/i,/^ab/i,/^me/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},defaultParseWidth:"any"}),day:(0,r.default)({matchPatterns:{narrow:/^[smdf]/i,short:/^(so|mé|dë|më|do|fr|sa)/i,abbreviated:/^(son?|méi?|dën?|mët?|don?|fre?|sam?)\.?/i,wide:/^(sonndeg|méindeg|dënschdeg|mëttwoch|donneschdeg|freideg|samschdeg)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^so/i,/^mé/i,/^dë/i,/^më/i,/^do/i,/^f/i,/^sa/i]},defaultParseWidth:"any"}),dayPeriod:(0,r.default)({matchPatterns:{narrow:/^(mo\.?|nomë\.?|Mëtternuecht|mëttes|moies|nomëttes|owes|nuets)/i,abbreviated:/^(moi\.?|nomët\.?|Mëtternuecht|mëttes|moies|nomëttes|owes|nuets)/i,wide:/^(moies|nomëttes|Mëtternuecht|mëttes|moies|nomëttes|owes|nuets)/i},defaultMatchWidth:"wide",parsePatterns:{any:{am:/^m/i,pm:/^n/i,midnight:/^Mëtter/i,noon:/^mëttes/i,morning:/moies/i,afternoon:/nomëttes/i,evening:/owes/i,night:/nuets/i}},defaultParseWidth:"any"})},e.exports=t.default},43163:function(e,t,n){"use strict";var o=n(15755).default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=o(n(60967)),a=o(n(91136)),i=o(n(56316)),u=o(n(58287)),d=o(n(90457));t.default={code:"lb",formatDistance:r.default,formatLong:a.default,formatRelative:i.default,localize:u.default,match:d.default,options:{weekStartsOn:1,firstWeekContainsDate:4}},e.exports=t.default},15755:function(e){e.exports=function(e){return e&&e.__esModule?e:{default:e}},e.exports.__esModule=!0,e.exports.default=e.exports}}]);