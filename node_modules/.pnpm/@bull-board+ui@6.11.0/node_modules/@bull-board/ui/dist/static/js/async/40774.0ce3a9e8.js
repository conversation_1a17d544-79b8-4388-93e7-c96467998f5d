(self.webpackChunk_bull_board_ui=self.webpackChunk_bull_board_ui||[]).push([["40774"],{22348:function(t,e){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){return function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},a=e.width?String(e.width):t.defaultWidth;return t.formats[a]||t.formats[t.defaultWidth]}},t.exports=e.default},84738:function(t,e){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){return function(e,a){var n;if("formatting"===(null!=a&&a.context?String(a.context):"standalone")&&t.formattingValues){var i=t.defaultFormattingWidth||t.defaultWidth,u=null!=a&&a.width?String(a.width):i;n=t.formattingValues[u]||t.formattingValues[i]}else{var o=t.defaultWidth,r=null!=a&&a.width?String(a.width):t.defaultWidth;n=t.values[r]||t.values[o]}return n[t.argumentCallback?t.argumentCallback(e):e]}},t.exports=e.default},89653:function(t,e){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){return function(e){var a,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=n.width,u=i&&t.matchPatterns[i]||t.matchPatterns[t.defaultMatchWidth],o=e.match(u);if(!o)return null;var r=o[0],d=i&&t.parsePatterns[i]||t.parsePatterns[t.defaultParseWidth],l=Array.isArray(d)?function(t,e){for(var a=0;a<t.length;a++)if(e(t[a]))return a}(d,function(t){return t.test(r)}):function(t,e){for(var a in t)if(t.hasOwnProperty(a)&&e(t[a]))return a}(d,function(t){return t.test(r)});return a=t.valueCallback?t.valueCallback(l):l,{value:a=n.valueCallback?n.valueCallback(a):a,rest:e.slice(r.length)}}},t.exports=e.default},71604:function(t,e){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){return function(e){var a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=e.match(t.matchPattern);if(!n)return null;var i=n[0],u=e.match(t.parsePattern);if(!u)return null;var o=t.valueCallback?t.valueCallback(u[0]):u[0];return{value:o=a.valueCallback?a.valueCallback(o):o,rest:e.slice(i.length)}}},t.exports=e.default},76193:function(t,e){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a={lessThanXSeconds:{one:{default:"ஒரு வினாடிக்கு குறைவாக",in:"ஒரு வினாடிக்குள்",ago:"ஒரு வினாடிக்கு முன்பு"},other:{default:"{{count}} வினாடிகளுக்கு குறைவாக",in:"{{count}} வினாடிகளுக்குள்",ago:"{{count}} வினாடிகளுக்கு முன்பு"}},xSeconds:{one:{default:"1 வினாடி",in:"1 வினாடியில்",ago:"1 வினாடி முன்பு"},other:{default:"{{count}} விநாடிகள்",in:"{{count}} வினாடிகளில்",ago:"{{count}} விநாடிகளுக்கு முன்பு"}},halfAMinute:{default:"அரை நிமிடம்",in:"அரை நிமிடத்தில்",ago:"அரை நிமிடம் முன்பு"},lessThanXMinutes:{one:{default:"ஒரு நிமிடத்திற்கும் குறைவாக",in:"ஒரு நிமிடத்திற்குள்",ago:"ஒரு நிமிடத்திற்கு முன்பு"},other:{default:"{{count}} நிமிடங்களுக்கும் குறைவாக",in:"{{count}} நிமிடங்களுக்குள்",ago:"{{count}} நிமிடங்களுக்கு முன்பு"}},xMinutes:{one:{default:"1 நிமிடம்",in:"1 நிமிடத்தில்",ago:"1 நிமிடம் முன்பு"},other:{default:"{{count}} நிமிடங்கள்",in:"{{count}} நிமிடங்களில்",ago:"{{count}} நிமிடங்களுக்கு முன்பு"}},aboutXHours:{one:{default:"சுமார் 1 மணி நேரம்",in:"சுமார் 1 மணி நேரத்தில்",ago:"சுமார் 1 மணி நேரத்திற்கு முன்பு"},other:{default:"சுமார் {{count}} மணி நேரம்",in:"சுமார் {{count}} மணி நேரத்திற்கு முன்பு",ago:"சுமார் {{count}} மணி நேரத்தில்"}},xHours:{one:{default:"1 மணி நேரம்",in:"1 மணி நேரத்தில்",ago:"1 மணி நேரத்திற்கு முன்பு"},other:{default:"{{count}} மணி நேரம்",in:"{{count}} மணி நேரத்தில்",ago:"{{count}} மணி நேரத்திற்கு முன்பு"}},xDays:{one:{default:"1 நாள்",in:"1 நாளில்",ago:"1 நாள் முன்பு"},other:{default:"{{count}} நாட்கள்",in:"{{count}} நாட்களில்",ago:"{{count}} நாட்களுக்கு முன்பு"}},aboutXWeeks:{one:{default:"சுமார் 1 வாரம்",in:"சுமார் 1 வாரத்தில்",ago:"சுமார் 1 வாரம் முன்பு"},other:{default:"சுமார் {{count}} வாரங்கள்",in:"சுமார் {{count}} வாரங்களில்",ago:"சுமார் {{count}} வாரங்களுக்கு முன்பு"}},xWeeks:{one:{default:"1 வாரம்",in:"1 வாரத்தில்",ago:"1 வாரம் முன்பு"},other:{default:"{{count}} வாரங்கள்",in:"{{count}} வாரங்களில்",ago:"{{count}} வாரங்களுக்கு முன்பு"}},aboutXMonths:{one:{default:"சுமார் 1 மாதம்",in:"சுமார் 1 மாதத்தில்",ago:"சுமார் 1 மாதத்திற்கு முன்பு"},other:{default:"சுமார் {{count}} மாதங்கள்",in:"சுமார் {{count}} மாதங்களில்",ago:"சுமார் {{count}} மாதங்களுக்கு முன்பு"}},xMonths:{one:{default:"1 மாதம்",in:"1 மாதத்தில்",ago:"1 மாதம் முன்பு"},other:{default:"{{count}} மாதங்கள்",in:"{{count}} மாதங்களில்",ago:"{{count}} மாதங்களுக்கு முன்பு"}},aboutXYears:{one:{default:"சுமார் 1 வருடம்",in:"சுமார் 1 ஆண்டில்",ago:"சுமார் 1 வருடம் முன்பு"},other:{default:"சுமார் {{count}} ஆண்டுகள்",in:"சுமார் {{count}} ஆண்டுகளில்",ago:"சுமார் {{count}} ஆண்டுகளுக்கு முன்பு"}},xYears:{one:{default:"1 வருடம்",in:"1 ஆண்டில்",ago:"1 வருடம் முன்பு"},other:{default:"{{count}} ஆண்டுகள்",in:"{{count}} ஆண்டுகளில்",ago:"{{count}} ஆண்டுகளுக்கு முன்பு"}},overXYears:{one:{default:"1 வருடத்திற்கு மேல்",in:"1 வருடத்திற்கும் மேலாக",ago:"1 வருடம் முன்பு"},other:{default:"{{count}} ஆண்டுகளுக்கும் மேலாக",in:"{{count}} ஆண்டுகளில்",ago:"{{count}} ஆண்டுகளுக்கு முன்பு"}},almostXYears:{one:{default:"கிட்டத்தட்ட 1 வருடம்",in:"கிட்டத்தட்ட 1 ஆண்டில்",ago:"கிட்டத்தட்ட 1 வருடம் முன்பு"},other:{default:"கிட்டத்தட்ட {{count}} ஆண்டுகள்",in:"கிட்டத்தட்ட {{count}} ஆண்டுகளில்",ago:"கிட்டத்தட்ட {{count}} ஆண்டுகளுக்கு முன்பு"}}};e.default=function(t,e,n){var i=null!=n&&n.addSuffix?n.comparison&&n.comparison>0?"in":"ago":"default",u=a[t];return void 0===u.one?u[i]:1===e?u.one[i]:u.other[i].replace("{{count}}",String(e))},t.exports=e.default},17548:function(t,e,a){"use strict";var n=a(15755).default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=n(a(22348));e.default={date:(0,i.default)({formats:{full:"EEEE, d MMMM, y",long:"d MMMM, y",medium:"d MMM, y",short:"d/M/yy"},defaultWidth:"full"}),time:(0,i.default)({formats:{full:"a h:mm:ss zzzz",long:"a h:mm:ss z",medium:"a h:mm:ss",short:"a h:mm"},defaultWidth:"full"}),dateTime:(0,i.default)({formats:{full:"{{date}} {{time}}",long:"{{date}} {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},defaultWidth:"full"})},t.exports=e.default},82908:function(t,e){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a={lastWeek:"'கடந்த' eeee p 'மணிக்கு'",yesterday:"'நேற்று ' p 'மணிக்கு'",today:"'இன்று ' p 'மணிக்கு'",tomorrow:"'நாளை ' p 'மணிக்கு'",nextWeek:"eeee p 'மணிக்கு'",other:"P"};e.default=function(t,e,n,i){return a[t]},t.exports=e.default},48351:function(t,e,a){"use strict";var n=a(15755).default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=n(a(84738));e.default={ordinalNumber:function(t,e){return String(t)},era:(0,i.default)({values:{narrow:["கி.மு.","கி.பி."],abbreviated:["கி.மு.","கி.பி."],wide:["கிறிஸ்துவுக்கு முன்","அன்னோ டோமினி"]},defaultWidth:"wide"}),quarter:(0,i.default)({values:{narrow:["1","2","3","4"],abbreviated:["காலா.1","காலா.2","காலா.3","காலா.4"],wide:["ஒன்றாம் காலாண்டு","இரண்டாம் காலாண்டு","மூன்றாம் காலாண்டு","நான்காம் காலாண்டு"]},defaultWidth:"wide",argumentCallback:function(t){return t-1}}),month:(0,i.default)({values:{narrow:["ஜ","பி","மா","ஏ","மே","ஜூ","ஜூ","ஆ","செ","அ","ந","டி"],abbreviated:["ஜன.","பிப்.","மார்.","ஏப்.","மே","ஜூன்","ஜூலை","ஆக.","செப்.","அக்.","நவ.","டிச."],wide:["ஜனவரி","பிப்ரவரி","மார்ச்","ஏப்ரல்","மே","ஜூன்","ஜூலை","ஆகஸ்ட்","செப்டம்பர்","அக்டோபர்","நவம்பர்","டிசம்பர்"]},defaultWidth:"wide"}),day:(0,i.default)({values:{narrow:["ஞா","தி","செ","பு","வி","வெ","ச"],short:["ஞா","தி","செ","பு","வி","வெ","ச"],abbreviated:["ஞாயி.","திங்.","செவ்.","புத.","வியா.","வெள்.","சனி"],wide:["ஞாயிறு","திங்கள்","செவ்வாய்","புதன்","வியாழன்","வெள்ளி","சனி"]},defaultWidth:"wide"}),dayPeriod:(0,i.default)({values:{narrow:{am:"மு.ப",pm:"பி.ப",midnight:"நள்.",noon:"நண்.",morning:"கா.",afternoon:"மதி.",evening:"மா.",night:"இர."},abbreviated:{am:"முற்பகல்",pm:"பிற்பகல்",midnight:"நள்ளிரவு",noon:"நண்பகல்",morning:"காலை",afternoon:"மதியம்",evening:"மாலை",night:"இரவு"},wide:{am:"முற்பகல்",pm:"பிற்பகல்",midnight:"நள்ளிரவு",noon:"நண்பகல்",morning:"காலை",afternoon:"மதியம்",evening:"மாலை",night:"இரவு"}},defaultWidth:"wide",formattingValues:{narrow:{am:"மு.ப",pm:"பி.ப",midnight:"நள்.",noon:"நண்.",morning:"கா.",afternoon:"மதி.",evening:"மா.",night:"இர."},abbreviated:{am:"முற்பகல்",pm:"பிற்பகல்",midnight:"நள்ளிரவு",noon:"நண்பகல்",morning:"காலை",afternoon:"மதியம்",evening:"மாலை",night:"இரவு"},wide:{am:"முற்பகல்",pm:"பிற்பகல்",midnight:"நள்ளிரவு",noon:"நண்பகல்",morning:"காலை",afternoon:"மதியம்",evening:"மாலை",night:"இரவு"}},defaultFormattingWidth:"wide"})},t.exports=e.default},85567:function(t,e,a){"use strict";var n=a(15755).default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=n(a(89653));e.default={ordinalNumber:(0,n(a(71604)).default)({matchPattern:/^(\d+)(வது)?/i,parsePattern:/\d+/i,valueCallback:function(t){return parseInt(t,10)}}),era:(0,i.default)({matchPatterns:{narrow:/^(கி.மு.|கி.பி.)/i,abbreviated:/^(கி\.?\s?மு\.?|கி\.?\s?பி\.?)/,wide:/^(கிறிஸ்துவுக்கு\sமுன்|அன்னோ\sடோமினி)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/கி\.?\s?மு\.?/,/கி\.?\s?பி\.?/]},defaultParseWidth:"any"}),quarter:(0,i.default)({matchPatterns:{narrow:/^[1234]/i,abbreviated:/^காலா.[1234]/i,wide:/^(ஒன்றாம்|இரண்டாம்|மூன்றாம்|நான்காம்) காலாண்டு/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/1/i,/2/i,/3/i,/4/i],any:[/(1|காலா.1|ஒன்றாம்)/i,/(2|காலா.2|இரண்டாம்)/i,/(3|காலா.3|மூன்றாம்)/i,/(4|காலா.4|நான்காம்)/i]},defaultParseWidth:"any",valueCallback:function(t){return t+1}}),month:(0,i.default)({matchPatterns:{narrow:/^(ஜ|பி|மா|ஏ|மே|ஜூ|ஆ|செ|அ|ந|டி)$/i,abbreviated:/^(ஜன.|பிப்.|மார்.|ஏப்.|மே|ஜூன்|ஜூலை|ஆக.|செப்.|அக்.|நவ.|டிச.)/i,wide:/^(ஜனவரி|பிப்ரவரி|மார்ச்|ஏப்ரல்|மே|ஜூன்|ஜூலை|ஆகஸ்ட்|செப்டம்பர்|அக்டோபர்|நவம்பர்|டிசம்பர்)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^ஜ$/i,/^பி/i,/^மா/i,/^ஏ/i,/^மே/i,/^ஜூ/i,/^ஜூ/i,/^ஆ/i,/^செ/i,/^அ/i,/^ந/i,/^டி/i],any:[/^ஜன/i,/^பி/i,/^மா/i,/^ஏ/i,/^மே/i,/^ஜூன்/i,/^ஜூலை/i,/^ஆ/i,/^செ/i,/^அ/i,/^ந/i,/^டி/i]},defaultParseWidth:"any"}),day:(0,i.default)({matchPatterns:{narrow:/^(ஞா|தி|செ|பு|வி|வெ|ச)/i,short:/^(ஞா|தி|செ|பு|வி|வெ|ச)/i,abbreviated:/^(ஞாயி.|திங்.|செவ்.|புத.|வியா.|வெள்.|சனி)/i,wide:/^(ஞாயிறு|திங்கள்|செவ்வாய்|புதன்|வியாழன்|வெள்ளி|சனி)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^ஞா/i,/^தி/i,/^செ/i,/^பு/i,/^வி/i,/^வெ/i,/^ச/i],any:[/^ஞா/i,/^தி/i,/^செ/i,/^பு/i,/^வி/i,/^வெ/i,/^ச/i]},defaultParseWidth:"any"}),dayPeriod:(0,i.default)({matchPatterns:{narrow:/^(மு.ப|பி.ப|நள்|நண்|காலை|மதியம்|மாலை|இரவு)/i,any:/^(மு.ப|பி.ப|முற்பகல்|பிற்பகல்|நள்ளிரவு|நண்பகல்|காலை|மதியம்|மாலை|இரவு)/i},defaultMatchWidth:"any",parsePatterns:{any:{am:/^மு/i,pm:/^பி/i,midnight:/^நள்/i,noon:/^நண்/i,morning:/காலை/i,afternoon:/மதியம்/i,evening:/மாலை/i,night:/இரவு/i}},defaultParseWidth:"any"})},t.exports=e.default},86455:function(t,e,a){"use strict";var n=a(15755).default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=n(a(76193)),u=n(a(17548)),o=n(a(82908)),r=n(a(48351)),d=n(a(85567));e.default={code:"ta",formatDistance:i.default,formatLong:u.default,formatRelative:o.default,localize:r.default,match:d.default,options:{weekStartsOn:1,firstWeekContainsDate:4}},t.exports=e.default},15755:function(t){t.exports=function(t){return t&&t.__esModule?t:{default:t}},t.exports.__esModule=!0,t.exports.default=t.exports}}]);