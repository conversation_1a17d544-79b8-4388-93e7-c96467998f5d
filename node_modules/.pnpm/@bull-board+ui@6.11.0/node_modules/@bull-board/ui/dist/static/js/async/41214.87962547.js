"use strict";(self.webpackChunk_bull_board_ui=self.webpackChunk_bull_board_ui||[]).push([["41214"],{61978:function(e,a,o){o.r(a),o.d(a,{JobPage:()=>f});var t=o(52322),l=o(67239),s=o(2784),n=o(59382),d=o(7267),r=o(47933),i=o(10175),u=o(58411),b=o(1071),c=o(9268),p=o(83089),J=o(84541),j=o(68850),h=o(35252),m=o(89458);let O=s.lazy(()=>Promise.all([o.e("76727"),o.e("37136")]).then(o.bind(o,58518)).then(e=>{let{AddJobModal:a}=e;return{default:a}})),x=s.lazy(()=>Promise.all([o.e("76727"),o.e("7876")]).then(o.bind(o,97097)).then(e=>{let{UpdateJobDataModal:a}=e;return{default:a}})),f=()=>{let{t:e}=(0,n.$G)(),a=(0,d.k6)(),o=(0,c.O)(),{job:f,status:g,actions:U}=(0,p.t)(),_=(0,j.U)(),y=(0,J.d)();if(U.pollJob(),!o)return(0,t.jsx)("section",{children:e("QUEUE.NOT_FOUND")});if(!f)return(0,t.jsx)("section",{children:e("JOB.NOT_FOUND")});let D=async()=>{await U.cleanJob(o.name)(f)(),a.replace(h.O.queuePage(o.name,_))};return(0,t.jsxs)("section",{children:[(0,t.jsx)(b.M,{actions:(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(r.rU,{className:(0,l.Z)(m.Z.button,m.Z.default),to:h.O.queuePage(o.name,_),children:(0,t.jsx)(i.Y,{})}),(0,t.jsxs)("div",{children:[" ",e("JOB.STATUS",{status:g.toLocaleUpperCase()})]})]})}),(0,t.jsx)(u.t,{job:f,status:g,actions:{cleanJob:D,promoteJob:U.promoteJob(o.name)(f),retryJob:U.retryJob(o.name,g)(f),getJobLogs:U.getJobLogs(o.name)(f),updateJobData:()=>y.open("updateJobData"),duplicateJob:()=>y.open("addJob")},readOnlyMode:o.readOnlyMode,allowRetries:(f.isFailed||o.allowCompletedRetries)&&o.allowRetries},f.id),(0,t.jsxs)(s.Suspense,{fallback:null,children:[y.isMounted("addJob")&&(0,t.jsx)(O,{open:y.isOpen("addJob"),onClose:y.close("addJob"),job:f}),y.isMounted("updateJobData")&&(0,t.jsx)(x,{open:y.isOpen("updateJobData"),onClose:y.close("updateJobData"),job:f})]})]})}}}]);