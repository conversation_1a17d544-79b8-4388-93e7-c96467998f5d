"use strict";(self.webpackChunk_bull_board_ui=self.webpackChunk_bull_board_ui||[]).push([["45223"],{33959:function(n,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o={lessThanXSeconds:{one:"mwens pase yon segond",other:"mwens pase {{count}} segond"},xSeconds:{one:"1 segond",other:"{{count}} segond"},halfAMinute:"30 segond",lessThanXMinutes:{one:"mwens pase yon minit",other:"mwens pase {{count}} minit"},xMinutes:{one:"1 minit",other:"{{count}} minit"},aboutXHours:{one:"anviwon in\xe8",other:"anviwon {{count}} \xe8"},xHours:{one:"1 l\xe8",other:"{{count}} l\xe8"},xDays:{one:"1 jou",other:"{{count}} jou"},aboutXWeeks:{one:"anviwon 1 sem\xe8n",other:"anviwon {{count}} sem\xe8n"},xWeeks:{one:"1 sem\xe8n",other:"{{count}} sem\xe8n"},aboutXMonths:{one:"anviwon 1 mwa",other:"anviwon {{count}} mwa"},xMonths:{one:"1 mwa",other:"{{count}} mwa"},aboutXYears:{one:"anviwon 1 an",other:"anviwon {{count}} an"},xYears:{one:"1 an",other:"{{count}} an"},overXYears:{one:"plis pase 1 an",other:"plis pase {{count}} an"},almostXYears:{one:"pr\xe8ske 1 an",other:"pr\xe8ske {{count}} an"}};e.default=function(n,e,t){var a,s=o[n];if(a="string"==typeof s?s:1===e?s.one:s.other.replace("{{count}}",String(e)),null!=t&&t.addSuffix)if(t.comparison&&t.comparison>0)return"nan "+a;else return"sa f\xe8 "+a;return a},n.exports=e.default}}]);