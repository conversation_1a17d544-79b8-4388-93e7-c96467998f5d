(self.webpackChunk_bull_board_ui=self.webpackChunk_bull_board_ui||[]).push([["47118"],{84738:function(e,a){"use strict";Object.defineProperty(a,"__esModule",{value:!0}),a.default=function(e){return function(a,t){var n;if("formatting"===(null!=t&&t.context?String(t.context):"standalone")&&e.formattingValues){var i=e.defaultFormattingWidth||e.defaultWidth,r=null!=t&&t.width?String(t.width):i;n=e.formattingValues[r]||e.formattingValues[i]}else{var u=e.defaultWidth,l=null!=t&&t.width?String(t.width):e.defaultWidth;n=e.values[l]||e.values[u]}return n[e.argumentCallback?e.argumentCallback(a):a]}},e.exports=a.default},7597:function(e,a,t){"use strict";var n=t(15755).default;Object.defineProperty(a,"__esModule",{value:!0}),a.default=void 0;var i=n(t(84738)),r={narrow:["J","V","M","A","M","J","J","A","S","O","N","D"],abbreviated:["jaan","veebr","m\xe4rts","apr","mai","juuni","juuli","aug","sept","okt","nov","dets"],wide:["jaanuar","veebruar","m\xe4rts","aprill","mai","juuni","juuli","august","september","oktoober","november","detsember"]},u={narrow:["P","E","T","K","N","R","L"],short:["P","E","T","K","N","R","L"],abbreviated:["p\xfchap.","esmasp.","teisip.","kolmap.","neljap.","reede.","laup."],wide:["p\xfchap\xe4ev","esmasp\xe4ev","teisip\xe4ev","kolmap\xe4ev","neljap\xe4ev","reede","laup\xe4ev"]};a.default={ordinalNumber:function(e,a){return Number(e)+"."},era:(0,i.default)({values:{narrow:["e.m.a","m.a.j"],abbreviated:["e.m.a","m.a.j"],wide:["enne meie ajaarvamist","meie ajaarvamise j\xe4rgi"]},defaultWidth:"wide"}),quarter:(0,i.default)({values:{narrow:["1","2","3","4"],abbreviated:["K1","K2","K3","K4"],wide:["1. kvartal","2. kvartal","3. kvartal","4. kvartal"]},defaultWidth:"wide",argumentCallback:function(e){return e-1}}),month:(0,i.default)({values:r,defaultWidth:"wide",formattingValues:r,defaultFormattingWidth:"wide"}),day:(0,i.default)({values:u,defaultWidth:"wide",formattingValues:u,defaultFormattingWidth:"wide"}),dayPeriod:(0,i.default)({values:{narrow:{am:"AM",pm:"PM",midnight:"kesk\xf6\xf6",noon:"keskp\xe4ev",morning:"hommik",afternoon:"p\xe4rastl\xf5una",evening:"\xf5htu",night:"\xf6\xf6"},abbreviated:{am:"AM",pm:"PM",midnight:"kesk\xf6\xf6",noon:"keskp\xe4ev",morning:"hommik",afternoon:"p\xe4rastl\xf5una",evening:"\xf5htu",night:"\xf6\xf6"},wide:{am:"AM",pm:"PM",midnight:"kesk\xf6\xf6",noon:"keskp\xe4ev",morning:"hommik",afternoon:"p\xe4rastl\xf5una",evening:"\xf5htu",night:"\xf6\xf6"}},defaultWidth:"wide",formattingValues:{narrow:{am:"AM",pm:"PM",midnight:"kesk\xf6\xf6l",noon:"keskp\xe4eval",morning:"hommikul",afternoon:"p\xe4rastl\xf5unal",evening:"\xf5htul",night:"\xf6\xf6sel"},abbreviated:{am:"AM",pm:"PM",midnight:"kesk\xf6\xf6l",noon:"keskp\xe4eval",morning:"hommikul",afternoon:"p\xe4rastl\xf5unal",evening:"\xf5htul",night:"\xf6\xf6sel"},wide:{am:"AM",pm:"PM",midnight:"kesk\xf6\xf6l",noon:"keskp\xe4eval",morning:"hommikul",afternoon:"p\xe4rastl\xf5unal",evening:"\xf5htul",night:"\xf6\xf6sel"}},defaultFormattingWidth:"wide"})},e.exports=a.default},15755:function(e){e.exports=function(e){return e&&e.__esModule?e:{default:e}},e.exports.__esModule=!0,e.exports.default=e.exports}}]);