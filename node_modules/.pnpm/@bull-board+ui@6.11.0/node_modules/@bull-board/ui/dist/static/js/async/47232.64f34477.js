(self.webpackChunk_bull_board_ui=self.webpackChunk_bull_board_ui||[]).push([["47232"],{84738:function(e,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.default=function(e){return function(n,t){var o;if("formatting"===(null!=t&&t.context?String(t.context):"standalone")&&e.formattingValues){var d=e.defaultFormattingWidth||e.defaultWidth,r=null!=t&&t.width?String(t.width):d;o=e.formattingValues[r]||e.formattingValues[d]}else{var a=e.defaultWidth,l=null!=t&&t.width?String(t.width):e.defaultWidth;o=e.values[l]||e.values[a]}return o[e.argumentCallback?e.argumentCallback(n):n]}},e.exports=n.default},1373:function(e,n,t){"use strict";var o=t(15755).default;Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var d=o(t(84738));n.default={ordinalNumber:function(e,n){return Number(e)+"."},era:(0,d.default)({values:{narrow:["př. n. l.","n. l."],abbreviated:["př. n. l.","n. l."],wide:["před naš\xedm letopočtem","našeho letopočtu"]},defaultWidth:"wide"}),quarter:(0,d.default)({values:{narrow:["1","2","3","4"],abbreviated:["1. čtvrtlet\xed","2. čtvrtlet\xed","3. čtvrtlet\xed","4. čtvrtlet\xed"],wide:["1. čtvrtlet\xed","2. čtvrtlet\xed","3. čtvrtlet\xed","4. čtvrtlet\xed"]},defaultWidth:"wide",argumentCallback:function(e){return e-1}}),month:(0,d.default)({values:{narrow:["L","\xda","B","D","K","Č","Č","S","Z","Ř","L","P"],abbreviated:["led","\xfano","bře","dub","kvě","čvn","čvc","srp","z\xe1ř","ř\xedj","lis","pro"],wide:["leden","\xfanor","březen","duben","květen","červen","červenec","srpen","z\xe1ř\xed","ř\xedjen","listopad","prosinec"]},defaultWidth:"wide",formattingValues:{narrow:["L","\xda","B","D","K","Č","Č","S","Z","Ř","L","P"],abbreviated:["led","\xfano","bře","dub","kvě","čvn","čvc","srp","z\xe1ř","ř\xedj","lis","pro"],wide:["ledna","\xfanora","března","dubna","května","června","července","srpna","z\xe1ř\xed","ř\xedjna","listopadu","prosince"]},defaultFormattingWidth:"wide"}),day:(0,d.default)({values:{narrow:["ne","po","\xfat","st","čt","p\xe1","so"],short:["ne","po","\xfat","st","čt","p\xe1","so"],abbreviated:["ned","pon","\xfate","stř","čtv","p\xe1t","sob"],wide:["neděle","ponděl\xed","\xfater\xfd","středa","čtvrtek","p\xe1tek","sobota"]},defaultWidth:"wide"}),dayPeriod:(0,d.default)({values:{narrow:{am:"dop.",pm:"odp.",midnight:"půlnoc",noon:"poledne",morning:"r\xe1no",afternoon:"odpoledne",evening:"večer",night:"noc"},abbreviated:{am:"dop.",pm:"odp.",midnight:"půlnoc",noon:"poledne",morning:"r\xe1no",afternoon:"odpoledne",evening:"večer",night:"noc"},wide:{am:"dopoledne",pm:"odpoledne",midnight:"půlnoc",noon:"poledne",morning:"r\xe1no",afternoon:"odpoledne",evening:"večer",night:"noc"}},defaultWidth:"wide",formattingValues:{narrow:{am:"dop.",pm:"odp.",midnight:"půlnoc",noon:"poledne",morning:"r\xe1no",afternoon:"odpoledne",evening:"večer",night:"noc"},abbreviated:{am:"dop.",pm:"odp.",midnight:"půlnoc",noon:"poledne",morning:"r\xe1no",afternoon:"odpoledne",evening:"večer",night:"noc"},wide:{am:"dopoledne",pm:"odpoledne",midnight:"půlnoc",noon:"poledne",morning:"r\xe1no",afternoon:"odpoledne",evening:"večer",night:"noc"}},defaultFormattingWidth:"wide"})},e.exports=n.default},15755:function(e){e.exports=function(e){return e&&e.__esModule?e:{default:e}},e.exports.__esModule=!0,e.exports.default=e.exports}}]);