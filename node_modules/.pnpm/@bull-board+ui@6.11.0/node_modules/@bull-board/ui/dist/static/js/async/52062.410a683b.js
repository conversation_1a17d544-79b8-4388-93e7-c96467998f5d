"use strict";(self.webpackChunk_bull_board_ui=self.webpackChunk_bull_board_ui||[]).push([["52062"],{79130:function(e,n){Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var o={lessThanXSeconds:{one:"minder as 1 sekonde",other:"minder as {{count}} sekonden"},xSeconds:{one:"1 sekonde",other:"{{count}} sekonden"},halfAMinute:"oardel min\xfat",lessThanXMinutes:{one:"minder as 1 min\xfat",other:"minder as {{count}} minuten"},xMinutes:{one:"1 min\xfat",other:"{{count}} minuten"},aboutXHours:{one:"sawat 1 oere",other:"sawat {{count}} oere"},xHours:{one:"1 oere",other:"{{count}} oere"},xDays:{one:"1 dei",other:"{{count}} dagen"},aboutXWeeks:{one:"sawat 1 wike",other:"sawat {{count}} wiken"},xWeeks:{one:"1 wike",other:"{{count}} wiken"},aboutXMonths:{one:"sawat 1 moanne",other:"sawat {{count}} moannen"},xMonths:{one:"1 moanne",other:"{{count}} moannen"},aboutXYears:{one:"sawat 1 jier",other:"sawat {{count}} jier"},xYears:{one:"1 jier",other:"{{count}} jier"},overXYears:{one:"mear as 1 jier",other:"mear as {{count}}s jier"},almostXYears:{one:"hast 1 jier",other:"hast {{count}} jier"}};n.default=function(e,n,t){var r,a=o[e];if(r="string"==typeof a?a:1===n?a.one:a.other.replace("{{count}}",String(n)),null!=t&&t.addSuffix)if(t.comparison&&t.comparison>0)return"oer "+r;else return r+" lyn";return r},e.exports=n.default}}]);