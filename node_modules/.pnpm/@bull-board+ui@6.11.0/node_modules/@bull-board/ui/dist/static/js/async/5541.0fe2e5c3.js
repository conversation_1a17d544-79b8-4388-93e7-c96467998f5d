"use strict";(self.webpackChunk_bull_board_ui=self.webpackChunk_bull_board_ui||[]).push([["5541"],{17293:function(t,e){function n(t,e,n){return(1===e&&t.one?t.one:e>=2&&e<=4&&t.twoFour?t.twoFour:t.other)[n].replace("{{count}}",String(e))}function u(t){var e="";return"almost"===t&&(e="takmer"),"about"===t&&(e="približne"),e.length>0?e+" ":""}function o(t){var e="";return"lessThan"===t&&(e="menej než"),"over"===t&&(e="viac než"),e.length>0?e+" ":""}Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var r={xSeconds:{one:{present:"sekunda",past:"sekundou",future:"sekundu"},twoFour:{present:"{{count}} sekundy",past:"{{count}} sekundami",future:"{{count}} sekundy"},other:{present:"{{count}} sek\xfand",past:"{{count}} sekundami",future:"{{count}} sek\xfand"}},halfAMinute:{other:{present:"pol min\xfaty",past:"pol min\xfatou",future:"pol min\xfaty"}},xMinutes:{one:{present:"min\xfata",past:"min\xfatou",future:"min\xfatu"},twoFour:{present:"{{count}} min\xfaty",past:"{{count}} min\xfatami",future:"{{count}} min\xfaty"},other:{present:"{{count}} min\xfat",past:"{{count}} min\xfatami",future:"{{count}} min\xfat"}},xHours:{one:{present:"hodina",past:"hodinou",future:"hodinu"},twoFour:{present:"{{count}} hodiny",past:"{{count}} hodinami",future:"{{count}} hodiny"},other:{present:"{{count}} hod\xedn",past:"{{count}} hodinami",future:"{{count}} hod\xedn"}},xDays:{one:{present:"deň",past:"dňom",future:"deň"},twoFour:{present:"{{count}} dni",past:"{{count}} dňami",future:"{{count}} dni"},other:{present:"{{count}} dn\xed",past:"{{count}} dňami",future:"{{count}} dn\xed"}},xWeeks:{one:{present:"t\xfdždeň",past:"t\xfdždňom",future:"t\xfdždeň"},twoFour:{present:"{{count}} t\xfdždne",past:"{{count}} t\xfdždňami",future:"{{count}} t\xfdždne"},other:{present:"{{count}} t\xfdždňov",past:"{{count}} t\xfdždňami",future:"{{count}} t\xfdždňov"}},xMonths:{one:{present:"mesiac",past:"mesiacom",future:"mesiac"},twoFour:{present:"{{count}} mesiace",past:"{{count}} mesiacmi",future:"{{count}} mesiace"},other:{present:"{{count}} mesiacov",past:"{{count}} mesiacmi",future:"{{count}} mesiacov"}},xYears:{one:{present:"rok",past:"rokom",future:"rok"},twoFour:{present:"{{count}} roky",past:"{{count}} rokmi",future:"{{count}} roky"},other:{present:"{{count}} rokov",past:"{{count}} rokmi",future:"{{count}} rokov"}}};e.default=function(t,e,s){var a,c=["lessThan","about","over","almost"].filter(function(e){return!!t.match(RegExp("^"+e))})[0]||"",i=r[(a=t.substring(c.length)).charAt(0).toLowerCase()+a.slice(1)];return null!=s&&s.addSuffix?s.comparison&&s.comparison>0?u(c)+"o "+o(c)+n(i,e,"future"):u(c)+"pred "+o(c)+n(i,e,"past"):u(c)+o(c)+n(i,e,"present")},t.exports=e.default}}]);