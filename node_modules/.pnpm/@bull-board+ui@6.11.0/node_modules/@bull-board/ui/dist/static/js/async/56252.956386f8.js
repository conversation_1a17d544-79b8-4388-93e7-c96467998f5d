"use strict";(self.webpackChunk_bull_board_ui=self.webpackChunk_bull_board_ui||[]).push([["56252"],{4725:function(t,e,n){n.d(e,{Z:()=>r});let r=n(96081).Z},18667:function(t,e,n){n.d(e,{j:()=>o});var r={};function o(){return r}},1645:function(t,e,n){n.d(e,{Z:()=>r});function r(t){var e=new Date(Date.UTC(t.getFullYear(),t.getMonth(),t.getDate(),t.getHours(),t.getMinutes(),t.getSeconds(),t.getMilliseconds()));return e.setUTCFullYear(t.getFullYear()),t.getTime()-e.getTime()}},19785:function(t,e,n){n.d(e,{Z:()=>r});function r(t,e){if(e.length<t)throw TypeError(t+" argument"+(t>1?"s":"")+" required, but only "+e.length+" present")}},86544:function(t,e,n){n.d(e,{Z:()=>a});var r=n(66700),o=n(19785);function a(t,e){return(0,o.Z)(2,arguments),(0,r.Z)(t).getTime()-(0,r.Z)(e).getTime()}},77184:function(t,e,n){n.d(e,{Z:()=>m});var r=n(18667),o=n(66700),a=n(19785);function u(t,e){(0,a.Z)(2,arguments);var n=(0,o.Z)(t),r=(0,o.Z)(e),u=n.getTime()-r.getTime();return u<0?-1:u>0?1:u}var i=n(86544),s={ceil:Math.ceil,round:Math.round,floor:Math.floor,trunc:function(t){return t<0?Math.ceil(t):Math.floor(t)}},f=n(4725);function l(t,e){if(null==t)throw TypeError("assign requires that input parameter not be null or undefined");for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t}var c=n(1645);function m(t,e,n){(0,a.Z)(2,arguments);var m,d,g,h,Z,b=(0,r.j)(),v=null!=(m=null!=(d=null==n?void 0:n.locale)?d:b.locale)?m:f.Z;if(!v.formatDistance)throw RangeError("locale must contain formatDistance property");var D=u(t,e);if(isNaN(D))throw RangeError("Invalid time value");var p=l(l({},n),{addSuffix:!!(null==n?void 0:n.addSuffix),comparison:D});D>0?(g=(0,o.Z)(e),h=(0,o.Z)(t)):(g=(0,o.Z)(t),h=(0,o.Z)(e));var M=function(t,e,n){(0,a.Z)(2,arguments);var r,o=(0,i.Z)(t,e)/1e3;return((r=null==n?void 0:n.roundingMethod)?s[r]:s.trunc)(o)}(h,g),y=Math.round((M-((0,c.Z)(h)-(0,c.Z)(g))/1e3)/60);if(y<2)if(null!=n&&n.includeSeconds)if(M<5)return v.formatDistance("lessThanXSeconds",5,p);else if(M<10)return v.formatDistance("lessThanXSeconds",10,p);else if(M<20)return v.formatDistance("lessThanXSeconds",20,p);else if(M<40)return v.formatDistance("halfAMinute",0,p);else if(M<60)return v.formatDistance("lessThanXMinutes",1,p);else return v.formatDistance("xMinutes",1,p);else if(0===y)return v.formatDistance("lessThanXMinutes",1,p);else return v.formatDistance("xMinutes",y,p);if(y<45)return v.formatDistance("xMinutes",y,p);if(y<90)return v.formatDistance("aboutXHours",1,p);if(y<1440){var T=Math.round(y/60);return v.formatDistance("aboutXHours",T,p)}if(y<2520)return v.formatDistance("xDays",1,p);else if(y<43200){var S=Math.round(y/1440);return v.formatDistance("xDays",S,p)}else if(y<86400)return Z=Math.round(y/43200),v.formatDistance("aboutXMonths",Z,p);if((Z=function(t,e){(0,a.Z)(2,arguments);var n,r=(0,o.Z)(t),i=(0,o.Z)(e),s=u(r,i),f=Math.abs(function(t,e){(0,a.Z)(2,arguments);var n=(0,o.Z)(t),r=(0,o.Z)(e);return 12*(n.getFullYear()-r.getFullYear())+(n.getMonth()-r.getMonth())}(r,i));if(f<1)n=0;else{1===r.getMonth()&&r.getDate()>27&&r.setDate(30),r.setMonth(r.getMonth()-s*f);var l=u(r,i)===-s;(function(t){(0,a.Z)(1,arguments);var e=(0,o.Z)(t);return(function(t){(0,a.Z)(1,arguments);var e=(0,o.Z)(t);return e.setHours(23,59,59,999),e})(e).getTime()===(function(t){(0,a.Z)(1,arguments);var e=(0,o.Z)(t),n=e.getMonth();return e.setFullYear(e.getFullYear(),n+1,0),e.setHours(23,59,59,999),e})(e).getTime()})((0,o.Z)(t))&&1===f&&1===u(t,i)&&(l=!1),n=s*(f-Number(l))}return 0===n?0:n}(h,g))<12){var w=Math.round(y/43200);return v.formatDistance("xMonths",w,p)}var X=Z%12,Y=Math.floor(Z/12);return X<3?v.formatDistance("aboutXYears",Y,p):X<9?v.formatDistance("overXYears",Y,p):v.formatDistance("almostXYears",Y+1,p)}},66700:function(t,e,n){n.d(e,{Z:()=>a});var r=n(95300),o=n(19785);function a(t){(0,o.Z)(1,arguments);var e=Object.prototype.toString.call(t);return t instanceof Date||"object"===(0,r.Z)(t)&&"[object Date]"===e?new Date(t.getTime()):"number"==typeof t||"[object Number]"===e?new Date(t):(("string"==typeof t||"[object String]"===e)&&"undefined"!=typeof console&&(console.warn("Starting with v2.0.0-beta.1 date-fns doesn't accept strings as date arguments. Please use `parseISO` to parse strings. See: https://github.com/date-fns/date-fns/blob/master/docs/upgradeGuide.md#string-arguments"),console.warn(Error().stack)),new Date(NaN))}},95300:function(t,e,n){n.d(e,{Z:()=>r});function r(t){return(r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}}}]);