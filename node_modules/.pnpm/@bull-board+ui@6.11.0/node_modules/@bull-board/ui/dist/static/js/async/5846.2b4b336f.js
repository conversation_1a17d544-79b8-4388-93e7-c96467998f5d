"use strict";(self.webpackChunk_bull_board_ui=self.webpackChunk_bull_board_ui||[]).push([["5846"],{6675:function(n,o){Object.defineProperty(o,"__esModule",{value:!0}),o.default=void 0;var e={lessThanXSeconds:{one:"dưới 1 gi\xe2y",other:"dưới {{count}} gi\xe2y"},xSeconds:{one:"1 gi\xe2y",other:"{{count}} gi\xe2y"},halfAMinute:"nửa ph\xfat",lessThanXMinutes:{one:"dưới 1 ph\xfat",other:"dưới {{count}} ph\xfat"},xMinutes:{one:"1 ph\xfat",other:"{{count}} ph\xfat"},aboutXHours:{one:"khoảng 1 giờ",other:"khoảng {{count}} giờ"},xHours:{one:"1 giờ",other:"{{count}} giờ"},xDays:{one:"1 ng\xe0y",other:"{{count}} ng\xe0y"},aboutXWeeks:{one:"khoảng 1 tuần",other:"khoảng {{count}} tuần"},xWeeks:{one:"1 tuần",other:"{{count}} tuần"},aboutXMonths:{one:"khoảng 1 th\xe1ng",other:"khoảng {{count}} th\xe1ng"},xMonths:{one:"1 th\xe1ng",other:"{{count}} th\xe1ng"},aboutXYears:{one:"khoảng 1 năm",other:"khoảng {{count}} năm"},xYears:{one:"1 năm",other:"{{count}} năm"},overXYears:{one:"hơn 1 năm",other:"hơn {{count}} năm"},almostXYears:{one:"gần 1 năm",other:"gần {{count}} năm"}};o.default=function(n,o,t){var u,h=e[n];if(u="string"==typeof h?h:1===o?h.one:h.other.replace("{{count}}",String(o)),null!=t&&t.addSuffix)if(t.comparison&&t.comparison>0)return u+" nữa";else return u+" trước";return u},n.exports=o.default}}]);