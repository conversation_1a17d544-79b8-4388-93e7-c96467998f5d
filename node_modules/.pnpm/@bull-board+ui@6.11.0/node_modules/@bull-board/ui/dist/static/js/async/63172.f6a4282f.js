(self.webpackChunk_bull_board_ui=self.webpackChunk_bull_board_ui||[]).push([["63172"],{22348:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){return function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},a=t.width?String(t.width):e.defaultWidth;return e.formats[a]||e.formats[e.defaultWidth]}},e.exports=t.default},84738:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){return function(t,a){var n;if("formatting"===(null!=a&&a.context?String(a.context):"standalone")&&e.formattingValues){var r=e.defaultFormattingWidth||e.defaultWidth,u=null!=a&&a.width?String(a.width):r;n=e.formattingValues[u]||e.formattingValues[r]}else{var i=e.defaultWidth,o=null!=a&&a.width?String(a.width):e.defaultWidth;n=e.values[o]||e.values[i]}return n[e.argumentCallback?e.argumentCallback(t):t]}},e.exports=t.default},89653:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){return function(t){var a,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=n.width,u=r&&e.matchPatterns[r]||e.matchPatterns[e.defaultMatchWidth],i=t.match(u);if(!i)return null;var o=i[0],d=r&&e.parsePatterns[r]||e.parsePatterns[e.defaultParseWidth],l=Array.isArray(d)?function(e,t){for(var a=0;a<e.length;a++)if(t(e[a]))return a}(d,function(e){return e.test(o)}):function(e,t){for(var a in e)if(e.hasOwnProperty(a)&&t(e[a]))return a}(d,function(e){return e.test(o)});return a=e.valueCallback?e.valueCallback(l):l,{value:a=n.valueCallback?n.valueCallback(a):a,rest:t.slice(o.length)}}},e.exports=t.default},71604:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){return function(t){var a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.match(e.matchPattern);if(!n)return null;var r=n[0],u=t.match(e.parsePattern);if(!u)return null;var i=e.valueCallback?e.valueCallback(u[0]):u[0];return{value:i=a.valueCallback?a.valueCallback(i):i,rest:t.slice(r.length)}}},e.exports=t.default},49287:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a={lessThanXSeconds:{one:"1秒未満",other:"{{count}}秒未満",oneWithSuffix:"約1秒",otherWithSuffix:"約{{count}}秒"},xSeconds:{one:"1秒",other:"{{count}}秒"},halfAMinute:"30秒",lessThanXMinutes:{one:"1分未満",other:"{{count}}分未満",oneWithSuffix:"約1分",otherWithSuffix:"約{{count}}分"},xMinutes:{one:"1分",other:"{{count}}分"},aboutXHours:{one:"約1時間",other:"約{{count}}時間"},xHours:{one:"1時間",other:"{{count}}時間"},xDays:{one:"1日",other:"{{count}}日"},aboutXWeeks:{one:"約1週間",other:"約{{count}}週間"},xWeeks:{one:"1週間",other:"{{count}}週間"},aboutXMonths:{one:"約1か月",other:"約{{count}}か月"},xMonths:{one:"1か月",other:"{{count}}か月"},aboutXYears:{one:"約1年",other:"約{{count}}年"},xYears:{one:"1年",other:"{{count}}年"},overXYears:{one:"1年以上",other:"{{count}}年以上"},almostXYears:{one:"1年近く",other:"{{count}}年近く"}};t.default=function(e,t,n){n=n||{};var r,u=a[e];if(r="string"==typeof u?u:1===t?n.addSuffix&&u.oneWithSuffix?u.oneWithSuffix:u.one:n.addSuffix&&u.otherWithSuffix?u.otherWithSuffix.replace("{{count}}",String(t)):u.other.replace("{{count}}",String(t)),n.addSuffix)if(n.comparison&&n.comparison>0)return r+"後";else return r+"前";return r},e.exports=t.default},96589:function(e,t,a){"use strict";var n=a(15755).default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=n(a(22348));t.default={date:(0,r.default)({formats:{full:"y年M月d日EEEE",long:"y年M月d日",medium:"y/MM/dd",short:"y/MM/dd"},defaultWidth:"full"}),time:(0,r.default)({formats:{full:"H時mm分ss秒 zzzz",long:"H:mm:ss z",medium:"H:mm:ss",short:"H:mm"},defaultWidth:"full"}),dateTime:(0,r.default)({formats:{full:"{{date}} {{time}}",long:"{{date}} {{time}}",medium:"{{date}} {{time}}",short:"{{date}} {{time}}"},defaultWidth:"full"})},e.exports=t.default},89754:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a={lastWeek:"先週のeeeeのp",yesterday:"昨日のp",today:"今日のp",tomorrow:"明日のp",nextWeek:"翌週のeeeeのp",other:"P"};t.default=function(e,t,n,r){return a[e]},e.exports=t.default},20077:function(e,t,a){"use strict";var n=a(15755).default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=n(a(84738));t.default={ordinalNumber:function(e,t){var a=Number(e);switch(String(null==t?void 0:t.unit)){case"year":return"".concat(a,"年");case"quarter":return"第".concat(a,"四半期");case"month":return"".concat(a,"月");case"week":return"第".concat(a,"週");case"date":return"".concat(a,"日");case"hour":return"".concat(a,"時");case"minute":return"".concat(a,"分");case"second":return"".concat(a,"秒");default:return"".concat(a)}},era:(0,r.default)({values:{narrow:["BC","AC"],abbreviated:["紀元前","西暦"],wide:["紀元前","西暦"]},defaultWidth:"wide"}),quarter:(0,r.default)({values:{narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["第1四半期","第2四半期","第3四半期","第4四半期"]},defaultWidth:"wide",argumentCallback:function(e){return Number(e)-1}}),month:(0,r.default)({values:{narrow:["1","2","3","4","5","6","7","8","9","10","11","12"],abbreviated:["1月","2月","3月","4月","5月","6月","7月","8月","9月","10月","11月","12月"],wide:["1月","2月","3月","4月","5月","6月","7月","8月","9月","10月","11月","12月"]},defaultWidth:"wide"}),day:(0,r.default)({values:{narrow:["日","月","火","水","木","金","土"],short:["日","月","火","水","木","金","土"],abbreviated:["日","月","火","水","木","金","土"],wide:["日曜日","月曜日","火曜日","水曜日","木曜日","金曜日","土曜日"]},defaultWidth:"wide"}),dayPeriod:(0,r.default)({values:{narrow:{am:"午前",pm:"午後",midnight:"深夜",noon:"正午",morning:"朝",afternoon:"午後",evening:"夜",night:"深夜"},abbreviated:{am:"午前",pm:"午後",midnight:"深夜",noon:"正午",morning:"朝",afternoon:"午後",evening:"夜",night:"深夜"},wide:{am:"午前",pm:"午後",midnight:"深夜",noon:"正午",morning:"朝",afternoon:"午後",evening:"夜",night:"深夜"}},defaultWidth:"wide",formattingValues:{narrow:{am:"午前",pm:"午後",midnight:"深夜",noon:"正午",morning:"朝",afternoon:"午後",evening:"夜",night:"深夜"},abbreviated:{am:"午前",pm:"午後",midnight:"深夜",noon:"正午",morning:"朝",afternoon:"午後",evening:"夜",night:"深夜"},wide:{am:"午前",pm:"午後",midnight:"深夜",noon:"正午",morning:"朝",afternoon:"午後",evening:"夜",night:"深夜"}},defaultFormattingWidth:"wide"})},e.exports=t.default},78776:function(e,t,a){"use strict";var n=a(15755).default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=n(a(71604)),u=n(a(89653));t.default={ordinalNumber:(0,r.default)({matchPattern:/^第?\d+(年|四半期|月|週|日|時|分|秒)?/i,parsePattern:/\d+/i,valueCallback:function(e){return parseInt(e,10)}}),era:(0,u.default)({matchPatterns:{narrow:/^(B\.?C\.?|A\.?D\.?)/i,abbreviated:/^(紀元[前後]|西暦)/i,wide:/^(紀元[前後]|西暦)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^B/i,/^A/i],any:[/^(紀元前)/i,/^(西暦|紀元後)/i]},defaultParseWidth:"any"}),quarter:(0,u.default)({matchPatterns:{narrow:/^[1234]/i,abbreviated:/^Q[1234]/i,wide:/^第[1234一二三四１２３４]四半期/i},defaultMatchWidth:"wide",parsePatterns:{any:[/(1|一|１)/i,/(2|二|２)/i,/(3|三|３)/i,/(4|四|４)/i]},defaultParseWidth:"any",valueCallback:function(e){return e+1}}),month:(0,u.default)({matchPatterns:{narrow:/^([123456789]|1[012])/,abbreviated:/^([123456789]|1[012])月/i,wide:/^([123456789]|1[012])月/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^1\D/,/^2/,/^3/,/^4/,/^5/,/^6/,/^7/,/^8/,/^9/,/^10/,/^11/,/^12/]},defaultParseWidth:"any"}),day:(0,u.default)({matchPatterns:{narrow:/^[日月火水木金土]/,short:/^[日月火水木金土]/,abbreviated:/^[日月火水木金土]/,wide:/^[日月火水木金土]曜日/},defaultMatchWidth:"wide",parsePatterns:{any:[/^日/,/^月/,/^火/,/^水/,/^木/,/^金/,/^土/]},defaultParseWidth:"any"}),dayPeriod:(0,u.default)({matchPatterns:{any:/^(AM|PM|午前|午後|正午|深夜|真夜中|夜|朝)/i},defaultMatchWidth:"any",parsePatterns:{any:{am:/^(A|午前)/i,pm:/^(P|午後)/i,midnight:/^深夜|真夜中/i,noon:/^正午/i,morning:/^朝/i,afternoon:/^午後/i,evening:/^夜/i,night:/^深夜/i}},defaultParseWidth:"any"})},e.exports=t.default},73079:function(e,t,a){"use strict";var n=a(15755).default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=n(a(49287)),u=n(a(96589)),i=n(a(89754)),o=n(a(20077)),d=n(a(78776));t.default={code:"ja",formatDistance:r.default,formatLong:u.default,formatRelative:i.default,localize:o.default,match:d.default,options:{weekStartsOn:0,firstWeekContainsDate:1}},e.exports=t.default},15755:function(e){e.exports=function(e){return e&&e.__esModule?e:{default:e}},e.exports.__esModule=!0,e.exports.default=e.exports}}]);