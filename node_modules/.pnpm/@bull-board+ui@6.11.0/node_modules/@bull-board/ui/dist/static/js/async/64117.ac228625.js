"use strict";(self.webpackChunk_bull_board_ui=self.webpackChunk_bull_board_ui||[]).push([["64117"],{96274:function(e,t,a){a.d(t,{n:()=>l});let l={latest:"latest",active:"active",waiting:"waiting",waitingChildren:"waiting-children",prioritized:"prioritized",completed:"completed",failed:"failed",delayed:"delayed",paused:"paused"}},73250:function(e,t,a){a.d(t,{g:()=>r});var l=a(52322),n=a(67239);a(2784);let r=e=>{let{label:t,id:a,inline:r,children:i}=e;return(0,l.jsxs)("div",{className:(0,n.Z)("field-JbAWJt",{"inline-vXG0Za":r}),children:[!!t&&!r&&(0,l.jsx)("label",{htmlFor:a,children:t}),i,!!t&&r&&(0,l.jsx)("label",{htmlFor:a,children:t})]})}},73283:function(e,t,a){a.d(t,{U:()=>r});var l=a(52322);a(2784);var n=a(73250);let r=e=>{let{label:t,id:a,...r}=e;return(0,l.jsx)(n.g,{label:t,id:a,children:(0,l.jsx)("input",{id:a,type:"text",...r})})}},84115:function(e,t,a){a.d(t,{m:()=>r});var l=a(52322);a(2784);var n=a(73250);let r=e=>{let{label:t,id:a,options:r,...i}=e;return(0,l.jsx)(n.g,{label:t,id:a,children:(0,l.jsx)("select",{id:a,...i,children:r.map(e=>(0,l.jsx)("option",{value:e.value,children:e.text},e.value))})})}},74672:function(e,t,a){a.d(t,{u:()=>c});var l=a(52322),n=a(78364),r=a(67239);a(2784);var i=a(59382),s=a(83576),o=a(67755);let c=e=>{let{open:t,title:a,onClose:c,children:d,width:u,actionButton:h}=e,{t:p}=(0,i.$G)();return(0,l.jsx)(n.fC,{open:t,modal:!0,onOpenChange:e=>{e||c()},children:(0,l.jsxs)(n.h_,{children:[(0,l.jsx)(n.aV,{className:o.Z.overlay}),(0,l.jsx)(n.VY,{className:o.Z.contentWrapper,children:(0,l.jsxs)("div",{className:(0,r.Z)(o.Z.content,o.Z[u||""]),children:[!!a&&(0,l.jsx)(n.Dx,{children:a}),(0,l.jsx)(n.dk,{asChild:!0,children:(0,l.jsx)("div",{className:o.Z.description,children:d})}),(0,l.jsxs)("div",{className:o.Z.actions,children:[h,(0,l.jsx)(n.x8,{asChild:!0,children:(0,l.jsx)(s.z,{theme:"basic",children:p("MODAL.CLOSE_BTN")})})]})]})})]})})}},14346:function(e,t,a){a.r(t),a.d(t,{SettingsModal:()=>A});var l=a(52322),n=a(2784),r=a(59382),i=a(73222),s=a(6294),o=a(4130),c=a(73283),d=a(84115),u=a(56137),h=a(59656),p=a(8210),b=a(69153),S=a(65534),v=a(3436),f="Switch",[x,T]=(0,p.b)(f),[j,g]=x(f),m=n.forwardRef((e,t)=>{let{__scopeSwitch:a,name:r,checked:i,defaultChecked:s,required:o,disabled:c,value:d="on",onCheckedChange:p,form:S,...x}=e,[T,g]=n.useState(null),m=(0,h.e)(t,e=>g(e)),E=n.useRef(!1),N=!T||S||!!T.closest("form"),[I,k]=(0,b.T)({prop:i,defaultProp:s??!1,onChange:p,caller:f});return(0,l.jsxs)(j,{scope:a,checked:I,disabled:c,children:[(0,l.jsx)(v.WV.button,{type:"button",role:"switch","aria-checked":I,"aria-required":o,"data-state":O(I),"data-disabled":c?"":void 0,disabled:c,value:d,...x,ref:m,onClick:(0,u.M)(e.onClick,e=>{k(e=>!e),N&&(E.current=e.isPropagationStopped(),E.current||e.stopPropagation())})}),N&&(0,l.jsx)(C,{control:T,bubbles:!E.current,name:r,value:d,checked:I,required:o,disabled:c,form:S,style:{transform:"translateX(-100%)"}})]})});m.displayName=f;var E="SwitchThumb",N=n.forwardRef((e,t)=>{let{__scopeSwitch:a,...n}=e,r=g(E,a);return(0,l.jsx)(v.WV.span,{"data-state":O(r.checked),"data-disabled":r.disabled?"":void 0,...n,ref:t})});N.displayName=E;var C=n.forwardRef(({__scopeSwitch:e,control:t,checked:a,bubbles:r=!0,...i},s)=>{let o=n.useRef(null),c=(0,h.e)(o,s),d=function(e){let t=n.useRef({value:e,previous:e});return n.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}(a),u=(0,S.t)(t);return n.useEffect(()=>{let e=o.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(d!==a&&t){let l=new Event("click",{bubbles:r});t.call(e,a),e.dispatchEvent(l)}},[d,a,r]),(0,l.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:a,...i,tabIndex:-1,ref:c,style:{...i.style,...u,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function O(e){return e?"checked":"unchecked"}C.displayName="SwitchBubbleInput";var I=a(73250);let k=e=>{let{label:t,id:a,...n}=e;return(0,l.jsx)(I.g,{label:t,id:a,inline:!0,children:(0,l.jsx)(m,{id:a,...n,className:"switch-DOdNhn",children:(0,l.jsx)(N,{className:"thumb-tvm2sT"})})})};var _=a(74672),L=a(1542);let G=[-1,3,5,10,20,60,300,900],A=e=>{let{open:t,onClose:a}=e,{language:n,pollingInterval:u,jobsPerPage:h,confirmQueueActions:p,confirmJobActions:b,collapseJob:S,collapseJobData:v,collapseJobOptions:f,collapseJobError:x,defaultJobTab:T,darkMode:j,setSettings:g}=(0,s.F)(e=>e),{pollingInterval:m}=(0,o.p)(),{t:E,i18n:N}=(0,r.$G)();return(0,l.jsxs)(_.u,{width:"small",open:t,onClose:a,title:E("SETTINGS.TITLE"),children:[(0,l.jsx)(d.m,{label:E("SETTINGS.LANGUAGE"),id:"language",options:i.M.map(e=>({text:e,value:e})),value:n,onChange:e=>{N.changeLanguage(e.target.value),g({language:e.target.value})}}),(null==m?void 0:m.showSetting)!==!1&&(0,l.jsx)(d.m,{label:E("SETTINGS.POLLING_INTERVAL"),id:"polling-interval",options:G.map(e=>({text:e<0?E("SETTINGS.POLLING_OPTIONS.OFF"):0===Math.floor(e/60)?E("SETTINGS.POLLING_OPTIONS.SECS",{count:e}):E("SETTINGS.POLLING_OPTIONS.MINS",{count:e/60}),value:`${e}`})),value:`${u}`,onChange:e=>g({pollingInterval:+e.target.value})}),(0,l.jsx)(d.m,{label:E("SETTINGS.DEFAULT_JOB_TAB"),id:"default-job-tab",options:["default"].concat(L.X).map(e=>({text:E(`JOB.TABS.${e.toUpperCase()}`),value:e})),value:T,onChange:e=>g({defaultJobTab:e.target.value})}),(0,l.jsx)(c.U,{label:E("SETTINGS.JOBS_PER_PAGE"),id:"jobs-per-page",value:h,type:"number",min:"1",max:300,maxLength:3,onChange:e=>{g({jobsPerPage:Math.min(+e.target.value,300)})}}),(0,l.jsx)(k,{label:E("SETTINGS.CONFIRM_QUEUE_ACTIONS"),id:"confirm-queue-actions",checked:p,onCheckedChange:e=>g({confirmQueueActions:e})}),(0,l.jsx)(k,{label:E("SETTINGS.CONFIRM_JOB_ACTIONS"),id:"confirm-job-actions",checked:b,onCheckedChange:e=>g({confirmJobActions:e})}),(0,l.jsx)(k,{label:E("SETTINGS.COLLAPSE_JOB"),id:"collapse-job",checked:S,onCheckedChange:e=>g({collapseJob:e})}),(0,l.jsx)(k,{label:E("SETTINGS.COLLAPSE_JOB_DATA"),id:"collapse-job-data",checked:v,onCheckedChange:e=>g({collapseJobData:e})}),(0,l.jsx)(k,{label:E("SETTINGS.COLLAPSE_JOB_OPTIONS"),id:"collapse-job-options",checked:f,onCheckedChange:e=>g({collapseJobOptions:e})}),(0,l.jsx)(k,{label:E("SETTINGS.COLLAPSE_JOB_ERROR"),id:"collapse-job-error",checked:x,onCheckedChange:e=>g({collapseJobError:e})}),(0,l.jsx)(k,{label:E("SETTINGS.DARK_MODE"),id:"dark-mode",checked:j,onCheckedChange:e=>g({darkMode:e})})]})}},1542:function(e,t,a){a.d(t,{X:()=>i,r:()=>s});var l=a(96274),n=a(2784),r=a(6294);let i=["Data","Options","Logs","Error"];function s(e){let[t,a]=(0,n.useState)([]),{defaultJobTab:s}=(0,r.F)(),[o,c]=(0,n.useState)(t.find(e=>e===s)||t[0]);return(0,n.useEffect)(()=>{let t=i.filter(e=>"Error"!==e);a(t=e===l.n.failed?["Error",...t]:[...t,"Error"])},[e]),(0,n.useEffect)(()=>{c(t.includes(s)?s:t[0])},[s,t]),{tabs:null==t?void 0:t.map(e=>({title:e,isActive:e===o,selectTab:()=>c(e)})),selectedTab:o}}}}]);