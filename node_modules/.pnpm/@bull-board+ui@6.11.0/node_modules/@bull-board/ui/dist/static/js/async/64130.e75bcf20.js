"use strict";(self.webpackChunk_bull_board_ui=self.webpackChunk_bull_board_ui||[]).push([["64130"],{94120:function(u,e){function n(u){return u.replace(/sekuntia?/,"sekunnin")}function t(u){return u.replace(/minuuttia?/,"minuutin")}function o(u){return u.replace(/tuntia?/,"tunnin")}function i(u){return u.replace(/(viikko|viikkoa)/,"viikon")}function r(u){return u.replace(/(kuukausi|kuukautta)/,"kuukauden")}function a(u){return u.replace(/(vuosi|vuotta)/,"vuoden")}Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var s={lessThanXSeconds:{one:"alle sekunti",other:"alle {{count}} sekuntia",futureTense:n},xSeconds:{one:"sekunti",other:"{{count}} sekuntia",futureTense:n},halfAMinute:{one:"puoli minuuttia",other:"puoli minuuttia",futureTense:function(u){return"puolen minuutin"}},lessThanXMinutes:{one:"alle minuutti",other:"alle {{count}} minuuttia",futureTense:t},xMinutes:{one:"minuutti",other:"{{count}} minuuttia",futureTense:t},aboutXHours:{one:"noin tunti",other:"noin {{count}} tuntia",futureTense:o},xHours:{one:"tunti",other:"{{count}} tuntia",futureTense:o},xDays:{one:"p\xe4iv\xe4",other:"{{count}} p\xe4iv\xe4\xe4",futureTense:function(u){return u.replace(/päivää?/,"p\xe4iv\xe4n")}},aboutXWeeks:{one:"noin viikko",other:"noin {{count}} viikkoa",futureTense:i},xWeeks:{one:"viikko",other:"{{count}} viikkoa",futureTense:i},aboutXMonths:{one:"noin kuukausi",other:"noin {{count}} kuukautta",futureTense:r},xMonths:{one:"kuukausi",other:"{{count}} kuukautta",futureTense:r},aboutXYears:{one:"noin vuosi",other:"noin {{count}} vuotta",futureTense:a},xYears:{one:"vuosi",other:"{{count}} vuotta",futureTense:a},overXYears:{one:"yli vuosi",other:"yli {{count}} vuotta",futureTense:a},almostXYears:{one:"l\xe4hes vuosi",other:"l\xe4hes {{count}} vuotta",futureTense:a}};e.default=function(u,e,n){var t=s[u],o=1===e?t.one:t.other.replace("{{count}}",String(e));if(null!=n&&n.addSuffix)if(n.comparison&&n.comparison>0)return t.futureTense(o)+" kuluttua";else return o+" sitten";return o},u.exports=e.default}}]);