(self.webpackChunk_bull_board_ui=self.webpackChunk_bull_board_ui||[]).push([["66856"],{84738:function(e,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.default=function(e){return function(n,t){var a;if("formatting"===(null!=t&&t.context?String(t.context):"standalone")&&e.formattingValues){var r=e.defaultFormattingWidth||e.defaultWidth,i=null!=t&&t.width?String(t.width):r;a=e.formattingValues[i]||e.formattingValues[r]}else{var d=e.defaultWidth,o=null!=t&&t.width?String(t.width):e.defaultWidth;a=e.values[o]||e.values[d]}return a[e.argumentCallback?e.argumentCallback(n):n]}},e.exports=n.default},17454:function(e,n,t){"use strict";var a=t(15755).default;Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var r=a(t(84738));n.default={ordinalNumber:function(e,n){var t=Number(e);return(null==n?void 0:n.unit)==="hour"?String(t):1===t?t+"-r\xeb":4===t?t+"t":t+"-t\xeb"},era:(0,r.default)({values:{narrow:["P","M"],abbreviated:["PK","MK"],wide:["Para Krishtit","Mbas Krishtit"]},defaultWidth:"wide"}),quarter:(0,r.default)({values:{narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["4-mujori I","4-mujori II","4-mujori III","4-mujori IV"]},defaultWidth:"wide",argumentCallback:function(e){return e-1}}),month:(0,r.default)({values:{narrow:["J","S","M","P","M","Q","K","G","S","T","N","D"],abbreviated:["Jan","Shk","Mar","Pri","Maj","Qer","Kor","Gus","Sht","Tet","N\xebn","Dhj"],wide:["Janar","Shkurt","Mars","Prill","Maj","Qershor","Korrik","Gusht","Shtator","Tetor","N\xebntor","Dhjetor"]},defaultWidth:"wide"}),day:(0,r.default)({values:{narrow:["D","H","M","M","E","P","S"],short:["Di","H\xeb","Ma","M\xeb","En","Pr","Sh"],abbreviated:["Die","H\xebn","Mar","M\xebr","Enj","Pre","Sht"],wide:["Diel\xeb","H\xebn\xeb","Mart\xeb","M\xebrkur\xeb","Enjte","Premte","Shtun\xeb"]},defaultWidth:"wide"}),dayPeriod:(0,r.default)({values:{narrow:{am:"p",pm:"m",midnight:"m",noon:"d",morning:"m\xebngjes",afternoon:"dite",evening:"mbr\xebmje",night:"nat\xeb"},abbreviated:{am:"PD",pm:"MD",midnight:"mesn\xebt\xeb",noon:"drek",morning:"m\xebngjes",afternoon:"mbasdite",evening:"mbr\xebmje",night:"nat\xeb"},wide:{am:"p.d.",pm:"m.d.",midnight:"mesn\xebt\xeb",noon:"drek",morning:"m\xebngjes",afternoon:"mbasdite",evening:"mbr\xebmje",night:"nat\xeb"}},defaultWidth:"wide",formattingValues:{narrow:{am:"p",pm:"m",midnight:"m",noon:"d",morning:"n\xeb m\xebngjes",afternoon:"n\xeb mbasdite",evening:"n\xeb mbr\xebmje",night:"n\xeb mesnat\xeb"},abbreviated:{am:"PD",pm:"MD",midnight:"mesnat\xeb",noon:"drek",morning:"n\xeb m\xebngjes",afternoon:"n\xeb mbasdite",evening:"n\xeb mbr\xebmje",night:"n\xeb mesnat\xeb"},wide:{am:"p.d.",pm:"m.d.",midnight:"mesnat\xeb",noon:"drek",morning:"n\xeb m\xebngjes",afternoon:"n\xeb mbasdite",evening:"n\xeb mbr\xebmje",night:"n\xeb mesnat\xeb"}},defaultFormattingWidth:"wide"})},e.exports=n.default},15755:function(e){e.exports=function(e){return e&&e.__esModule?e:{default:e}},e.exports.__esModule=!0,e.exports.default=e.exports}}]);