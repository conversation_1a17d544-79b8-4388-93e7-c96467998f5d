(self.webpackChunk_bull_board_ui=self.webpackChunk_bull_board_ui||[]).push([["72710"],{1906:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getDefaultOptions=function(){return r},t.setDefaultOptions=function(e){r=e};var r={}},85345:function(e,t,r){"use strict";var n=r(15755).default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t,r){(0,u.default)(2,arguments);var n=(0,o.default)(e,r),a=(0,o.default)(t,r);return n.getTime()===a.getTime()};var u=n(r(26193)),o=n(r(23658));e.exports=t.default},26193:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t){if(t.length<e)throw TypeError(e+" argument"+(e>1?"s":"")+" required, but only "+t.length+" present")},e.exports=t.default},23658:function(e,t,r){"use strict";var n=r(15755).default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t){(0,o.default)(1,arguments);var r,n,s,f,i,d,c,p,b=(0,l.getDefaultOptions)(),y=(0,a.default)(null!=(r=null!=(n=null!=(s=null!=(f=null==t?void 0:t.weekStartsOn)?f:null==t||null==(i=t.locale)||null==(d=i.options)?void 0:d.weekStartsOn)?s:b.weekStartsOn)?n:null==(c=b.locale)||null==(p=c.options)?void 0:p.weekStartsOn)?r:0);if(!(y>=0&&y<=6))throw RangeError("weekStartsOn must be between 0 and 6 inclusively");var v=(0,u.default)(e),_=v.getUTCDay();return v.setUTCDate(v.getUTCDate()-(7*(_<y)+_-y)),v.setUTCHours(0,0,0,0),v};var u=n(r(39276)),o=n(r(26193)),a=n(r(65862)),l=r(1906);e.exports=t.default},65862:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){if(null===e||!0===e||!1===e)return NaN;var t=Number(e);return isNaN(t)?t:t<0?Math.ceil(t):Math.floor(t)},e.exports=t.default},97981:function(e,t,r){"use strict";var n=r(15755).default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var u=n(r(85345)),o=["жексенбіде","дүйсенбіде","сейсенбіде","сәрсенбіде","бейсенбіде","жұмада","сенбіде"];function a(e){return"'"+o[e]+" сағат' p'-де'"}var l={lastWeek:function(e,t,r){var n=e.getUTCDay();return(0,u.default)(e,t,r)?a(n):"'өткен "+o[n]+" сағат' p'-де'"},yesterday:"'кеше сағат' p'-де'",today:"'бүгін сағат' p'-де'",tomorrow:"'ертең сағат' p'-де'",nextWeek:function(e,t,r){var n=e.getUTCDay();return(0,u.default)(e,t,r)?a(n):"'келесі "+o[n]+" сағат' p'-де'"},other:"P"};t.default=function(e,t,r,n){var u=l[e];return"function"==typeof u?u(t,r,n):u},e.exports=t.default},39276:function(e,t,r){"use strict";var n=r(15755).default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){(0,o.default)(1,arguments);var t=Object.prototype.toString.call(e);return e instanceof Date||"object"===(0,u.default)(e)&&"[object Date]"===t?new Date(e.getTime()):"number"==typeof e||"[object Number]"===t?new Date(e):(("string"==typeof e||"[object String]"===t)&&"undefined"!=typeof console&&(console.warn("Starting with v2.0.0-beta.1 date-fns doesn't accept strings as date arguments. Please use `parseISO` to parse strings. See: https://github.com/date-fns/date-fns/blob/master/docs/upgradeGuide.md#string-arguments"),console.warn(Error().stack)),new Date(NaN))};var u=n(r(69430)),o=n(r(26193));e.exports=t.default},15755:function(e){e.exports=function(e){return e&&e.__esModule?e:{default:e}},e.exports.__esModule=!0,e.exports.default=e.exports},69430:function(e){function t(r){return e.exports=t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e.exports.__esModule=!0,e.exports.default=e.exports,t(r)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports}}]);