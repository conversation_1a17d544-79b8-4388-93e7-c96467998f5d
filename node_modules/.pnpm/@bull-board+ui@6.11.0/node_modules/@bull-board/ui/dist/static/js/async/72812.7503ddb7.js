(self.webpackChunk_bull_board_ui=self.webpackChunk_bull_board_ui||[]).push([["72812"],{84738:function(a,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.default=function(a){return function(n,e){var t;if("formatting"===(null!=e&&e.context?String(e.context):"standalone")&&a.formattingValues){var r=a.defaultFormattingWidth||a.defaultWidth,i=null!=e&&e.width?String(e.width):r;t=a.formattingValues[i]||a.formattingValues[r]}else{var u=a.defaultWidth,d=null!=e&&e.width?String(e.width):a.defaultWidth;t=a.values[d]||a.values[u]}return t[a.argumentCallback?a.argumentCallback(n):n]}},a.exports=n.default},14434:function(a,n,e){"use strict";var t=e(15755).default;Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var r=t(e(84738)),i={1:"-inci",5:"-inci",8:"-inci",70:"-inci",80:"-inci",2:"-nci",7:"-nci",20:"-nci",50:"-nci",3:"-\xfcnc\xfc",4:"-\xfcnc\xfc",100:"-\xfcnc\xfc",6:"-ncı",9:"-uncu",10:"-uncu",30:"-uncu",60:"-ıncı",90:"-ıncı"},u=function(a){if(0===a)return a+"-ıncı";var n=a%10,e=a%100-n,t=a>=100?100:null;return i[n]?i[n]:i[e]?i[e]:null!==t?i[t]:""};n.default={ordinalNumber:function(a,n){var e=Number(a),t=u(e);return e+t},era:(0,r.default)({values:{narrow:["e.ə","b.e"],abbreviated:["e.ə","b.e"],wide:["eramızdan əvvəl","bizim era"]},defaultWidth:"wide"}),quarter:(0,r.default)({values:{narrow:["1","2","3","4"],abbreviated:["K1","K2","K3","K4"],wide:["1ci kvartal","2ci kvartal","3c\xfc kvartal","4c\xfc kvartal"]},defaultWidth:"wide",argumentCallback:function(a){return a-1}}),month:(0,r.default)({values:{narrow:["Y","F","M","A","M","İ","İ","A","S","O","N","D"],abbreviated:["Yan","Fev","Mar","Apr","May","İyun","İyul","Avq","Sen","Okt","Noy","Dek"],wide:["Yanvar","Fevral","Mart","Aprel","May","İyun","İyul","Avqust","Sentyabr","Oktyabr","Noyabr","Dekabr"]},defaultWidth:"wide"}),day:(0,r.default)({values:{narrow:["B.","B.e","\xc7.a","\xc7.","C.a","C.","Ş."],short:["B.","B.e","\xc7.a","\xc7.","C.a","C.","Ş."],abbreviated:["Baz","Baz.e","\xc7ər.a","\xc7ər","C\xfcm.a","C\xfcm","Şə"],wide:["Bazar","Bazar ertəsi","\xc7ərşənbə axşamı","\xc7ərşənbə","C\xfcmə axşamı","C\xfcmə","Şənbə"]},defaultWidth:"wide"}),dayPeriod:(0,r.default)({values:{narrow:{am:"am",pm:"pm",midnight:"gecəyarı",noon:"g\xfcn",morning:"səhər",afternoon:"g\xfcnd\xfcz",evening:"axşam",night:"gecə"},abbreviated:{am:"AM",pm:"PM",midnight:"gecəyarı",noon:"g\xfcn",morning:"səhər",afternoon:"g\xfcnd\xfcz",evening:"axşam",night:"gecə"},wide:{am:"a.m.",pm:"p.m.",midnight:"gecəyarı",noon:"g\xfcn",morning:"səhər",afternoon:"g\xfcnd\xfcz",evening:"axşam",night:"gecə"}},defaultWidth:"wide",formattingValues:{narrow:{am:"a",pm:"p",midnight:"gecəyarı",noon:"g\xfcn",morning:"səhər",afternoon:"g\xfcnd\xfcz",evening:"axşam",night:"gecə"},abbreviated:{am:"AM",pm:"PM",midnight:"gecəyarı",noon:"g\xfcn",morning:"səhər",afternoon:"g\xfcnd\xfcz",evening:"axşam",night:"gecə"},wide:{am:"a.m.",pm:"p.m.",midnight:"gecəyarı",noon:"g\xfcn",morning:"səhər",afternoon:"g\xfcnd\xfcz",evening:"axşam",night:"gecə"}},defaultFormattingWidth:"wide"})},a.exports=n.default},15755:function(a){a.exports=function(a){return a&&a.__esModule?a:{default:a}},a.exports.__esModule=!0,a.exports.default=a.exports}}]);