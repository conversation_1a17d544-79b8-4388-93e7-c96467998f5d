"use strict";(self.webpackChunk_bull_board_ui=self.webpackChunk_bull_board_ui||[]).push([["77527"],{1864:function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n={lessThanXSeconds:{one:"mindre enn eitt sekund",other:"mindre enn {{count}} sekund"},xSeconds:{one:"eitt sekund",other:"{{count}} sekund"},halfAMinute:"eit halvt minutt",lessThanXMinutes:{one:"mindre enn eitt minutt",other:"mindre enn {{count}} minutt"},xMinutes:{one:"eitt minutt",other:"{{count}} minutt"},aboutXHours:{one:"omtrent ein time",other:"omtrent {{count}} timar"},xHours:{one:"ein time",other:"{{count}} timar"},xDays:{one:"ein dag",other:"{{count}} dagar"},aboutXWeeks:{one:"omtrent ei veke",other:"omtrent {{count}} veker"},xWeeks:{one:"ei veke",other:"{{count}} veker"},aboutXMonths:{one:"omtrent ein m\xe5nad",other:"omtrent {{count}} m\xe5nader"},xMonths:{one:"ein m\xe5nad",other:"{{count}} m\xe5nader"},aboutXYears:{one:"omtrent eitt \xe5r",other:"omtrent {{count}} \xe5r"},xYears:{one:"eitt \xe5r",other:"{{count}} \xe5r"},overXYears:{one:"over eitt \xe5r",other:"over {{count}} \xe5r"},almostXYears:{one:"nesten eitt \xe5r",other:"nesten {{count}} \xe5r"}},o=["null","ein","to","tre","fire","fem","seks","sju","\xe5tte","ni","ti","elleve","tolv"];t.default=function(e,t,r){var u,i=n[e];if(u="string"==typeof i?i:1===t?i.one:r&&r.onlyNumeric?i.other.replace("{{count}}",String(t)):i.other.replace("{{count}}",t<13?o[t]:String(t)),null!=r&&r.addSuffix)if(r.comparison&&r.comparison>0)return"om "+u;else return u+" sidan";return u},e.exports=t.default}}]);