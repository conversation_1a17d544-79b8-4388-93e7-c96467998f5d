(self.webpackChunk_bull_board_ui=self.webpackChunk_bull_board_ui||[]).push([["77697"],{89653:function(a,i){"use strict";Object.defineProperty(i,"__esModule",{value:!0}),i.default=function(a){return function(i){var e,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.width,r=n&&a.matchPatterns[n]||a.matchPatterns[a.defaultMatchWidth],d=i.match(r);if(!d)return null;var l=d[0],u=n&&a.parsePatterns[n]||a.parsePatterns[a.defaultParseWidth],s=Array.isArray(u)?function(a,i){for(var e=0;e<a.length;e++)if(i(a[e]))return e}(u,function(a){return a.test(l)}):function(a,i){for(var e in a)if(a.hasOwnProperty(e)&&i(a[e]))return e}(u,function(a){return a.test(l)});return e=a.valueCallback?a.valueCallback(s):s,{value:e=t.valueCallback?t.valueCallback(e):e,rest:i.slice(l.length)}}},a.exports=i.default},71604:function(a,i){"use strict";Object.defineProperty(i,"__esModule",{value:!0}),i.default=function(a){return function(i){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},t=i.match(a.matchPattern);if(!t)return null;var n=t[0],r=i.match(a.parsePattern);if(!r)return null;var d=a.valueCallback?a.valueCallback(r[0]):r[0];return{value:d=e.valueCallback?e.valueCallback(d):d,rest:i.slice(n.length)}}},a.exports=i.default},953:function(a,i,e){"use strict";var t=e(15755).default;Object.defineProperty(i,"__esModule",{value:!0}),i.default=void 0;var n=t(e(89653));i.default={ordinalNumber:(0,t(e(71604)).default)({matchPattern:/^(\d+)(d|na|tr|mh)?/i,parsePattern:/\d+/i,valueCallback:function(a){return parseInt(a,10)}}),era:(0,n.default)({matchPatterns:{narrow:/^(r|a)/i,abbreviated:/^(r\.?\s?c\.?|r\.?\s?a\.?\s?c\.?|a\.?\s?d\.?|a\.?\s?c\.?)/i,wide:/^(ro Chrìosta|ron aois choitchinn|anno domini|aois choitcheann)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^b/i,/^(a|c)/i]},defaultParseWidth:"any"}),quarter:(0,n.default)({matchPatterns:{narrow:/^[1234]/i,abbreviated:/^c[1234]/i,wide:/^[1234](cd|na|tr|mh)? cairteal/i},defaultMatchWidth:"wide",parsePatterns:{any:[/1/i,/2/i,/3/i,/4/i]},defaultParseWidth:"any",valueCallback:function(a){return a+1}}),month:(0,n.default)({matchPatterns:{narrow:/^[fgmcòilsd]/i,abbreviated:/^(faoi|gear|màrt|gibl|cèit|ògmh|iuch|lùn|sult|dàmh|samh|dùbh)/i,wide:/^(am faoilleach|an gearran|am màrt|an giblean|an cèitean|an t-Ògmhios|an t-Iuchar|an lùnastal|an t-Sultain|an dàmhair|an t-Samhain|an dùbhlachd)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^f/i,/^g/i,/^m/i,/^g/i,/^c/i,/^ò/i,/^i/i,/^l/i,/^s/i,/^d/i,/^s/i,/^d/i],any:[/^fa/i,/^ge/i,/^mà/i,/^gi/i,/^c/i,/^ò/i,/^i/i,/^l/i,/^su/i,/^d/i,/^sa/i,/^d/i]},defaultParseWidth:"any"}),day:(0,n.default)({matchPatterns:{narrow:/^[dlmcahs]/i,short:/^(dò|lu|mà|ci|ar|ha|sa)/i,abbreviated:/^(did|dil|dim|dic|dia|dih|dis)/i,wide:/^(didòmhnaich|diluain|dimàirt|diciadain|diardaoin|dihaoine|disathairne)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^d/i,/^l/i,/^m/i,/^c/i,/^a/i,/^h/i,/^s/i],any:[/^d/i,/^l/i,/^m/i,/^c/i,/^a/i,/^h/i,/^s/i]},defaultParseWidth:"any"}),dayPeriod:(0,n.default)({matchPatterns:{narrow:/^(a|p|mi|n|(san|aig) (madainn|feasgar|feasgar|oidhche))/i,any:/^([ap]\.?\s?m\.?|meadhan oidhche|meadhan là|(san|aig) (madainn|feasgar|feasgar|oidhche))/i},defaultMatchWidth:"any",parsePatterns:{any:{am:/^m/i,pm:/^f/i,midnight:/^meadhan oidhche/i,noon:/^meadhan là/i,morning:/sa mhadainn/i,afternoon:/feasgar/i,evening:/feasgar/i,night:/air an oidhche/i}},defaultParseWidth:"any"})},a.exports=i.default},15755:function(a){a.exports=function(a){return a&&a.__esModule?a:{default:a}},a.exports.__esModule=!0,a.exports.default=a.exports}}]);