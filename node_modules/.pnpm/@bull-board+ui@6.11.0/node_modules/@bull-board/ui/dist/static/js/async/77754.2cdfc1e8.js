"use strict";(self.webpackChunk_bull_board_ui=self.webpackChunk_bull_board_ui||[]).push([["77754"],{74672:function(e,i,t){t.d(i,{u:()=>m});var l=t(52322),r=t(78364),n=t(67239);t(2784);var a=t(59382),s=t(83576),o=t(67755);let m=e=>{let{open:i,title:t,onClose:m,children:c,width:u,actionButton:d}=e,{t:b}=(0,a.$G)();return(0,l.jsx)(r.fC,{open:i,modal:!0,onOpenChange:e=>{e||m()},children:(0,l.jsxs)(r.h_,{children:[(0,l.jsx)(r.aV,{className:o.Z.overlay}),(0,l.jsx)(r.<PERSON><PERSON>,{className:o.<PERSON>.contentWrapper,children:(0,l.jsxs)("div",{className:(0,n.Z)(o.Z.content,o.Z[u||""]),children:[!!t&&(0,l.jsx)(r.Dx,{children:t}),(0,l.jsx)(r.dk,{asChild:!0,children:(0,l.jsx)("div",{className:o.Z.description,children:c})}),(0,l.jsxs)("div",{className:o.Z.actions,children:[d,(0,l.jsx)(r.x8,{asChild:!0,children:(0,l.jsx)(s.z,{theme:"basic",children:b("MODAL.CLOSE_BTN")})})]})]})})]})})}},17784:function(e,i,t){t.r(i),t.d(i,{RedisStatsModal:()=>f});var l=t(52322),r=t(77184);let n=["B","kB","MB","GB","TB","PB","EB","ZB","YB"],a=["B","KiB","MiB","GiB","TiB","PiB","EiB","ZiB","YiB"],s=["b","kbit","Mbit","Gbit","Tbit","Pbit","Ebit","Zbit","Ybit"],o=["b","kibit","Mibit","Gibit","Tibit","Pibit","Eibit","Zibit","Yibit"],m=(e,i,t)=>{let l=e;return"string"==typeof i||Array.isArray(i)?l=e.toLocaleString(i,t):(!0===i||void 0!==t)&&(l=e.toLocaleString(void 0,t)),l},c=e=>{if("number"==typeof e)return Math.log10(e);let i=e.toString(10);return i.length+Math.log10("0."+i.slice(0,15))},u=e=>"number"==typeof e?Math.log(e):c(e)*Math.log(10),d=(e,i)=>{if("number"==typeof e)return e/i;let t=e/BigInt(i),l=e%BigInt(i);return Number(t)+Number(l)/i};function b(e,i){let t;if("bigint"!=typeof e&&!Number.isFinite(e))throw TypeError(`Expected a finite number, got ${typeof e}: ${e}`);let l=(i={bits:!1,binary:!1,space:!0,...i}).bits?i.binary?o:s:i.binary?a:n,r=i.space?" ":"";if(i.signed&&("number"==typeof e?0===e:0n===e))return` 0${r}${l[0]}`;let b=e<0,h=b?"-":i.signed?"+":"";if(b&&(e=-e),void 0!==i.minimumFractionDigits&&(t={minimumFractionDigits:i.minimumFractionDigits}),void 0!==i.maximumFractionDigits&&(t={maximumFractionDigits:i.maximumFractionDigits,...t}),e<1)return h+m(e,i.locale,t)+r+l[0];let E=Math.min(Math.floor(i.binary?u(e)/Math.log(1024):c(e)/3),l.length-1);return e=d(e,(i.binary?1024:1e3)**E),t||(e=e.toPrecision(3)),h+m(Number(e),i.locale,t)+r+l[E]}var h=t(2784),E=t(59382),S=t(22623),g=t(85950),x=t(61314),p=t(74672);let v={redisStats:"redisStats-bLzaEu"},y=(e,i)=>void 0===e?"-":void 0===i?b(e):`${(e/i*100).toFixed(2)}%`,f=e=>{let{open:i,onClose:t}=e,{t:n}=(0,E.$G)(),[a,s]=(0,h.useState)(null),o=(0,S.h)();if((0,g.Y)(()=>o.getStats().then(e=>s(e)),5e3),!a)return null;let m=[{title:n("REDIS.MEMORY_USAGE"),value:(0,l.jsxs)(l.Fragment,{children:[a.memory.total&&a.memory.used?(0,l.jsxs)("small",{children:[b(a.memory.used)," of ",b(a.memory.total)]}):(0,l.jsx)("small",{className:"error",children:n("REDIS.ERROR.MEMORY_USAGE")}),y(a.memory.used,a.memory.total)]})},{title:n("REDIS.PEEK_MEMORY"),value:b(a.memory.peak)},{title:n("REDIS.FRAGMENTATION_RATIO"),value:a.memory.fragmentationRatio},{title:n("REDIS.CONNECTED_CLIENTS"),value:a.clients.connected},{title:n("REDIS.BLOCKED_CLIENTS"),value:a.clients.blocked},{title:n("REDIS.VERSION"),value:a.version},{title:n("REDIS.MODE"),value:a.mode},{title:n("REDIS.OS"),value:a.os},{title:n("REDIS.UP_TIME"),value:(0,r.Z)(0,1e3*a.uptime,{includeSeconds:!0,locale:x.Y})}];return(0,l.jsx)(p.u,{width:"small",open:i,onClose:t,title:n("REDIS.TITLE"),children:(0,l.jsx)("ul",{className:v.redisStats,children:m.map((e,i)=>(0,l.jsxs)("li",{children:[(0,l.jsx)("span",{children:e.title}),(0,l.jsx)("span",{children:e.value})]},i))})})}}}]);