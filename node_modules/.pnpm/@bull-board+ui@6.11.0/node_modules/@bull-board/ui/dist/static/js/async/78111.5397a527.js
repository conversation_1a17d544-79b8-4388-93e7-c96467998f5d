(self.webpackChunk_bull_board_ui=self.webpackChunk_bull_board_ui||[]).push([["78111"],{89653:function(a,e){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(a){return function(e){var t,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=r.width,i=n&&a.matchPatterns[n]||a.matchPatterns[a.defaultMatchWidth],u=e.match(i);if(!u)return null;var l=u[0],d=n&&a.parsePatterns[n]||a.parsePatterns[a.defaultParseWidth],s=Array.isArray(d)?function(a,e){for(var t=0;t<a.length;t++)if(e(a[t]))return t}(d,function(a){return a.test(l)}):function(a,e){for(var t in a)if(a.hasOwnProperty(t)&&e(a[t]))return t}(d,function(a){return a.test(l)});return t=a.valueCallback?a.valueCallback(s):s,{value:t=r.valueCallback?r.valueCallback(t):t,rest:e.slice(l.length)}}},a.exports=e.default},71604:function(a,e){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(a){return function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=e.match(a.matchPattern);if(!r)return null;var n=r[0],i=e.match(a.parsePattern);if(!i)return null;var u=a.valueCallback?a.valueCallback(i[0]):i[0];return{value:u=t.valueCallback?t.valueCallback(u):u,rest:e.slice(n.length)}}},a.exports=e.default},10312:function(a,e,t){"use strict";var r=t(15755).default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=r(t(89653));e.default={ordinalNumber:(0,r(t(71604)).default)({matchPattern:/^(\d+)(일|번째)?/i,parsePattern:/\d+/i,valueCallback:function(a){return parseInt(a,10)}}),era:(0,n.default)({matchPatterns:{narrow:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(기원전|서기)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^(bc|기원전)/i,/^(ad|서기)/i]},defaultParseWidth:"any"}),quarter:(0,n.default)({matchPatterns:{narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234]사?분기/i},defaultMatchWidth:"wide",parsePatterns:{any:[/1/i,/2/i,/3/i,/4/i]},defaultParseWidth:"any",valueCallback:function(a){return a+1}}),month:(0,n.default)({matchPatterns:{narrow:/^(1[012]|[123456789])/,abbreviated:/^(1[012]|[123456789])월/i,wide:/^(1[012]|[123456789])월/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^1월?$/,/^2/,/^3/,/^4/,/^5/,/^6/,/^7/,/^8/,/^9/,/^10/,/^11/,/^12/]},defaultParseWidth:"any"}),day:(0,n.default)({matchPatterns:{narrow:/^[일월화수목금토]/,short:/^[일월화수목금토]/,abbreviated:/^[일월화수목금토]/,wide:/^[일월화수목금토]요일/},defaultMatchWidth:"wide",parsePatterns:{any:[/^일/,/^월/,/^화/,/^수/,/^목/,/^금/,/^토/]},defaultParseWidth:"any"}),dayPeriod:(0,n.default)({matchPatterns:{any:/^(am|pm|오전|오후|자정|정오|아침|저녁|밤)/i},defaultMatchWidth:"any",parsePatterns:{any:{am:/^(am|오전)/i,pm:/^(pm|오후)/i,midnight:/^자정/i,noon:/^정오/i,morning:/^아침/i,afternoon:/^오후/i,evening:/^저녁/i,night:/^밤/i}},defaultParseWidth:"any"})},a.exports=e.default},15755:function(a){a.exports=function(a){return a&&a.__esModule?a:{default:a}},a.exports.__esModule=!0,a.exports.default=a.exports}}]);