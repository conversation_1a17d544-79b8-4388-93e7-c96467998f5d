"use strict";(self.webpackChunk_bull_board_ui=self.webpackChunk_bull_board_ui||[]).push([["7876"],{73389:function(e,r,o){o.d(r,{$:()=>j});var t=o(52322),a=o(2784),n=o(54250),l=o(66203),i=o(85237),s=o(77730),d=o(35740),c=o(76621),u=o(42367),p=o(21372);let b=i.Qf.define([{tag:u.pJ.atom,color:"var(--hl-keyword)"},{tag:u.pJ.keyword,color:"var(--hl-keyword)"},{tag:u.pJ.bool,color:"var(--hl-keyword)"},{tag:u.pJ.string,color:"var(--hl-string)"},{tag:u.pJ.number,color:"var(--hl-number)"},{tag:u.pJ.brace,color:"var(--accent-color-d1)"},{tag:u.pJ.punctuation,color:"var(--accent-color-d1)"},{tag:u.pJ.propertyName,color:"var(--hl-type)"}]),m=c.tk.theme({"&":{height:"200px",backgroundColor:"var(--input-bg)",border:"1px var(--input-border) solid",borderRadius:"0.375rem",boxShadow:"0 1px 2px 0 rgba(0, 0, 0, 0.05)",marginTop:"0.25rem",fontSize:"0.875rem",transition:"border-color 0.2s ease-out, box-shadow 0.2s ease-out"},"&.cm-focused":{outline:"none",borderColor:"var(--input-focus-border)",boxShadow:"var(--input-focus-shadow)"},".cm-gutters":{borderRadius:"0.375rem 0 0 0.375rem",backgroundColor:"var(--json-edit-gutter-bg)",color:"inherit",borderRight:"var(--json-edit-gutter-border-color)"},".cm-cursor":{borderLeftColor:"var(--json-edit-cursor-color)"},".cm-activeLineGutter":{backgroundColor:"var(--json-edit-gutter-active-bg)"},".cm-scroller":{overflow:"auto"},".cm-tooltip":{padding:"0.25rem 0.5rem",borderRadius:"0.275rem",backgroundColor:"var(--json-edit-tooltip-bg)",border:"1px solid var(--json-edit-tooltip-border-color)"},".cm6-json-schema-hover--code > p":{margin:"0.5em 0"},".cm-tooltip-above .cm-tooltip-arrow:before":{borderTop:"7px solid var(--json-edit-tooltip-border-color)"},".cm-tooltip-above .cm-tooltip-arrow:after":{borderTop:"7px solid var(--json-edit-tooltip-bg)"},".cm-selectionBackground":{background:"var(--json-edit-selection-bg)!important"}}),v=[(0,c.v5)({class:"CodeMirror-lint-markers"}),(0,i.n$)(),(0,c.HQ)(),(0,n.vQ)(),(0,l.m8)(),(0,n.ys)(),(0,c.Eu)(),(0,s.Q2)(),(0,i.nY)(),(0,c.Uw)(),(0,i.mi)(),c.$f.of([...n.GA,...l.wQ,...l.f$,...i.e7,...n.B1,...s.Fv]),c.tk.lineWrapping,d.yy.tabSize.of(2),(0,i.nF)(b),m],h=d.q6.define(),f=e=>{let{doc:r,schema:o,...n}=e,l=(0,a.useRef)(null),i=(0,a.useRef)(null),[u,b]=(0,a.useState)(null);return(0,a.useEffect)(()=>{let e=c.tk.updateListener.of(e=>{if(!i.current)return;let r=(0,s.KS)(e.state);if(e.docChanged||e.transactions.some(e=>e.annotation(h))||0!==r)r>0&&(i.current.value="");else{let r=e.state.doc;i.current.value=r.toString()}}),t=d.yy.create({doc:JSON.stringify(r,null,2),extensions:[v,e,(0,p.fB)(o||{})]}),a=new c.tk({state:t,parent:l.current});return b(a),()=>a.destroy()},[]),(0,a.useEffect)(()=>{u&&(0,p.ov)(u,o||{})},o?[o]:[]),(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{ref:l}),(0,t.jsx)("input",{type:"hidden",ref:i,...n})]})};var g=o(73250);let j=e=>{let{label:r,id:o,value:a,...n}=e;return(0,t.jsx)(g.g,{label:r,id:o,children:(0,t.jsx)(f,{doc:a||{},id:o,...n})})}},74672:function(e,r,o){o.d(r,{u:()=>d});var t=o(52322),a=o(78364),n=o(67239);o(2784);var l=o(59382),i=o(83576),s=o(67755);let d=e=>{let{open:r,title:o,onClose:d,children:c,width:u,actionButton:p}=e,{t:b}=(0,l.$G)();return(0,t.jsx)(a.fC,{open:r,modal:!0,onOpenChange:e=>{e||d()},children:(0,t.jsxs)(a.h_,{children:[(0,t.jsx)(a.aV,{className:s.Z.overlay}),(0,t.jsx)(a.VY,{className:s.Z.contentWrapper,children:(0,t.jsxs)("div",{className:(0,n.Z)(s.Z.content,s.Z[u||""]),children:[!!o&&(0,t.jsx)(a.Dx,{children:o}),(0,t.jsx)(a.dk,{asChild:!0,children:(0,t.jsx)("div",{className:s.Z.description,children:c})}),(0,t.jsxs)("div",{className:s.Z.actions,children:[p,(0,t.jsx)(a.x8,{asChild:!0,children:(0,t.jsx)(i.z,{theme:"basic",children:b("MODAL.CLOSE_BTN")})})]})]})})]})})}},97097:function(e,r,o){o.r(r),o.d(r,{UpdateJobDataModal:()=>u});var t=o(52322);o(2784);var a=o(59382),n=o(9268),l=o(83089),i=o(2575),s=o(83576),d=o(73389),c=o(74672);let u=e=>{let{open:r,onClose:o,job:u}=e,{queues:p}=(0,i.y)(),{actions:b}=(0,l.t)(),m=(0,n.O)(),{t:v}=(0,a.$G)();if(!p||!m)return null;let h=async e=>{e.preventDefault();let r=Object.fromEntries(Array.from(e.target.elements).filter(e=>e.name).map(e=>[e.name,e.value]));try{r.jobData=JSON.parse(r.jobData),await b.updateJobData(m.name,u,r)(),o()}catch(e){console.error(e)}};return(0,t.jsx)(c.u,{width:"small",open:r,onClose:o,title:v("UPDATE_JOB_DATA.TITLE"),actionButton:(0,t.jsx)(s.z,{type:"submit",theme:"primary",form:"edit-job-data-form",children:v("UPDATE_JOB_DATA.UPDATE")}),children:(0,t.jsx)("form",{id:"edit-job-data-form",onSubmit:h,children:(0,t.jsx)(d.$,{label:v("UPDATE_JOB_DATA.JOB_DATA"),value:(null==u?void 0:u.data)||{},id:"job-data",name:"jobData"})})})}}}]);