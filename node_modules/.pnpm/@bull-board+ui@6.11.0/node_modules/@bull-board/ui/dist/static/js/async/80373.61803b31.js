(self.webpackChunk_bull_board_ui=self.webpackChunk_bull_board_ui||[]).push([["80373"],{89653:function(t,a){"use strict";Object.defineProperty(a,"__esModule",{value:!0}),a.default=function(t){return function(a){var e,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=i.width,n=r&&t.matchPatterns[r]||t.matchPatterns[t.defaultMatchWidth],u=a.match(n);if(!u)return null;var l=u[0],d=r&&t.parsePatterns[r]||t.parsePatterns[t.defaultParseWidth],s=Array.isArray(d)?function(t,a){for(var e=0;e<t.length;e++)if(a(t[e]))return e}(d,function(t){return t.test(l)}):function(t,a){for(var e in t)if(t.hasOwnProperty(e)&&a(t[e]))return e}(d,function(t){return t.test(l)});return e=t.valueCallback?t.valueCallback(s):s,{value:e=i.valueCallback?i.valueCallback(e):e,rest:a.slice(l.length)}}},t.exports=a.default},71604:function(t,a){"use strict";Object.defineProperty(a,"__esModule",{value:!0}),a.default=function(t){return function(a){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=a.match(t.matchPattern);if(!i)return null;var r=i[0],n=a.match(t.parsePattern);if(!n)return null;var u=t.valueCallback?t.valueCallback(n[0]):n[0];return{value:u=e.valueCallback?e.valueCallback(u):u,rest:a.slice(r.length)}}},t.exports=a.default},5960:function(t,a,e){"use strict";var i=e(15755).default;Object.defineProperty(a,"__esModule",{value:!0}),a.default=void 0;var r=i(e(89653));a.default={ordinalNumber:(0,i(e(71604)).default)({matchPattern:/^(\d+)(th|st|nd|rd)?/i,parsePattern:/\d+/i,valueCallback:function(t){return parseInt(t,10)}}),era:(0,r.default)({matchPatterns:{narrow:/^(ម\.)?គស/i,abbreviated:/^(មុន)?គ\.ស/i,wide:/^(មុន|នៃ)គ្រិស្តសករាជ/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^(ម|មុន)គ\.?ស/i,/^(នៃ)?គ\.?ស/i]},defaultParseWidth:"any"}),quarter:(0,r.default)({matchPatterns:{narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^(ត្រីមាស)(ទី)?\s?[1234]/i},defaultMatchWidth:"wide",parsePatterns:{any:[/1/i,/2/i,/3/i,/4/i]},defaultParseWidth:"any",valueCallback:function(t){return t+1}}),month:(0,r.default)({matchPatterns:{narrow:/^(ម\.ក|ក\.ម|មិ|ម\.ស|ឧ\.ស|ម\.ថ|ក\.ដ|សី|កញ|តុ|វិ|ធ)/i,abbreviated:/^(មករា|កុម្ភៈ|មីនា|មេសា|ឧសភា|មិថុនា|កក្កដា|សីហា|កញ្ញា|តុលា|វិច្ឆិកា|ធ្នូ)/i,wide:/^(មករា|កុម្ភៈ|មីនា|មេសា|ឧសភា|មិថុនា|កក្កដា|សីហា|កញ្ញា|តុលា|វិច្ឆិកា|ធ្នូ)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^ម\.ក/i,/^ក\.ម/i,/^មិ/i,/^ម\.ស/i,/^ឧ\.ស/i,/^ម\.ថ/i,/^ក\.ដ/i,/^សី/i,/^កញ/i,/^តុ/i,/^វិ/i,/^ធ/i],any:[/^មក/i,/^កុ/i,/^មីន/i,/^មេ/i,/^ឧស/i,/^មិថ/i,/^កក/i,/^សី/i,/^កញ/i,/^តុ/i,/^វិច/i,/^ធ/i]},defaultParseWidth:"any"}),day:(0,r.default)({matchPatterns:{narrow:/^(អា|ច|អ|ព|ព្រ|សុ|ស)/i,short:/^(អា|ច|អ|ព|ព្រ|សុ|ស)/i,abbreviated:/^(អា|ច|អ|ព|ព្រ|សុ|ស)/i,wide:/^(អាទិត្យ|ចន្ទ|អង្គារ|ពុធ|ព្រហស្បតិ៍|សុក្រ|សៅរ៍)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^អា/i,/^ច/i,/^អ/i,/^ព/i,/^ព្រ/i,/^សុ/i,/^ស/i],any:[/^អា/i,/^ច/i,/^អ/i,/^ព/i,/^ព្រ/i,/^សុ/i,/^សៅ/i]},defaultParseWidth:"any"}),dayPeriod:(0,r.default)({matchPatterns:{narrow:/^(ព្រឹក|ល្ងាច|ពេលព្រឹក|ពេលថ្ងៃត្រង់|ពេលល្ងាច|ពេលរសៀល|ពេលយប់|ពេលកណ្ដាលអធ្រាត្រ)/i,any:/^(ព្រឹក|ល្ងាច|ពេលព្រឹក|ពេលថ្ងៃត្រង់|ពេលល្ងាច|ពេលរសៀល|ពេលយប់|ពេលកណ្ដាលអធ្រាត្រ)/i},defaultMatchWidth:"any",parsePatterns:{any:{am:/^ព្រឹក/i,pm:/^ល្ងាច/i,midnight:/^ពេលកណ្ដាលអធ្រាត្រ/i,noon:/^ពេលថ្ងៃត្រង់/i,morning:/ពេលព្រឹក/i,afternoon:/ពេលរសៀល/i,evening:/ពេលល្ងាច/i,night:/ពេលយប់/i}},defaultParseWidth:"any"})},t.exports=a.default},15755:function(t){t.exports=function(t){return t&&t.__esModule?t:{default:t}},t.exports.__esModule=!0,t.exports.default=t.exports}}]);