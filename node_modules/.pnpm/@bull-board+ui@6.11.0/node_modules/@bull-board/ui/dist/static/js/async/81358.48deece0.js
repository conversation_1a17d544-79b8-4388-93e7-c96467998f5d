(self.webpackChunk_bull_board_ui=self.webpackChunk_bull_board_ui||[]).push([["81358"],{89653:function(t,a){"use strict";Object.defineProperty(a,"__esModule",{value:!0}),a.default=function(t){return function(a){var e,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=i.width,n=r&&t.matchPatterns[r]||t.matchPatterns[t.defaultMatchWidth],u=a.match(n);if(!u)return null;var l=u[0],d=r&&t.parsePatterns[r]||t.parsePatterns[t.defaultParseWidth],s=Array.isArray(d)?function(t,a){for(var e=0;e<t.length;e++)if(a(t[e]))return e}(d,function(t){return t.test(l)}):function(t,a){for(var e in t)if(t.hasOwnProperty(e)&&a(t[e]))return e}(d,function(t){return t.test(l)});return e=t.valueCallback?t.valueCallback(s):s,{value:e=i.valueCallback?i.valueCallback(e):e,rest:a.slice(l.length)}}},t.exports=a.default},71604:function(t,a){"use strict";Object.defineProperty(a,"__esModule",{value:!0}),a.default=function(t){return function(a){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=a.match(t.matchPattern);if(!i)return null;var r=i[0],n=a.match(t.parsePattern);if(!n)return null;var u=t.valueCallback?t.valueCallback(n[0]):n[0];return{value:u=e.valueCallback?e.valueCallback(u):u,rest:a.slice(r.length)}}},t.exports=a.default},30275:function(t,a,e){"use strict";var i=e(15755).default;Object.defineProperty(a,"__esModule",{value:!0}),a.default=void 0;var r=i(e(89653));a.default={ordinalNumber:(0,i(e(71604)).default)({matchPattern:/^(\d+)(чи)?/i,parsePattern:/\d+/i,valueCallback:function(t){return parseInt(t,10)}}),era:(0,r.default)({matchPatterns:{narrow:/^(м\.а|м\.)/i,abbreviated:/^(м\.а|м\.)/i,wide:/^(милоддан аввал|милоддан кейин)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^м/i,/^а/i]},defaultParseWidth:"any"}),quarter:(0,r.default)({matchPatterns:{narrow:/^[1234]/i,abbreviated:/^[1234]-чор./i,wide:/^[1234]-чорак/i},defaultMatchWidth:"wide",parsePatterns:{any:[/1/i,/2/i,/3/i,/4/i]},defaultParseWidth:"any",valueCallback:function(t){return t+1}}),month:(0,r.default)({matchPatterns:{narrow:/^[яфмамииасонд]/i,abbreviated:/^(янв|фев|мар|апр|май|июн|июл|авг|сен|окт|ноя|дек)/i,wide:/^(январ|феврал|март|апрел|май|июн|июл|август|сентабр|октабр|ноябр|декабр)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^я/i,/^ф/i,/^м/i,/^а/i,/^м/i,/^и/i,/^и/i,/^а/i,/^с/i,/^о/i,/^н/i,/^д/i],any:[/^я/i,/^ф/i,/^мар/i,/^ап/i,/^май/i,/^июн/i,/^июл/i,/^ав/i,/^с/i,/^о/i,/^н/i,/^д/i]},defaultParseWidth:"any"}),day:(0,r.default)({matchPatterns:{narrow:/^[ядсчпжш]/i,short:/^(як|ду|се|чо|па|жу|ша)/i,abbreviated:/^(якш|душ|сеш|чор|пай|жум|шан)/i,wide:/^(якшанба|душанба|сешанба|чоршанба|пайшанба|жума|шанба)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^я/i,/^д/i,/^с/i,/^ч/i,/^п/i,/^ж/i,/^ш/i],any:[/^як/i,/^ду/i,/^се/i,/^чор/i,/^пай/i,/^жу/i,/^шан/i]},defaultParseWidth:"any"}),dayPeriod:(0,r.default)({matchPatterns:{any:/^(п\.о\.|п\.к\.|ярим тун|пешиндан кейин|(эрталаб|пешиндан кейин|кечаси|тун))/i},defaultMatchWidth:"any",parsePatterns:{any:{am:/^п\.о\./i,pm:/^п\.к\./i,midnight:/^ярим тун/i,noon:/^пешиндан кейин/i,morning:/эрталаб/i,afternoon:/пешиндан кейин/i,evening:/кечаси/i,night:/тун/i}},defaultParseWidth:"any"})},t.exports=a.default},15755:function(t){t.exports=function(t){return t&&t.__esModule?t:{default:t}},t.exports.__esModule=!0,t.exports.default=t.exports}}]);