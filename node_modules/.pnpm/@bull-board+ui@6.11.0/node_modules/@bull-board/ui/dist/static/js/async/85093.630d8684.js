(self.webpackChunk_bull_board_ui=self.webpackChunk_bull_board_ui||[]).push([["85093"],{90733:function(e){class t{constructor(e){void 0===e.data&&(e.data={}),this.data=e.data,this.isMatchIgnored=!1}ignoreMatch(){this.isMatchIgnored=!0}}function n(e){return e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#x27;")}function i(e,...t){let n=Object.create(null);for(let t in e)n[t]=e[t];return t.forEach(function(e){for(let t in e)n[t]=e[t]}),n}let r=e=>!!e.scope,o=(e,{prefix:t})=>{if(e.startsWith("language:"))return e.replace("language:","language-");if(e.includes(".")){let n=e.split(".");return[`${t}${n.shift()}`,...n.map((e,t)=>`${e}${"_".repeat(t+1)}`)].join(" ")}return`${t}${e}`};class s{constructor(e,t){this.buffer="",this.classPrefix=t.classPrefix,e.walk(this)}addText(e){this.buffer+=n(e)}openNode(e){if(!r(e))return;let t=o(e.scope,{prefix:this.classPrefix});this.span(t)}closeNode(e){r(e)&&(this.buffer+="</span>")}value(){return this.buffer}span(e){this.buffer+=`<span class="${e}">`}}let l=(e={})=>{let t={children:[]};return Object.assign(t,e),t};class a{constructor(){this.rootNode=l(),this.stack=[this.rootNode]}get top(){return this.stack[this.stack.length-1]}get root(){return this.rootNode}add(e){this.top.children.push(e)}openNode(e){let t=l({scope:e});this.add(t),this.stack.push(t)}closeNode(){if(this.stack.length>1)return this.stack.pop()}closeAllNodes(){for(;this.closeNode(););}toJSON(){return JSON.stringify(this.rootNode,null,4)}walk(e){return this.constructor._walk(e,this.rootNode)}static _walk(e,t){return"string"==typeof t?e.addText(t):t.children&&(e.openNode(t),t.children.forEach(t=>this._walk(e,t)),e.closeNode(t)),e}static _collapse(e){"string"!=typeof e&&e.children&&(e.children.every(e=>"string"==typeof e)?e.children=[e.children.join("")]:e.children.forEach(e=>{a._collapse(e)}))}}class c extends a{constructor(e){super(),this.options=e}addText(e){""!==e&&this.add(e)}startScope(e){this.openNode(e)}endScope(){this.closeNode()}__addSublanguage(e,t){let n=e.root;t&&(n.scope=`language:${t}`),this.add(n)}toHTML(){return new s(this,this.options).value()}finalize(){return this.closeAllNodes(),!0}}function u(e){return e?"string"==typeof e?e:e.source:null}function g(e){return f("(?=",e,")")}function d(e){return f("(?:",e,")*")}function h(e){return f("(?:",e,")?")}function f(...e){return e.map(e=>u(e)).join("")}function p(...e){return"("+(function(e){let t=e[e.length-1];return"object"==typeof t&&t.constructor===Object?(e.splice(e.length-1,1),t):{}}(e).capture?"":"?:")+e.map(e=>u(e)).join("|")+")"}function b(e){return RegExp(e.toString()+"|").exec("").length-1}let m=/\[(?:[^\\\]]|\\.)*\]|\(\??|\\([1-9][0-9]*)|\\./;function E(e,{joinWith:t}){let n=0;return e.map(e=>{let t=n+=1,i=u(e),r="";for(;i.length>0;){let e=m.exec(i);if(!e){r+=i;break}r+=i.substring(0,e.index),i=i.substring(e.index+e[0].length),"\\"===e[0][0]&&e[1]?r+="\\"+String(Number(e[1])+t):(r+=e[0],"("===e[0]&&n++)}return r}).map(e=>`(${e})`).join(t)}let _="[a-zA-Z]\\w*",x="[a-zA-Z_]\\w*",w="\\b\\d+(\\.\\d+)?",y="(-?)(\\b0[xX][a-fA-F0-9]+|(\\b\\d+(\\.\\d*)?|\\.\\d+)([eE][-+]?\\d+)?)",O="\\b(0b[01]+)",M={begin:"\\\\[\\s\\S]",relevance:0},N=function(e,t,n={}){let r=i({scope:"comment",begin:e,end:t,contains:[]},n);r.contains.push({scope:"doctag",begin:"[ ]*(?=(TODO|FIXME|NOTE|BUG|OPTIMIZE|HACK|XXX):)",end:/(TODO|FIXME|NOTE|BUG|OPTIMIZE|HACK|XXX):/,excludeBegin:!0,relevance:0});let o=p("I","a","is","so","us","to","at","if","in","it","on",/[A-Za-z]+['](d|ve|re|ll|t|s|n)/,/[A-Za-z]+[-][a-z]+/,/[A-Za-z][a-z]{2,}/);return r.contains.push({begin:f(/[ ]+/,"(",o,/[.]?[:]?([.][ ]|[ ])/,"){3}")}),r},S=N("//","$"),k=N("/\\*","\\*/"),v=N("#","$");var R=Object.freeze({__proto__:null,APOS_STRING_MODE:{scope:"string",begin:"'",end:"'",illegal:"\\n",contains:[M]},BACKSLASH_ESCAPE:M,BINARY_NUMBER_MODE:{scope:"number",begin:O,relevance:0},BINARY_NUMBER_RE:O,COMMENT:N,C_BLOCK_COMMENT_MODE:k,C_LINE_COMMENT_MODE:S,C_NUMBER_MODE:{scope:"number",begin:y,relevance:0},C_NUMBER_RE:y,END_SAME_AS_BEGIN:function(e){return Object.assign(e,{"on:begin":(e,t)=>{t.data._beginMatch=e[1]},"on:end":(e,t)=>{t.data._beginMatch!==e[1]&&t.ignoreMatch()}})},HASH_COMMENT_MODE:v,IDENT_RE:_,MATCH_NOTHING_RE:/\b\B/,METHOD_GUARD:{begin:"\\.\\s*"+x,relevance:0},NUMBER_MODE:{scope:"number",begin:w,relevance:0},NUMBER_RE:w,PHRASAL_WORDS_MODE:{begin:/\b(a|an|the|are|I'm|isn't|don't|doesn't|won't|but|just|should|pretty|simply|enough|gonna|going|wtf|so|such|will|you|your|they|like|more)\b/},QUOTE_STRING_MODE:{scope:"string",begin:'"',end:'"',illegal:"\\n",contains:[M]},REGEXP_MODE:{scope:"regexp",begin:/\/(?=[^/\n]*\/)/,end:/\/[gimuy]*/,contains:[M,{begin:/\[/,end:/\]/,relevance:0,contains:[M]}]},RE_STARTERS_RE:"!|!=|!==|%|%=|&|&&|&=|\\*|\\*=|\\+|\\+=|,|-|-=|/=|/|:|;|<<|<<=|<=|<|===|==|=|>>>=|>>=|>=|>>>|>>|>|\\?|\\[|\\{|\\(|\\^|\\^=|\\||\\|=|\\|\\||~",SHEBANG:(e={})=>{let t=/^#![ ]*\//;return e.binary&&(e.begin=f(t,/.*\b/,e.binary,/\b.*/)),i({scope:"meta",begin:t,end:/$/,relevance:0,"on:begin":(e,t)=>{0!==e.index&&t.ignoreMatch()}},e)},TITLE_MODE:{scope:"title",begin:_,relevance:0},UNDERSCORE_IDENT_RE:x,UNDERSCORE_TITLE_MODE:{scope:"title",begin:x,relevance:0}});function j(e,t){"."===e.input[e.index-1]&&t.ignoreMatch()}function T(e,t){void 0!==e.className&&(e.scope=e.className,delete e.className)}function A(e,t){t&&e.beginKeywords&&(e.begin="\\b("+e.beginKeywords.split(" ").join("|")+")(?!\\.)(?=\\b|\\s)",e.__beforeBegin=j,e.keywords=e.keywords||e.beginKeywords,delete e.beginKeywords,void 0===e.relevance&&(e.relevance=0))}function I(e,t){Array.isArray(e.illegal)&&(e.illegal=p(...e.illegal))}function L(e,t){if(e.match){if(e.begin||e.end)throw Error("begin & end are not supported with match");e.begin=e.match,delete e.match}}function B(e,t){void 0===e.relevance&&(e.relevance=1)}let C=(e,t)=>{if(!e.beforeMatch)return;if(e.starts)throw Error("beforeMatch cannot be used with starts");let n=Object.assign({},e);Object.keys(e).forEach(t=>{delete e[t]}),e.keywords=n.keywords,e.begin=f(n.beforeMatch,g(n.begin)),e.starts={relevance:0,contains:[Object.assign(n,{endsParent:!0})]},e.relevance=0,delete n.beforeMatch},D=["of","and","for","in","not","or","if","then","parent","list","value"],H={},P=e=>{console.error(e)},$=(e,...t)=>{console.log(`WARN: ${e}`,...t)},U=(e,t)=>{H[`${e}/${t}`]||(console.log(`Deprecated as of ${e}. ${t}`),H[`${e}/${t}`]=!0)},z=Error();function W(e,t,{key:n}){let i=0,r=e[n],o={},s={};for(let e=1;e<=t.length;e++)s[e+i]=r[e],o[e+i]=!0,i+=b(t[e-1]);e[n]=s,e[n]._emit=o,e[n]._multi=!0}function G(e){if(e.scope&&"object"==typeof e.scope&&null!==e.scope&&(e.beginScope=e.scope,delete e.scope),"string"==typeof e.beginScope&&(e.beginScope={_wrap:e.beginScope}),"string"==typeof e.endScope&&(e.endScope={_wrap:e.endScope}),Array.isArray(e.begin)){if(e.skip||e.excludeBegin||e.returnBegin)throw P("skip, excludeBegin, returnBegin not compatible with beginScope: {}"),z;if("object"!=typeof e.beginScope||null===e.beginScope)throw P("beginScope must be object"),z;W(e,e.begin,{key:"beginScope"}),e.begin=E(e.begin,{joinWith:""})}if(Array.isArray(e.end)){if(e.skip||e.excludeEnd||e.returnEnd)throw P("skip, excludeEnd, returnEnd not compatible with endScope: {}"),z;if("object"!=typeof e.endScope||null===e.endScope)throw P("endScope must be object"),z;W(e,e.end,{key:"endScope"}),e.end=E(e.end,{joinWith:""})}}class K extends Error{constructor(e,t){super(e),this.name="HTMLInjectionError",this.html=t}}let X=Symbol("nomatch"),Z=function(e){let r=Object.create(null),o=Object.create(null),s=[],l=!0,a="Could not find the language '{}', did you forget to load/include a language module?",m={disableAutodetect:!0,name:"Plain text",contains:[]},_={ignoreUnescapedHTML:!1,throwUnescapedHTML:!1,noHighlightRe:/^(no-?highlight)$/i,languageDetectRe:/\blang(?:uage)?-([\w-]+)\b/i,classPrefix:"hljs-",cssSelector:"pre code",languages:null,__emitter:c};function x(e){return _.noHighlightRe.test(e)}function w(e,t,n){let i="",r="";"object"==typeof t?(i=e,n=t.ignoreIllegals,r=t.language):(U("10.7.0","highlight(lang, code, ...args) has been deprecated."),U("10.7.0","Please use highlight(code, options) instead.\nhttps://github.com/highlightjs/highlight.js/issues/2277"),r=e,i=t),void 0===n&&(n=!0);let o={code:i,language:r};H("before:highlight",o);let s=o.result?o.result:y(o.language,o.code,n);return s.code=o.code,H("after:highlight",s),s}function y(e,o,s,c){let g=Object.create(null);function d(){if(!v.keywords)return void j.addText($);let e=0;v.keywordPatternRe.lastIndex=0;let t=v.keywordPatternRe.exec($),n="";for(;t;){n+=$.substring(e,t.index);let i=M.case_insensitive?t[0].toLowerCase():t[0],r=v.keywords[i];if(r){let[e,o]=r;if(j.addText(n),n="",g[i]=(g[i]||0)+1,g[i]<=7&&(U+=o),e.startsWith("_"))n+=t[0];else{let n=M.classNameAliases[e]||e;f(t[0],n)}}else n+=t[0];e=v.keywordPatternRe.lastIndex,t=v.keywordPatternRe.exec($)}n+=$.substring(e),j.addText(n)}function h(){null!=v.subLanguage?function(){if(""===$)return;let e=null;if("string"==typeof v.subLanguage){if(!r[v.subLanguage])return j.addText($);e=y(v.subLanguage,$,!0,R[v.subLanguage]),R[v.subLanguage]=e._top}else e=O($,v.subLanguage.length?v.subLanguage:null);v.relevance>0&&(U+=e.relevance),j.__addSublanguage(e._emitter,e.language)}():d(),$=""}function f(e,t){""!==e&&(j.startScope(t),j.addText(e),j.endScope())}function p(e,t){let n=1,i=t.length-1;for(;n<=i;){if(!e._emit[n]){n++;continue}let i=M.classNameAliases[e[n]]||e[n],r=t[n];i?f(r,i):($=r,d(),$=""),n++}}function m(e,t){return e.scope&&"string"==typeof e.scope&&j.openNode(M.classNameAliases[e.scope]||e.scope),e.beginScope&&(e.beginScope._wrap?(f($,M.classNameAliases[e.beginScope._wrap]||e.beginScope._wrap),$=""):e.beginScope._multi&&(p(e.beginScope,t),$="")),v=Object.create(e,{parent:{value:v}})}let x={};function w(n,i){let r=i&&i[0];if($+=n,null==r)return h(),0;if("begin"===x.type&&"end"===i.type&&x.index===i.index&&""===r){if($+=o.slice(i.index,i.index+1),!l){let t=Error(`0 width match regex (${e})`);throw t.languageName=e,t.badRule=x.rule,t}return 1}if(x=i,"begin"===i.type){let e=i[0],n=i.rule,r=new t(n);for(let t of[n.__beforeBegin,n["on:begin"]])if(t&&(t(i,r),r.isMatchIgnored))return 0===v.matcher.regexIndex?($+=e[0],1):(K=!0,0);return n.skip?$+=e:(n.excludeBegin&&($+=e),h(),n.returnBegin||n.excludeBegin||($=e)),m(n,i),n.returnBegin?0:e.length}if("illegal"!==i.type||s){if("end"===i.type){let e=function(e){let n=e[0],i=o.substring(e.index),r=function e(n,i,r){let o=function(e,t){let n=e&&e.exec(t);return n&&0===n.index}(n.endRe,r);if(o){if(n["on:end"]){let e=new t(n);n["on:end"](i,e),e.isMatchIgnored&&(o=!1)}if(o){for(;n.endsParent&&n.parent;)n=n.parent;return n}}if(n.endsWithParent)return e(n.parent,i,r)}(v,e,i);if(!r)return X;let s=v;v.endScope&&v.endScope._wrap?(h(),f(n,v.endScope._wrap)):v.endScope&&v.endScope._multi?(h(),p(v.endScope,e)):s.skip?$+=n:(s.returnEnd||s.excludeEnd||($+=n),h(),s.excludeEnd&&($=n));do v.scope&&j.closeNode(),v.skip||v.subLanguage||(U+=v.relevance),v=v.parent;while(v!==r.parent);return r.starts&&m(r.starts,e),s.returnEnd?0:n.length}(i);if(e!==X)return e}}else{let e=Error('Illegal lexeme "'+r+'" for mode "'+(v.scope||"<unnamed>")+'"');throw e.mode=v,e}if("illegal"===i.type&&""===r)return $+="\n",1;if(W>1e5&&W>3*i.index)throw Error("potential infinite loop, way more iterations than matches");return $+=r,r.length}let M=k(e);if(!M)throw P(a.replace("{}",e)),Error('Unknown language: "'+e+'"');let N=function(e){function t(t,n){return RegExp(u(t),"m"+(e.case_insensitive?"i":"")+(e.unicodeRegex?"u":"")+(n?"g":""))}class n{constructor(){this.matchIndexes={},this.regexes=[],this.matchAt=1,this.position=0}addRule(e,t){t.position=this.position++,this.matchIndexes[this.matchAt]=t,this.regexes.push([t,e]),this.matchAt+=b(e)+1}compile(){0===this.regexes.length&&(this.exec=()=>null);let e=this.regexes.map(e=>e[1]);this.matcherRe=t(E(e,{joinWith:"|"}),!0),this.lastIndex=0}exec(e){this.matcherRe.lastIndex=this.lastIndex;let t=this.matcherRe.exec(e);if(!t)return null;let n=t.findIndex((e,t)=>t>0&&void 0!==e),i=this.matchIndexes[n];return t.splice(0,n),Object.assign(t,i)}}class r{constructor(){this.rules=[],this.multiRegexes=[],this.count=0,this.lastIndex=0,this.regexIndex=0}getMatcher(e){if(this.multiRegexes[e])return this.multiRegexes[e];let t=new n;return this.rules.slice(e).forEach(([e,n])=>t.addRule(e,n)),t.compile(),this.multiRegexes[e]=t,t}resumingScanAtSamePosition(){return 0!==this.regexIndex}considerAll(){this.regexIndex=0}addRule(e,t){this.rules.push([e,t]),"begin"===t.type&&this.count++}exec(e){let t=this.getMatcher(this.regexIndex);t.lastIndex=this.lastIndex;let n=t.exec(e);if(this.resumingScanAtSamePosition())if(n&&n.index===this.lastIndex);else{let t=this.getMatcher(0);t.lastIndex=this.lastIndex+1,n=t.exec(e)}return n&&(this.regexIndex+=n.position+1,this.regexIndex===this.count&&this.considerAll()),n}}if(e.compilerExtensions||(e.compilerExtensions=[]),e.contains&&e.contains.includes("self"))throw Error("ERR: contains `self` is not supported at the top-level of a language.  See documentation.");return e.classNameAliases=i(e.classNameAliases||{}),function n(o,s){if(o.isCompiled)return o;[T,L,G,C].forEach(e=>e(o,s)),e.compilerExtensions.forEach(e=>e(o,s)),o.__beforeBegin=null,[A,I,B].forEach(e=>e(o,s)),o.isCompiled=!0;let l=null;return"object"==typeof o.keywords&&o.keywords.$pattern&&(o.keywords=Object.assign({},o.keywords),l=o.keywords.$pattern,delete o.keywords.$pattern),l=l||/\w+/,o.keywords&&(o.keywords=function e(t,n,i="keyword"){let r=Object.create(null);return"string"==typeof t?o(i,t.split(" ")):Array.isArray(t)?o(i,t):Object.keys(t).forEach(function(i){Object.assign(r,e(t[i],n,i))}),r;function o(e,t){n&&(t=t.map(e=>e.toLowerCase())),t.forEach(function(t){var n,i,o;let s=t.split("|");r[s[0]]=[e,(n=s[0],(i=s[1])?Number(i):+(o=n,!D.includes(o.toLowerCase())))]})}}(o.keywords,e.case_insensitive)),o.keywordPatternRe=t(l,!0),s&&(o.begin||(o.begin=/\B|\b/),o.beginRe=t(o.begin),o.end||o.endsWithParent||(o.end=/\B|\b/),o.end&&(o.endRe=t(o.end)),o.terminatorEnd=u(o.end)||"",o.endsWithParent&&s.terminatorEnd&&(o.terminatorEnd+=(o.end?"|":"")+s.terminatorEnd)),o.illegal&&(o.illegalRe=t(o.illegal)),o.contains||(o.contains=[]),o.contains=[].concat(...o.contains.map(function(e){var t;return((t="self"===e?o:e).variants&&!t.cachedVariants&&(t.cachedVariants=t.variants.map(function(e){return i(t,{variants:null},e)})),t.cachedVariants)?t.cachedVariants:!function e(t){return!!t&&(t.endsWithParent||e(t.starts))}(t)?Object.isFrozen(t)?i(t):t:i(t,{starts:t.starts?i(t.starts):null})})),o.contains.forEach(function(e){n(e,o)}),o.starts&&n(o.starts,s),o.matcher=function(e){let t=new r;return e.contains.forEach(e=>t.addRule(e.begin,{rule:e,type:"begin"})),e.terminatorEnd&&t.addRule(e.terminatorEnd,{type:"end"}),e.illegal&&t.addRule(e.illegal,{type:"illegal"}),t}(o),o}(e)}(M),S="",v=c||N,R={},j=new _.__emitter(_),H=[];for(let e=v;e!==M;e=e.parent)e.scope&&H.unshift(e.scope);H.forEach(e=>j.openNode(e));let $="",U=0,z=0,W=0,K=!1;try{if(M.__emitTokens)M.__emitTokens(o,j);else{for(v.matcher.considerAll();;){W++,K?K=!1:v.matcher.considerAll(),v.matcher.lastIndex=z;let e=v.matcher.exec(o);if(!e)break;let t=o.substring(z,e.index),n=w(t,e);z=e.index+n}w(o.substring(z))}return j.finalize(),S=j.toHTML(),{language:e,value:S,relevance:U,illegal:!1,_emitter:j,_top:v}}catch(t){if(t.message&&t.message.includes("Illegal"))return{language:e,value:n(o),illegal:!0,relevance:0,_illegalBy:{message:t.message,index:z,context:o.slice(z-100,z+100),mode:t.mode,resultSoFar:S},_emitter:j};if(l)return{language:e,value:n(o),illegal:!1,relevance:0,errorRaised:t,_emitter:j,_top:v};throw t}}function O(e,t){t=t||_.languages||Object.keys(r);let i=function(e){let t={value:n(e),illegal:!1,relevance:0,_top:m,_emitter:new _.__emitter(_)};return t._emitter.addText(e),t}(e),o=t.filter(k).filter(j).map(t=>y(t,e,!1));o.unshift(i);let[s,l]=o.sort((e,t)=>{if(e.relevance!==t.relevance)return t.relevance-e.relevance;if(e.language&&t.language){if(k(e.language).supersetOf===t.language)return 1;else if(k(t.language).supersetOf===e.language)return -1}return 0});return s.secondBest=l,s}function M(e){let t=null,n=function(e){let t=e.className+" ";t+=e.parentNode?e.parentNode.className:"";let n=_.languageDetectRe.exec(t);if(n){let t=k(n[1]);return t||($(a.replace("{}",n[1])),$("Falling back to no-highlight mode for this block.",e)),t?n[1]:"no-highlight"}return t.split(/\s+/).find(e=>x(e)||k(e))}(e);if(x(n))return;if(H("before:highlightElement",{el:e,language:n}),e.dataset.highlighted)return void console.log("Element previously highlighted. To highlight again, first unset `dataset.highlighted`.",e);if(e.children.length>0&&(_.ignoreUnescapedHTML||(console.warn("One of your code blocks includes unescaped HTML. This is a potentially serious security risk."),console.warn("https://github.com/highlightjs/highlight.js/wiki/security"),console.warn("The element with unescaped HTML:"),console.warn(e)),_.throwUnescapedHTML))throw new K("One of your code blocks includes unescaped HTML.",e.innerHTML);let i=e.textContent,r=n?w(i,{language:n,ignoreIllegals:!0}):O(i);e.innerHTML=r.value,e.dataset.highlighted="yes";var s=r.language;let l=n&&o[n]||s;e.classList.add("hljs"),e.classList.add(`language-${l}`),e.result={language:r.language,re:r.relevance,relevance:r.relevance},r.secondBest&&(e.secondBest={language:r.secondBest.language,relevance:r.secondBest.relevance}),H("after:highlightElement",{el:e,result:r,text:i})}let N=!1;function S(){if("loading"===document.readyState){N||window.addEventListener("DOMContentLoaded",function(){S()},!1),N=!0;return}document.querySelectorAll(_.cssSelector).forEach(M)}function k(e){return r[e=(e||"").toLowerCase()]||r[o[e]]}function v(e,{languageName:t}){"string"==typeof e&&(e=[e]),e.forEach(e=>{o[e.toLowerCase()]=t})}function j(e){let t=k(e);return t&&!t.disableAutodetect}function H(e,t){s.forEach(function(n){n[e]&&n[e](t)})}for(let t in Object.assign(e,{highlight:w,highlightAuto:O,highlightAll:S,highlightElement:M,highlightBlock:function(e){return U("10.7.0","highlightBlock will be removed entirely in v12.0"),U("10.7.0","Please use highlightElement now."),M(e)},configure:function(e){_=i(_,e)},initHighlighting:()=>{S(),U("10.6.0","initHighlighting() deprecated.  Use highlightAll() now.")},initHighlightingOnLoad:function(){S(),U("10.6.0","initHighlightingOnLoad() deprecated.  Use highlightAll() now.")},registerLanguage:function(t,n){let i=null;try{i=n(e)}catch(e){if(P("Language definition for '{}' could not be registered.".replace("{}",t)),l)P(e);else throw e;i=m}i.name||(i.name=t),r[t]=i,i.rawDefinition=n.bind(null,e),i.aliases&&v(i.aliases,{languageName:t})},unregisterLanguage:function(e){for(let t of(delete r[e],Object.keys(o)))o[t]===e&&delete o[t]},listLanguages:function(){return Object.keys(r)},getLanguage:k,registerAliases:v,autoDetection:j,inherit:i,addPlugin:function(e){var t;(t=e)["before:highlightBlock"]&&!t["before:highlightElement"]&&(t["before:highlightElement"]=e=>{t["before:highlightBlock"](Object.assign({block:e.el},e))}),t["after:highlightBlock"]&&!t["after:highlightElement"]&&(t["after:highlightElement"]=e=>{t["after:highlightBlock"](Object.assign({block:e.el},e))}),s.push(e)},removePlugin:function(e){let t=s.indexOf(e);-1!==t&&s.splice(t,1)}}),e.debugMode=function(){l=!1},e.safeMode=function(){l=!0},e.versionString="11.11.1",e.regex={concat:f,lookahead:g,either:p,optional:h,anyNumberOfTimes:d},R)"object"==typeof R[t]&&function e(t){return t instanceof Map?t.clear=t.delete=t.set=function(){throw Error("map is read-only")}:t instanceof Set&&(t.add=t.clear=t.delete=function(){throw Error("set is read-only")}),Object.freeze(t),Object.getOwnPropertyNames(t).forEach(n=>{let i=t[n],r=typeof i;"object"!==r&&"function"!==r||Object.isFrozen(i)||e(i)}),t}(R[t]);return Object.assign(e,R),e},F=Z({});F.newInstance=()=>Z({}),e.exports=F,F.HighlightJS=F,F.default=F},19294:function(e,t,n){"use strict";n.d(t,{Z:()=>i});let i=n(90733)},49436:function(e,t,n){"use strict";function i(e){let t=["true","false","null"],n={scope:"literal",beginKeywords:t.join(" ")};return{name:"JSON",aliases:["jsonc"],keywords:{literal:t},contains:[{className:"attr",begin:/"(\\.|[^\\"\r\n])*"(?=\s*:)/,relevance:1.01},{match:/[{}[\],:]/,className:"punctuation",relevance:0},e.QUOTE_STRING_MODE,n,e.C_NUMBER_MODE,e.C_LINE_COMMENT_MODE,e.C_BLOCK_COMMENT_MODE],illegal:"\\S"}}n.d(t,{Z:()=>i})}}]);