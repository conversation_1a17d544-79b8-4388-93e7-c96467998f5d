(self.webpackChunk_bull_board_ui=self.webpackChunk_bull_board_ui||[]).push([["85787"],{1906:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getDefaultOptions=function(){return r},t.setDefaultOptions=function(e){r=e};var r={}},85345:function(e,t,r){"use strict";var n=r(15755).default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t,r){(0,u.default)(2,arguments);var n=(0,a.default)(e,r),o=(0,a.default)(t,r);return n.getTime()===o.getTime()};var u=n(r(26193)),a=n(r(23658));e.exports=t.default},26193:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t){if(t.length<e)throw TypeError(e+" argument"+(e>1?"s":"")+" required, but only "+t.length+" present")},e.exports=t.default},23658:function(e,t,r){"use strict";var n=r(15755).default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t){(0,a.default)(1,arguments);var r,n,l,f,c,i,d,p,b=(0,s.getDefaultOptions)(),v=(0,o.default)(null!=(r=null!=(n=null!=(l=null!=(f=null==t?void 0:t.weekStartsOn)?f:null==t||null==(c=t.locale)||null==(i=c.options)?void 0:i.weekStartsOn)?l:b.weekStartsOn)?n:null==(d=b.locale)||null==(p=d.options)?void 0:p.weekStartsOn)?r:0);if(!(v>=0&&v<=6))throw RangeError("weekStartsOn must be between 0 and 6 inclusively");var y=(0,u.default)(e),_=y.getUTCDay();return y.setUTCDate(y.getUTCDate()-(7*(_<v)+_-v)),y.setUTCHours(0,0,0,0),y};var u=n(r(39276)),a=n(r(26193)),o=n(r(65862)),s=r(1906);e.exports=t.default},65862:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){if(null===e||!0===e||!1===e)return NaN;var t=Number(e);return isNaN(t)?t:t<0?Math.ceil(t):Math.floor(t)},e.exports=t.default},66842:function(e,t,r){"use strict";var n=r(15755).default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var u=n(r(85345)),a=["недела","понеделник","вторник","среда","четврток","петок","сабота"];function o(e){var t=a[e];switch(e){case 0:case 3:case 6:return"'ова "+t+" вo' p";case 1:case 2:case 4:case 5:return"'овој "+t+" вo' p"}}var s={lastWeek:function(e,t,r){var n=e.getUTCDay();if((0,u.default)(e,t,r))return o(n);var s=a[n];switch(n){case 0:case 3:case 6:return"'минатата "+s+" во' p";case 1:case 2:case 4:case 5:return"'минатиот "+s+" во' p"}},yesterday:"'вчера во' p",today:"'денес во' p",tomorrow:"'утре во' p",nextWeek:function(e,t,r){var n=e.getUTCDay();if((0,u.default)(e,t,r))return o(n);var s=a[n];switch(n){case 0:case 3:case 6:return"'следната "+s+" вo' p";case 1:case 2:case 4:case 5:return"'следниот "+s+" вo' p"}},other:"P"};t.default=function(e,t,r,n){var u=s[e];return"function"==typeof u?u(t,r,n):u},e.exports=t.default},39276:function(e,t,r){"use strict";var n=r(15755).default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){(0,a.default)(1,arguments);var t=Object.prototype.toString.call(e);return e instanceof Date||"object"===(0,u.default)(e)&&"[object Date]"===t?new Date(e.getTime()):"number"==typeof e||"[object Number]"===t?new Date(e):(("string"==typeof e||"[object String]"===t)&&"undefined"!=typeof console&&(console.warn("Starting with v2.0.0-beta.1 date-fns doesn't accept strings as date arguments. Please use `parseISO` to parse strings. See: https://github.com/date-fns/date-fns/blob/master/docs/upgradeGuide.md#string-arguments"),console.warn(Error().stack)),new Date(NaN))};var u=n(r(69430)),a=n(r(26193));e.exports=t.default},15755:function(e){e.exports=function(e){return e&&e.__esModule?e:{default:e}},e.exports.__esModule=!0,e.exports.default=e.exports},69430:function(e){function t(r){return e.exports=t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e.exports.__esModule=!0,e.exports.default=e.exports,t(r)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports}}]);