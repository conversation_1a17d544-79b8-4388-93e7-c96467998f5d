(self.webpackChunk_bull_board_ui=self.webpackChunk_bull_board_ui||[]).push([["85926"],{22348:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){return function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},a=t.width?String(t.width):e.defaultWidth;return e.formats[a]||e.formats[e.defaultWidth]}},e.exports=t.default},84738:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){return function(t,a){var n;if("formatting"===(null!=a&&a.context?String(a.context):"standalone")&&e.formattingValues){var r=e.defaultFormattingWidth||e.defaultWidth,u=null!=a&&a.width?String(a.width):r;n=e.formattingValues[u]||e.formattingValues[r]}else{var i=e.defaultWidth,o=null!=a&&a.width?String(a.width):e.defaultWidth;n=e.values[o]||e.values[i]}return n[e.argumentCallback?e.argumentCallback(t):t]}},e.exports=t.default},89653:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){return function(t){var a,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=n.width,u=r&&e.matchPatterns[r]||e.matchPatterns[e.defaultMatchWidth],i=t.match(u);if(!i)return null;var o=i[0],d=r&&e.parsePatterns[r]||e.parsePatterns[e.defaultParseWidth],l=Array.isArray(d)?function(e,t){for(var a=0;a<e.length;a++)if(t(e[a]))return a}(d,function(e){return e.test(o)}):function(e,t){for(var a in e)if(e.hasOwnProperty(a)&&t(e[a]))return a}(d,function(e){return e.test(o)});return a=e.valueCallback?e.valueCallback(l):l,{value:a=n.valueCallback?n.valueCallback(a):a,rest:t.slice(o.length)}}},e.exports=t.default},71604:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){return function(t){var a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.match(e.matchPattern);if(!n)return null;var r=n[0],u=t.match(e.parsePattern);if(!u)return null;var i=e.valueCallback?e.valueCallback(u[0]):u[0];return{value:i=a.valueCallback?a.valueCallback(i):i,rest:t.slice(r.length)}}},e.exports=t.default},66050:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a={lessThanXSeconds:{one:"1びょうみまん",other:"{{count}}びょうみまん",oneWithSuffix:"やく1びょう",otherWithSuffix:"やく{{count}}びょう"},xSeconds:{one:"1びょう",other:"{{count}}びょう"},halfAMinute:"30びょう",lessThanXMinutes:{one:"1ぷんみまん",other:"{{count}}ふんみまん",oneWithSuffix:"やく1ぷん",otherWithSuffix:"やく{{count}}ふん"},xMinutes:{one:"1ぷん",other:"{{count}}ふん"},aboutXHours:{one:"やく1じかん",other:"やく{{count}}じかん"},xHours:{one:"1じかん",other:"{{count}}じかん"},xDays:{one:"1にち",other:"{{count}}にち"},aboutXWeeks:{one:"やく1しゅうかん",other:"やく{{count}}しゅうかん"},xWeeks:{one:"1しゅうかん",other:"{{count}}しゅうかん"},aboutXMonths:{one:"やく1かげつ",other:"やく{{count}}かげつ"},xMonths:{one:"1かげつ",other:"{{count}}かげつ"},aboutXYears:{one:"やく1ねん",other:"やく{{count}}ねん"},xYears:{one:"1ねん",other:"{{count}}ねん"},overXYears:{one:"1ねんいじょう",other:"{{count}}ねんいじょう"},almostXYears:{one:"1ねんちかく",other:"{{count}}ねんちかく"}};t.default=function(e,t,n){n=n||{};var r,u=a[e];if(r="string"==typeof u?u:1===t?n.addSuffix&&u.oneWithSuffix?u.oneWithSuffix:u.one:n.addSuffix&&u.otherWithSuffix?u.otherWithSuffix.replace("{{count}}",String(t)):u.other.replace("{{count}}",String(t)),n.addSuffix)if(n.comparison&&n.comparison>0)return r+"あと";else return r+"まえ";return r},e.exports=t.default},25773:function(e,t,a){"use strict";var n=a(15755).default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=n(a(22348));t.default={date:(0,r.default)({formats:{full:"yねんMがつdにちEEEE",long:"yねんMがつdにち",medium:"y/MM/dd",short:"y/MM/dd"},defaultWidth:"full"}),time:(0,r.default)({formats:{full:"Hじmmふんssびょう zzzz",long:"H:mm:ss z",medium:"H:mm:ss",short:"H:mm"},defaultWidth:"full"}),dateTime:(0,r.default)({formats:{full:"{{date}} {{time}}",long:"{{date}} {{time}}",medium:"{{date}} {{time}}",short:"{{date}} {{time}}"},defaultWidth:"full"})},e.exports=t.default},41178:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a={lastWeek:"せんしゅうのeeeeのp",yesterday:"きのうのp",today:"きょうのp",tomorrow:"あしたのp",nextWeek:"よくしゅうのeeeeのp",other:"P"};t.default=function(e,t,n,r){return a[e]},e.exports=t.default},14676:function(e,t,a){"use strict";var n=a(15755).default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=n(a(84738));t.default={ordinalNumber:function(e,t){var a=Number(e);switch(String(null==t?void 0:t.unit)){case"year":return"".concat(a,"ねん");case"quarter":return"だい".concat(a,"しはんき");case"month":return"".concat(a,"がつ");case"week":return"だい".concat(a,"しゅう");case"date":return"".concat(a,"にち");case"hour":return"".concat(a,"じ");case"minute":return"".concat(a,"ふん");case"second":return"".concat(a,"びょう");default:return"".concat(a)}},era:(0,r.default)({values:{narrow:["BC","AC"],abbreviated:["きげんぜん","せいれき"],wide:["きげんぜん","せいれき"]},defaultWidth:"wide"}),quarter:(0,r.default)({values:{narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["だい1しはんき","だい2しはんき","だい3しはんき","だい4しはんき"]},defaultWidth:"wide",argumentCallback:function(e){return Number(e)-1}}),month:(0,r.default)({values:{narrow:["1","2","3","4","5","6","7","8","9","10","11","12"],abbreviated:["1がつ","2がつ","3がつ","4がつ","5がつ","6がつ","7がつ","8がつ","9がつ","10がつ","11がつ","12がつ"],wide:["1がつ","2がつ","3がつ","4がつ","5がつ","6がつ","7がつ","8がつ","9がつ","10がつ","11がつ","12がつ"]},defaultWidth:"wide"}),day:(0,r.default)({values:{narrow:["にち","げつ","か","すい","もく","きん","ど"],short:["にち","げつ","か","すい","もく","きん","ど"],abbreviated:["にち","げつ","か","すい","もく","きん","ど"],wide:["にちようび","げつようび","かようび","すいようび","もくようび","きんようび","どようび"]},defaultWidth:"wide"}),dayPeriod:(0,r.default)({values:{narrow:{am:"ごぜん",pm:"ごご",midnight:"しんや",noon:"しょうご",morning:"あさ",afternoon:"ごご",evening:"よる",night:"しんや"},abbreviated:{am:"ごぜん",pm:"ごご",midnight:"しんや",noon:"しょうご",morning:"あさ",afternoon:"ごご",evening:"よる",night:"しんや"},wide:{am:"ごぜん",pm:"ごご",midnight:"しんや",noon:"しょうご",morning:"あさ",afternoon:"ごご",evening:"よる",night:"しんや"}},defaultWidth:"wide",formattingValues:{narrow:{am:"ごぜん",pm:"ごご",midnight:"しんや",noon:"しょうご",morning:"あさ",afternoon:"ごご",evening:"よる",night:"しんや"},abbreviated:{am:"ごぜん",pm:"ごご",midnight:"しんや",noon:"しょうご",morning:"あさ",afternoon:"ごご",evening:"よる",night:"しんや"},wide:{am:"ごぜん",pm:"ごご",midnight:"しんや",noon:"しょうご",morning:"あさ",afternoon:"ごご",evening:"よる",night:"しんや"}},defaultFormattingWidth:"wide"})},e.exports=t.default},91929:function(e,t,a){"use strict";var n=a(15755).default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=n(a(89653));t.default={ordinalNumber:(0,n(a(71604)).default)({matchPattern:/^だ?い?\d+(ねん|しはんき|がつ|しゅう|にち|じ|ふん|びょう)?/i,parsePattern:/\d+/i,valueCallback:function(e){return parseInt(e,10)}}),era:(0,r.default)({matchPatterns:{narrow:/^(B\.?C\.?|A\.?D\.?)/i,abbreviated:/^(きげん[前後]|せいれき)/i,wide:/^(きげん[前後]|せいれき)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^B/i,/^A/i],any:[/^(きげんぜん)/i,/^(せいれき|きげんご)/i]},defaultParseWidth:"any"}),quarter:(0,r.default)({matchPatterns:{narrow:/^[1234]/i,abbreviated:/^Q[1234]/i,wide:/^だい[1234一二三四１２３４]しはんき/i},defaultMatchWidth:"wide",parsePatterns:{any:[/(1|一|１)/i,/(2|二|２)/i,/(3|三|３)/i,/(4|四|４)/i]},defaultParseWidth:"any",valueCallback:function(e){return e+1}}),month:(0,r.default)({matchPatterns:{narrow:/^([123456789]|1[012])/,abbreviated:/^([123456789]|1[012])がつ/i,wide:/^([123456789]|1[012])がつ/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^1\D/,/^2/,/^3/,/^4/,/^5/,/^6/,/^7/,/^8/,/^9/,/^10/,/^11/,/^12/]},defaultParseWidth:"any"}),day:(0,r.default)({matchPatterns:{narrow:/^(にち|げつ|か|すい|もく|きん|ど)/,short:/^(にち|げつ|か|すい|もく|きん|ど)/,abbreviated:/^(にち|げつ|か|すい|もく|きん|ど)/,wide:/^(にち|げつ|か|すい|もく|きん|ど)ようび/},defaultMatchWidth:"wide",parsePatterns:{any:[/^にち/,/^げつ/,/^か/,/^すい/,/^もく/,/^きん/,/^ど/]},defaultParseWidth:"any"}),dayPeriod:(0,r.default)({matchPatterns:{any:/^(AM|PM|ごぜん|ごご|しょうご|しんや|まよなか|よる|あさ)/i},defaultMatchWidth:"any",parsePatterns:{any:{am:/^(A|ごぜん)/i,pm:/^(P|ごご)/i,midnight:/^しんや|まよなか/i,noon:/^しょうご/i,morning:/^あさ/i,afternoon:/^ごご/i,evening:/^よる/i,night:/^しんや/i}},defaultParseWidth:"any"})},e.exports=t.default},46100:function(e,t,a){"use strict";var n=a(15755).default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=n(a(66050)),u=n(a(25773)),i=n(a(41178)),o=n(a(14676)),d=n(a(91929));t.default={code:"ja-Hira",formatDistance:r.default,formatLong:u.default,formatRelative:i.default,localize:o.default,match:d.default,options:{weekStartsOn:0,firstWeekContainsDate:1}},e.exports=t.default},15755:function(e){e.exports=function(e){return e&&e.__esModule?e:{default:e}},e.exports.__esModule=!0,e.exports.default=e.exports}}]);