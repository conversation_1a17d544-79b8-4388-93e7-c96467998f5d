(self.webpackChunk_bull_board_ui=self.webpackChunk_bull_board_ui||[]).push([["92411"],{22348:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){return function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},u=t.width?String(t.width):e.defaultWidth;return e.formats[u]||e.formats[e.defaultWidth]}},e.exports=t.default},61139:function(e,t,u){"use strict";var l=u(15755).default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var d=l(u(22348));t.default={date:(0,d.default)({formats:{full:"EEEE, d. MMMM y",long:"d. MMMM y",medium:"d. MMM y",short:"dd.MM.y"},defaultWidth:"full"}),time:(0,d.default)({formats:{full:"HH:mm:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"HH:mm"},defaultWidth:"full"}),dateTime:(0,d.default)({formats:{full:"{{date}} 'kell' {{time}}",long:"{{date}} 'kell' {{time}}",medium:"{{date}}. {{time}}",short:"{{date}}. {{time}}"},defaultWidth:"full"})},e.exports=t.default},15755:function(e){e.exports=function(e){return e&&e.__esModule?e:{default:e}},e.exports.__esModule=!0,e.exports.default=e.exports}}]);