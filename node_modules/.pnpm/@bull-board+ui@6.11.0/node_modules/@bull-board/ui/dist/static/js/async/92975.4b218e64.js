(self.webpackChunk_bull_board_ui=self.webpackChunk_bull_board_ui||[]).push([["92975"],{1906:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getDefaultOptions=function(){return n},t.setDefaultOptions=function(e){n=e};var n={}},85345:function(e,t,n){"use strict";var a=n(15755).default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t,n){(0,r.default)(2,arguments);var a=(0,o.default)(e,n),u=(0,o.default)(t,n);return a.getTime()===u.getTime()};var r=a(n(26193)),o=a(n(23658));e.exports=t.default},26193:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t){if(t.length<e)throw TypeError(e+" argument"+(e>1?"s":"")+" required, but only "+t.length+" present")},e.exports=t.default},23658:function(e,t,n){"use strict";var a=n(15755).default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t){(0,o.default)(1,arguments);var n,a,l,d,s,f,p,c,m=(0,i.getDefaultOptions)(),v=(0,u.default)(null!=(n=null!=(a=null!=(l=null!=(d=null==t?void 0:t.weekStartsOn)?d:null==t||null==(s=t.locale)||null==(f=s.options)?void 0:f.weekStartsOn)?l:m.weekStartsOn)?a:null==(p=m.locale)||null==(c=p.options)?void 0:c.weekStartsOn)?n:0);if(!(v>=0&&v<=6))throw RangeError("weekStartsOn must be between 0 and 6 inclusively");var h=(0,r.default)(e),b=h.getUTCDay();return h.setUTCDate(h.getUTCDate()-(7*(b<v)+b-v)),h.setUTCHours(0,0,0,0),h};var r=a(n(39276)),o=a(n(26193)),u=a(n(65862)),i=n(1906);e.exports=t.default},65862:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){if(null===e||!0===e||!1===e)return NaN;var t=Number(e);return isNaN(t)?t:t<0?Math.ceil(t):Math.floor(t)},e.exports=t.default},22348:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){return function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=t.width?String(t.width):e.defaultWidth;return e.formats[n]||e.formats[e.defaultWidth]}},e.exports=t.default},84738:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){return function(t,n){var a;if("formatting"===(null!=n&&n.context?String(n.context):"standalone")&&e.formattingValues){var r=e.defaultFormattingWidth||e.defaultWidth,o=null!=n&&n.width?String(n.width):r;a=e.formattingValues[o]||e.formattingValues[r]}else{var u=e.defaultWidth,i=null!=n&&n.width?String(n.width):e.defaultWidth;a=e.values[i]||e.values[u]}return a[e.argumentCallback?e.argumentCallback(t):t]}},e.exports=t.default},89653:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){return function(t){var n,a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=a.width,o=r&&e.matchPatterns[r]||e.matchPatterns[e.defaultMatchWidth],u=t.match(o);if(!u)return null;var i=u[0],l=r&&e.parsePatterns[r]||e.parsePatterns[e.defaultParseWidth],d=Array.isArray(l)?function(e,t){for(var n=0;n<e.length;n++)if(t(e[n]))return n}(l,function(e){return e.test(i)}):function(e,t){for(var n in e)if(e.hasOwnProperty(n)&&t(e[n]))return n}(l,function(e){return e.test(i)});return n=e.valueCallback?e.valueCallback(d):d,{value:n=a.valueCallback?a.valueCallback(n):n,rest:t.slice(i.length)}}},e.exports=t.default},71604:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){return function(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},a=t.match(e.matchPattern);if(!a)return null;var r=a[0],o=t.match(e.parsePattern);if(!o)return null;var u=e.valueCallback?e.valueCallback(o[0]):o[0];return{value:u=n.valueCallback?n.valueCallback(u):u,rest:t.slice(r.length)}}},e.exports=t.default},17293:function(e,t){"use strict";function n(e,t,n){return(1===t&&e.one?e.one:t>=2&&t<=4&&e.twoFour?e.twoFour:e.other)[n].replace("{{count}}",String(t))}function a(e){var t="";return"almost"===e&&(t="takmer"),"about"===e&&(t="približne"),t.length>0?t+" ":""}function r(e){var t="";return"lessThan"===e&&(t="menej než"),"over"===e&&(t="viac než"),t.length>0?t+" ":""}Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o={xSeconds:{one:{present:"sekunda",past:"sekundou",future:"sekundu"},twoFour:{present:"{{count}} sekundy",past:"{{count}} sekundami",future:"{{count}} sekundy"},other:{present:"{{count}} sek\xfand",past:"{{count}} sekundami",future:"{{count}} sek\xfand"}},halfAMinute:{other:{present:"pol min\xfaty",past:"pol min\xfatou",future:"pol min\xfaty"}},xMinutes:{one:{present:"min\xfata",past:"min\xfatou",future:"min\xfatu"},twoFour:{present:"{{count}} min\xfaty",past:"{{count}} min\xfatami",future:"{{count}} min\xfaty"},other:{present:"{{count}} min\xfat",past:"{{count}} min\xfatami",future:"{{count}} min\xfat"}},xHours:{one:{present:"hodina",past:"hodinou",future:"hodinu"},twoFour:{present:"{{count}} hodiny",past:"{{count}} hodinami",future:"{{count}} hodiny"},other:{present:"{{count}} hod\xedn",past:"{{count}} hodinami",future:"{{count}} hod\xedn"}},xDays:{one:{present:"deň",past:"dňom",future:"deň"},twoFour:{present:"{{count}} dni",past:"{{count}} dňami",future:"{{count}} dni"},other:{present:"{{count}} dn\xed",past:"{{count}} dňami",future:"{{count}} dn\xed"}},xWeeks:{one:{present:"t\xfdždeň",past:"t\xfdždňom",future:"t\xfdždeň"},twoFour:{present:"{{count}} t\xfdždne",past:"{{count}} t\xfdždňami",future:"{{count}} t\xfdždne"},other:{present:"{{count}} t\xfdždňov",past:"{{count}} t\xfdždňami",future:"{{count}} t\xfdždňov"}},xMonths:{one:{present:"mesiac",past:"mesiacom",future:"mesiac"},twoFour:{present:"{{count}} mesiace",past:"{{count}} mesiacmi",future:"{{count}} mesiace"},other:{present:"{{count}} mesiacov",past:"{{count}} mesiacmi",future:"{{count}} mesiacov"}},xYears:{one:{present:"rok",past:"rokom",future:"rok"},twoFour:{present:"{{count}} roky",past:"{{count}} rokmi",future:"{{count}} roky"},other:{present:"{{count}} rokov",past:"{{count}} rokmi",future:"{{count}} rokov"}}};t.default=function(e,t,u){var i,l=["lessThan","about","over","almost"].filter(function(t){return!!e.match(RegExp("^"+t))})[0]||"",d=o[(i=e.substring(l.length)).charAt(0).toLowerCase()+i.slice(1)];return null!=u&&u.addSuffix?u.comparison&&u.comparison>0?a(l)+"o "+r(l)+n(d,t,"future"):a(l)+"pred "+r(l)+n(d,t,"past"):a(l)+r(l)+n(d,t,"present")},e.exports=t.default},73687:function(e,t,n){"use strict";var a=n(15755).default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=a(n(22348));t.default={date:(0,r.default)({formats:{full:"EEEE d. MMMM y",long:"d. MMMM y",medium:"d. M. y",short:"d. M. y"},defaultWidth:"full"}),time:(0,r.default)({formats:{full:"H:mm:ss zzzz",long:"H:mm:ss z",medium:"H:mm:ss",short:"H:mm"},defaultWidth:"full"}),dateTime:(0,r.default)({formats:{full:"{{date}}, {{time}}",long:"{{date}}, {{time}}",medium:"{{date}}, {{time}}",short:"{{date}} {{time}}"},defaultWidth:"full"})},e.exports=t.default},21778:function(e,t,n){"use strict";var a=n(15755).default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=a(n(85345)),o=["nedeľu","pondelok","utorok","stredu","štvrtok","piatok","sobotu"];function u(e){var t=o[e];return 4===e?"'vo' eeee 'o' p":"'v "+t+" o' p"}var i={lastWeek:function(e,t,n){var a=e.getUTCDay();if((0,r.default)(e,t,n))return u(a);var i=o[a];switch(a){case 0:case 3:case 6:return"'minul\xfa "+i+" o' p";default:return"'minul\xfd' eeee 'o' p"}},yesterday:"'včera o' p",today:"'dnes o' p",tomorrow:"'zajtra o' p",nextWeek:function(e,t,n){var a=e.getUTCDay();if((0,r.default)(e,t,n))return u(a);var i=o[a];switch(a){case 0:case 4:case 6:return"'bud\xfacu "+i+" o' p";default:return"'bud\xfaci' eeee 'o' p"}},other:"P"};t.default=function(e,t,n,a){var r=i[e];return"function"==typeof r?r(t,n,a):r},e.exports=t.default},12134:function(e,t,n){"use strict";var a=n(15755).default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=a(n(84738));t.default={ordinalNumber:function(e,t){return Number(e)+"."},era:(0,r.default)({values:{narrow:["pred Kr.","po Kr."],abbreviated:["pred Kr.","po Kr."],wide:["pred Kristom","po Kristovi"]},defaultWidth:"wide"}),quarter:(0,r.default)({values:{narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1. štvrťrok","2. štvrťrok","3. štvrťrok","4. štvrťrok"]},defaultWidth:"wide",argumentCallback:function(e){return e-1}}),month:(0,r.default)({values:{narrow:["j","f","m","a","m","j","j","a","s","o","n","d"],abbreviated:["jan","feb","mar","apr","m\xe1j","j\xfan","j\xfal","aug","sep","okt","nov","dec"],wide:["janu\xe1r","febru\xe1r","marec","apr\xedl","m\xe1j","j\xfan","j\xfal","august","september","okt\xf3ber","november","december"]},defaultWidth:"wide",formattingValues:{narrow:["j","f","m","a","m","j","j","a","s","o","n","d"],abbreviated:["jan","feb","mar","apr","m\xe1j","j\xfan","j\xfal","aug","sep","okt","nov","dec"],wide:["janu\xe1ra","febru\xe1ra","marca","apr\xedla","m\xe1ja","j\xfana","j\xfala","augusta","septembra","okt\xf3bra","novembra","decembra"]},defaultFormattingWidth:"wide"}),day:(0,r.default)({values:{narrow:["n","p","u","s","š","p","s"],short:["ne","po","ut","st","št","pi","so"],abbreviated:["ne","po","ut","st","št","pi","so"],wide:["nedeľa","pondelok","utorok","streda","štvrtok","piatok","sobota"]},defaultWidth:"wide"}),dayPeriod:(0,r.default)({values:{narrow:{am:"AM",pm:"PM",midnight:"poln.",noon:"pol.",morning:"r\xe1no",afternoon:"pop.",evening:"več.",night:"noc"},abbreviated:{am:"AM",pm:"PM",midnight:"poln.",noon:"pol.",morning:"r\xe1no",afternoon:"popol.",evening:"večer",night:"noc"},wide:{am:"AM",pm:"PM",midnight:"polnoc",noon:"poludnie",morning:"r\xe1no",afternoon:"popoludnie",evening:"večer",night:"noc"}},defaultWidth:"wide",formattingValues:{narrow:{am:"AM",pm:"PM",midnight:"o poln.",noon:"nap.",morning:"r\xe1no",afternoon:"pop.",evening:"več.",night:"v n."},abbreviated:{am:"AM",pm:"PM",midnight:"o poln.",noon:"napol.",morning:"r\xe1no",afternoon:"popol.",evening:"večer",night:"v noci"},wide:{am:"AM",pm:"PM",midnight:"o polnoci",noon:"napoludnie",morning:"r\xe1no",afternoon:"popoludn\xed",evening:"večer",night:"v noci"}},defaultFormattingWidth:"wide"})},e.exports=t.default},76263:function(e,t,n){"use strict";var a=n(15755).default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=a(n(89653));t.default={ordinalNumber:(0,a(n(71604)).default)({matchPattern:/^(\d+)\.?/i,parsePattern:/\d+/i,valueCallback:function(e){return parseInt(e,10)}}),era:(0,r.default)({matchPatterns:{narrow:/^(pred Kr\.|pred n\. l\.|po Kr\.|n\. l\.)/i,abbreviated:/^(pred Kr\.|pred n\. l\.|po Kr\.|n\. l\.)/i,wide:/^(pred Kristom|pred na[šs][íi]m letopo[čc]tom|po Kristovi|n[áa][šs]ho letopo[čc]tu)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^pr/i,/^(po|n)/i]},defaultParseWidth:"any"}),quarter:(0,r.default)({matchPatterns:{narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234]\. [šs]tvr[ťt]rok/i},defaultMatchWidth:"wide",parsePatterns:{any:[/1/i,/2/i,/3/i,/4/i]},defaultParseWidth:"any",valueCallback:function(e){return e+1}}),month:(0,r.default)({matchPatterns:{narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|m[áa]j|j[úu]n|j[úu]l|aug|sep|okt|nov|dec)/i,wide:/^(janu[áa]ra?|febru[áa]ra?|(marec|marca)|apr[íi]la?|m[áa]ja?|j[úu]na?|j[úu]la?|augusta?|(september|septembra)|(okt[óo]ber|okt[óo]bra)|(november|novembra)|(december|decembra))/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^m[áa]j/i,/^j[úu]n/i,/^j[úu]l/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},defaultParseWidth:"any"}),day:(0,r.default)({matchPatterns:{narrow:/^[npusšp]/i,short:/^(ne|po|ut|st|št|pi|so)/i,abbreviated:/^(ne|po|ut|st|št|pi|so)/i,wide:/^(nede[ľl]a|pondelok|utorok|streda|[šs]tvrtok|piatok|sobota])/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^n/i,/^p/i,/^u/i,/^s/i,/^š/i,/^p/i,/^s/i],any:[/^n/i,/^po/i,/^u/i,/^st/i,/^(št|stv)/i,/^pi/i,/^so/i]},defaultParseWidth:"any"}),dayPeriod:(0,r.default)({matchPatterns:{narrow:/^(am|pm|(o )?poln\.?|(nap\.?|pol\.?)|r[áa]no|pop\.?|ve[čc]\.?|(v n\.?|noc))/i,abbreviated:/^(am|pm|(o )?poln\.?|(napol\.?|pol\.?)|r[áa]no|pop\.?|ve[čc]er|(v )?noci?)/i,any:/^(am|pm|(o )?polnoci?|(na)?poludnie|r[áa]no|popoludn(ie|í|i)|ve[čc]er|(v )?noci?)/i},defaultMatchWidth:"any",parsePatterns:{any:{am:/^am/i,pm:/^pm/i,midnight:/poln/i,noon:/^(nap|(na)?pol(\.|u))/i,morning:/^r[áa]no/i,afternoon:/^pop/i,evening:/^ve[čc]/i,night:/^(noc|v n\.)/i}},defaultParseWidth:"any"})},e.exports=t.default},17341:function(e,t,n){"use strict";var a=n(15755).default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=a(n(17293)),o=a(n(73687)),u=a(n(21778)),i=a(n(12134)),l=a(n(76263));t.default={code:"sk",formatDistance:r.default,formatLong:o.default,formatRelative:u.default,localize:i.default,match:l.default,options:{weekStartsOn:1,firstWeekContainsDate:4}},e.exports=t.default},39276:function(e,t,n){"use strict";var a=n(15755).default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){(0,o.default)(1,arguments);var t=Object.prototype.toString.call(e);return e instanceof Date||"object"===(0,r.default)(e)&&"[object Date]"===t?new Date(e.getTime()):"number"==typeof e||"[object Number]"===t?new Date(e):(("string"==typeof e||"[object String]"===t)&&"undefined"!=typeof console&&(console.warn("Starting with v2.0.0-beta.1 date-fns doesn't accept strings as date arguments. Please use `parseISO` to parse strings. See: https://github.com/date-fns/date-fns/blob/master/docs/upgradeGuide.md#string-arguments"),console.warn(Error().stack)),new Date(NaN))};var r=a(n(69430)),o=a(n(26193));e.exports=t.default},15755:function(e){e.exports=function(e){return e&&e.__esModule?e:{default:e}},e.exports.__esModule=!0,e.exports.default=e.exports},69430:function(e){function t(n){return e.exports=t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e.exports.__esModule=!0,e.exports.default=e.exports,t(n)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports}}]);