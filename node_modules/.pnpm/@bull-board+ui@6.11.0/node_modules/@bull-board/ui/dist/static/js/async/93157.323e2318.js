(self.webpackChunk_bull_board_ui=self.webpackChunk_bull_board_ui||[]).push([["93157"],{84738:function(a,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(a){return function(t,e){var i;if("formatting"===(null!=e&&e.context?String(e.context):"standalone")&&a.formattingValues){var u=a.defaultFormattingWidth||a.defaultWidth,n=null!=e&&e.width?String(e.width):u;i=a.formattingValues[n]||a.formattingValues[u]}else{var l=a.defaultWidth,r=null!=e&&e.width?String(e.width):a.defaultWidth;i=a.values[r]||a.values[l]}return i[a.argumentCallback?a.argumentCallback(t):t]}},a.exports=t.default},81501:function(a,t,e){"use strict";var i=e(15755).default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var u=i(e(84738)),n={narrow:["T","H","M","H","T","K","H","E","S","L","M","J"],abbreviated:["tammi","helmi","maalis","huhti","touko","kes\xe4","hein\xe4","elo","syys","loka","marras","joulu"],wide:["tammikuu","helmikuu","maaliskuu","huhtikuu","toukokuu","kes\xe4kuu","hein\xe4kuu","elokuu","syyskuu","lokakuu","marraskuu","joulukuu"]},l={narrow:n.narrow,abbreviated:n.abbreviated,wide:["tammikuuta","helmikuuta","maaliskuuta","huhtikuuta","toukokuuta","kes\xe4kuuta","hein\xe4kuuta","elokuuta","syyskuuta","lokakuuta","marraskuuta","joulukuuta"]},r={narrow:["S","M","T","K","T","P","L"],short:["su","ma","ti","ke","to","pe","la"],abbreviated:["sunn.","maan.","tiis.","kesk.","torst.","perj.","la"],wide:["sunnuntai","maanantai","tiistai","keskiviikko","torstai","perjantai","lauantai"]},o={narrow:r.narrow,short:r.short,abbreviated:r.abbreviated,wide:["sunnuntaina","maanantaina","tiistaina","keskiviikkona","torstaina","perjantaina","lauantaina"]};t.default={ordinalNumber:function(a,t){return Number(a)+"."},era:(0,u.default)({values:{narrow:["eaa.","jaa."],abbreviated:["eaa.","jaa."],wide:["ennen ajanlaskun alkua","j\xe4lkeen ajanlaskun alun"]},defaultWidth:"wide"}),quarter:(0,u.default)({values:{narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1. kvartaali","2. kvartaali","3. kvartaali","4. kvartaali"]},defaultWidth:"wide",argumentCallback:function(a){return a-1}}),month:(0,u.default)({values:n,defaultWidth:"wide",formattingValues:l,defaultFormattingWidth:"wide"}),day:(0,u.default)({values:r,defaultWidth:"wide",formattingValues:o,defaultFormattingWidth:"wide"}),dayPeriod:(0,u.default)({values:{narrow:{am:"ap",pm:"ip",midnight:"keskiy\xf6",noon:"keskip\xe4iv\xe4",morning:"ap",afternoon:"ip",evening:"illalla",night:"y\xf6ll\xe4"},abbreviated:{am:"ap",pm:"ip",midnight:"keskiy\xf6",noon:"keskip\xe4iv\xe4",morning:"ap",afternoon:"ip",evening:"illalla",night:"y\xf6ll\xe4"},wide:{am:"ap",pm:"ip",midnight:"keskiy\xf6ll\xe4",noon:"keskip\xe4iv\xe4ll\xe4",morning:"aamup\xe4iv\xe4ll\xe4",afternoon:"iltap\xe4iv\xe4ll\xe4",evening:"illalla",night:"y\xf6ll\xe4"}},defaultWidth:"wide"})},a.exports=t.default},15755:function(a){a.exports=function(a){return a&&a.__esModule?a:{default:a}},a.exports.__esModule=!0,a.exports.default=a.exports}}]);