(self.webpackChunk_bull_board_ui=self.webpackChunk_bull_board_ui||[]).push([["93929"],{89653:function(t,a){"use strict";Object.defineProperty(a,"__esModule",{value:!0}),a.default=function(t){return function(a){var e,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=r.width,i=n&&t.matchPatterns[n]||t.matchPatterns[t.defaultMatchWidth],u=a.match(i);if(!u)return null;var l=u[0],d=n&&t.parsePatterns[n]||t.parsePatterns[t.defaultParseWidth],s=Array.isArray(d)?function(t,a){for(var e=0;e<t.length;e++)if(a(t[e]))return e}(d,function(t){return t.test(l)}):function(t,a){for(var e in t)if(t.hasOwnProperty(e)&&a(t[e]))return e}(d,function(t){return t.test(l)});return e=t.valueCallback?t.valueCallback(s):s,{value:e=r.valueCallback?r.valueCallback(e):e,rest:a.slice(l.length)}}},t.exports=a.default},71604:function(t,a){"use strict";Object.defineProperty(a,"__esModule",{value:!0}),a.default=function(t){return function(a){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=a.match(t.matchPattern);if(!r)return null;var n=r[0],i=a.match(t.parsePattern);if(!i)return null;var u=t.valueCallback?t.valueCallback(i[0]):i[0];return{value:u=e.valueCallback?e.valueCallback(u):u,rest:a.slice(n.length)}}},t.exports=a.default},91929:function(t,a,e){"use strict";var r=e(15755).default;Object.defineProperty(a,"__esModule",{value:!0}),a.default=void 0;var n=r(e(89653));a.default={ordinalNumber:(0,r(e(71604)).default)({matchPattern:/^だ?い?\d+(ねん|しはんき|がつ|しゅう|にち|じ|ふん|びょう)?/i,parsePattern:/\d+/i,valueCallback:function(t){return parseInt(t,10)}}),era:(0,n.default)({matchPatterns:{narrow:/^(B\.?C\.?|A\.?D\.?)/i,abbreviated:/^(きげん[前後]|せいれき)/i,wide:/^(きげん[前後]|せいれき)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^B/i,/^A/i],any:[/^(きげんぜん)/i,/^(せいれき|きげんご)/i]},defaultParseWidth:"any"}),quarter:(0,n.default)({matchPatterns:{narrow:/^[1234]/i,abbreviated:/^Q[1234]/i,wide:/^だい[1234一二三四１２３４]しはんき/i},defaultMatchWidth:"wide",parsePatterns:{any:[/(1|一|１)/i,/(2|二|２)/i,/(3|三|３)/i,/(4|四|４)/i]},defaultParseWidth:"any",valueCallback:function(t){return t+1}}),month:(0,n.default)({matchPatterns:{narrow:/^([123456789]|1[012])/,abbreviated:/^([123456789]|1[012])がつ/i,wide:/^([123456789]|1[012])がつ/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^1\D/,/^2/,/^3/,/^4/,/^5/,/^6/,/^7/,/^8/,/^9/,/^10/,/^11/,/^12/]},defaultParseWidth:"any"}),day:(0,n.default)({matchPatterns:{narrow:/^(にち|げつ|か|すい|もく|きん|ど)/,short:/^(にち|げつ|か|すい|もく|きん|ど)/,abbreviated:/^(にち|げつ|か|すい|もく|きん|ど)/,wide:/^(にち|げつ|か|すい|もく|きん|ど)ようび/},defaultMatchWidth:"wide",parsePatterns:{any:[/^にち/,/^げつ/,/^か/,/^すい/,/^もく/,/^きん/,/^ど/]},defaultParseWidth:"any"}),dayPeriod:(0,n.default)({matchPatterns:{any:/^(AM|PM|ごぜん|ごご|しょうご|しんや|まよなか|よる|あさ)/i},defaultMatchWidth:"any",parsePatterns:{any:{am:/^(A|ごぜん)/i,pm:/^(P|ごご)/i,midnight:/^しんや|まよなか/i,noon:/^しょうご/i,morning:/^あさ/i,afternoon:/^ごご/i,evening:/^よる/i,night:/^しんや/i}},defaultParseWidth:"any"})},t.exports=a.default},15755:function(t){t.exports=function(t){return t&&t.__esModule?t:{default:t}},t.exports.__esModule=!0,t.exports.default=t.exports}}]);