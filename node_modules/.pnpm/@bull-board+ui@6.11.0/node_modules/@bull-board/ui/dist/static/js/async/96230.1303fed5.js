(self.webpackChunk_bull_board_ui=self.webpackChunk_bull_board_ui||[]).push([["96230"],{89653:function(i,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(i){return function(t){var a,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=e.width,n=r&&i.matchPatterns[r]||i.matchPatterns[i.defaultMatchWidth],u=t.match(n);if(!u)return null;var l=u[0],d=r&&i.parsePatterns[r]||i.parsePatterns[i.defaultParseWidth],s=Array.isArray(d)?function(i,t){for(var a=0;a<i.length;a++)if(t(i[a]))return a}(d,function(i){return i.test(l)}):function(i,t){for(var a in i)if(i.hasOwnProperty(a)&&t(i[a]))return a}(d,function(i){return i.test(l)});return a=i.valueCallback?i.valueCallback(s):s,{value:a=e.valueCallback?e.valueCallback(a):a,rest:t.slice(l.length)}}},i.exports=t.default},71604:function(i,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(i){return function(t){var a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},e=t.match(i.matchPattern);if(!e)return null;var r=e[0],n=t.match(i.parsePattern);if(!n)return null;var u=i.valueCallback?i.valueCallback(n[0]):n[0];return{value:u=a.valueCallback?a.valueCallback(u):u,rest:t.slice(r.length)}}},i.exports=t.default},17332:function(i,t,a){"use strict";var e=a(15755).default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=e(a(89653));t.default={ordinalNumber:(0,e(a(71604)).default)({matchPattern:/\d+/i,parsePattern:/\d+/i,valueCallback:function(i){return parseInt(i,10)}}),era:(0,r.default)({matchPatterns:{narrow:/^(нтө|нт)/i,abbreviated:/^(нтө|нт)/i,wide:/^(нийтийн тооллын өмнө|нийтийн тооллын)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^(нтө|нийтийн тооллын өмнө)/i,/^(нт|нийтийн тооллын)/i]},defaultParseWidth:"any"}),quarter:(0,r.default)({matchPatterns:{narrow:/^(iv|iii|ii|i)/i,abbreviated:/^(iv|iii|ii|i) улирал/i,wide:/^[1-4]-р улирал/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^(i(\s|$)|1)/i,/^(ii(\s|$)|2)/i,/^(iii(\s|$)|3)/i,/^(iv(\s|$)|4)/i]},defaultParseWidth:"any",valueCallback:function(i){return i+1}}),month:(0,r.default)({matchPatterns:{narrow:/^(xii|xi|x|ix|viii|vii|vi|v|iv|iii|ii|i)/i,abbreviated:/^(1-р сар|2-р сар|3-р сар|4-р сар|5-р сар|6-р сар|7-р сар|8-р сар|9-р сар|10-р сар|11-р сар|12-р сар)/i,wide:/^(нэгдүгээр сар|хоёрдугаар сар|гуравдугаар сар|дөрөвдүгээр сар|тавдугаар сар|зургаадугаар сар|долоодугаар сар|наймдугаар сар|есдүгээр сар|аравдугаар сар|арван нэгдүгээр сар|арван хоёрдугаар сар)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^i$/i,/^ii$/i,/^iii$/i,/^iv$/i,/^v$/i,/^vi$/i,/^vii$/i,/^viii$/i,/^ix$/i,/^x$/i,/^xi$/i,/^xii$/i],any:[/^(1|нэгдүгээр)/i,/^(2|хоёрдугаар)/i,/^(3|гуравдугаар)/i,/^(4|дөрөвдүгээр)/i,/^(5|тавдугаар)/i,/^(6|зургаадугаар)/i,/^(7|долоодугаар)/i,/^(8|наймдугаар)/i,/^(9|есдүгээр)/i,/^(10|аравдугаар)/i,/^(11|арван нэгдүгээр)/i,/^(12|арван хоёрдугаар)/i]},defaultParseWidth:"any"}),day:(0,r.default)({matchPatterns:{narrow:/^[ндмлпбб]/i,short:/^(ня|да|мя|лх|пү|ба|бя)/i,abbreviated:/^(ням|дав|мяг|лха|пүр|баа|бям)/i,wide:/^(ням|даваа|мягмар|лхагва|пүрэв|баасан|бямба)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^н/i,/^д/i,/^м/i,/^л/i,/^п/i,/^б/i,/^б/i],any:[/^ня/i,/^да/i,/^мя/i,/^лх/i,/^пү/i,/^ба/i,/^бя/i]},defaultParseWidth:"any"}),dayPeriod:(0,r.default)({matchPatterns:{narrow:/^(ү\.ө\.|ү\.х\.|шөнө дунд|үд дунд|өглөө|өдөр|орой|шөнө)/i,any:/^(ү\.ө\.|ү\.х\.|шөнө дунд|үд дунд|өглөө|өдөр|орой|шөнө)/i},defaultMatchWidth:"any",parsePatterns:{any:{am:/^ү\.ө\./i,pm:/^ү\.х\./i,midnight:/^шөнө дунд/i,noon:/^үд дунд/i,morning:/өглөө/i,afternoon:/өдөр/i,evening:/орой/i,night:/шөнө/i}},defaultParseWidth:"any"})},i.exports=t.default},15755:function(i){i.exports=function(i){return i&&i.__esModule?i:{default:i}},i.exports.__esModule=!0,i.exports.default=i.exports}}]);