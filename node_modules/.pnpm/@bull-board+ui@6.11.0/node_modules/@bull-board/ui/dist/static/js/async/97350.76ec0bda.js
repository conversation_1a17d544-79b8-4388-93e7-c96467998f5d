"use strict";(self.webpackChunk_bull_board_ui=self.webpackChunk_bull_board_ui||[]).push([["97350"],{66050:function(o,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var t={lessThanXSeconds:{one:"1びょうみまん",other:"{{count}}びょうみまん",oneWithSuffix:"やく1びょう",otherWithSuffix:"やく{{count}}びょう"},xSeconds:{one:"1びょう",other:"{{count}}びょう"},halfAMinute:"30びょう",lessThanXMinutes:{one:"1ぷんみまん",other:"{{count}}ふんみまん",oneWithSuffix:"やく1ぷん",otherWithSuffix:"やく{{count}}ふん"},xMinutes:{one:"1ぷん",other:"{{count}}ふん"},aboutXHours:{one:"やく1じかん",other:"やく{{count}}じかん"},xHours:{one:"1じかん",other:"{{count}}じかん"},xDays:{one:"1にち",other:"{{count}}にち"},aboutXWeeks:{one:"やく1しゅうかん",other:"やく{{count}}しゅうかん"},xWeeks:{one:"1しゅうかん",other:"{{count}}しゅうかん"},aboutXMonths:{one:"やく1かげつ",other:"やく{{count}}かげつ"},xMonths:{one:"1かげつ",other:"{{count}}かげつ"},aboutXYears:{one:"やく1ねん",other:"やく{{count}}ねん"},xYears:{one:"1ねん",other:"{{count}}ねん"},overXYears:{one:"1ねんいじょう",other:"{{count}}ねんいじょう"},almostXYears:{one:"1ねんちかく",other:"{{count}}ねんちかく"}};e.default=function(o,e,n){n=n||{};var u,r=t[o];if(u="string"==typeof r?r:1===e?n.addSuffix&&r.oneWithSuffix?r.oneWithSuffix:r.one:n.addSuffix&&r.otherWithSuffix?r.otherWithSuffix.replace("{{count}}",String(e)):r.other.replace("{{count}}",String(e)),n.addSuffix)if(n.comparison&&n.comparison>0)return u+"あと";else return u+"まえ";return u},o.exports=e.default}}]);