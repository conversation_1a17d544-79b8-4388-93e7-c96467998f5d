(self.webpackChunk_bull_board_ui=self.webpackChunk_bull_board_ui||[]).push([["97984"],{1906:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getDefaultOptions=function(){return n},t.setDefaultOptions=function(e){n=e};var n={}},85345:function(e,t,n){"use strict";var o=n(15755).default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t,n){(0,r.default)(2,arguments);var o=(0,u.default)(e,n),a=(0,u.default)(t,n);return o.getTime()===a.getTime()};var r=o(n(26193)),u=o(n(23658));e.exports=t.default},26193:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t){if(t.length<e)throw TypeError(e+" argument"+(e>1?"s":"")+" required, but only "+t.length+" present")},e.exports=t.default},23658:function(e,t,n){"use strict";var o=n(15755).default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t){(0,u.default)(1,arguments);var n,o,s,i,f,c,d,p,b=(0,l.getDefaultOptions)(),m=(0,a.default)(null!=(n=null!=(o=null!=(s=null!=(i=null==t?void 0:t.weekStartsOn)?i:null==t||null==(f=t.locale)||null==(c=f.options)?void 0:c.weekStartsOn)?s:b.weekStartsOn)?o:null==(d=b.locale)||null==(p=d.options)?void 0:p.weekStartsOn)?n:0);if(!(m>=0&&m<=6))throw RangeError("weekStartsOn must be between 0 and 6 inclusively");var y=(0,r.default)(e),v=y.getUTCDay();return y.setUTCDate(y.getUTCDate()-(7*(v<m)+v-m)),y.setUTCHours(0,0,0,0),y};var r=o(n(39276)),u=o(n(26193)),a=o(n(65862)),l=n(1906);e.exports=t.default},65862:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){if(null===e||!0===e||!1===e)return NaN;var t=Number(e);return isNaN(t)?t:t<0?Math.ceil(t):Math.floor(t)},e.exports=t.default},91531:function(e,t,n){"use strict";var o=n(15755).default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=o(n(85345)),u={masculine:"ostatni",feminine:"ostatnia"},a={masculine:"ten",feminine:"ta"},l={masculine:"następny",feminine:"następna"},s={0:"feminine",1:"masculine",2:"masculine",3:"feminine",4:"masculine",5:"masculine",6:"feminine"};function i(e,t,n,o){if((0,r.default)(t,n,o))i=a;else if("lastWeek"===e)i=u;else if("nextWeek"===e)i=l;else throw Error("Cannot determine adjectives for token ".concat(e));var i,f=i[s[t.getUTCDay()]];return"'".concat(f,"' eeee 'o' p")}var f={lastWeek:i,yesterday:"'wczoraj o' p",today:"'dzisiaj o' p",tomorrow:"'jutro o' p",nextWeek:i,other:"P"};t.default=function(e,t,n,o){var r=f[e];return"function"==typeof r?r(e,t,n,o):r},e.exports=t.default},39276:function(e,t,n){"use strict";var o=n(15755).default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){(0,u.default)(1,arguments);var t=Object.prototype.toString.call(e);return e instanceof Date||"object"===(0,r.default)(e)&&"[object Date]"===t?new Date(e.getTime()):"number"==typeof e||"[object Number]"===t?new Date(e):(("string"==typeof e||"[object String]"===t)&&"undefined"!=typeof console&&(console.warn("Starting with v2.0.0-beta.1 date-fns doesn't accept strings as date arguments. Please use `parseISO` to parse strings. See: https://github.com/date-fns/date-fns/blob/master/docs/upgradeGuide.md#string-arguments"),console.warn(Error().stack)),new Date(NaN))};var r=o(n(69430)),u=o(n(26193));e.exports=t.default},15755:function(e){e.exports=function(e){return e&&e.__esModule?e:{default:e}},e.exports.__esModule=!0,e.exports.default=e.exports},69430:function(e){function t(n){return e.exports=t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e.exports.__esModule=!0,e.exports.default=e.exports,t(n)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports}}]);