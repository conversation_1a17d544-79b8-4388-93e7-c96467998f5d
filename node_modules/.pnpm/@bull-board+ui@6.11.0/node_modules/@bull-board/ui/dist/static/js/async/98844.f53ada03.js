(self.webpackChunk_bull_board_ui=self.webpackChunk_bull_board_ui||[]).push([["98844"],{89653:function(a,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(a){return function(t){var e,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=i.width,n=r&&a.matchPatterns[r]||a.matchPatterns[a.defaultMatchWidth],u=t.match(n);if(!u)return null;var l=u[0],d=r&&a.parsePatterns[r]||a.parsePatterns[a.defaultParseWidth],s=Array.isArray(d)?function(a,t){for(var e=0;e<a.length;e++)if(t(a[e]))return e}(d,function(a){return a.test(l)}):function(a,t){for(var e in a)if(a.hasOwnProperty(e)&&t(a[e]))return e}(d,function(a){return a.test(l)});return e=a.valueCallback?a.valueCallback(s):s,{value:e=i.valueCallback?i.valueCallback(e):e,rest:t.slice(l.length)}}},a.exports=t.default},71604:function(a,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(a){return function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=t.match(a.matchPattern);if(!i)return null;var r=i[0],n=t.match(a.parsePattern);if(!n)return null;var u=a.valueCallback?a.valueCallback(n[0]):n[0];return{value:u=e.valueCallback?e.valueCallback(u):u,rest:t.slice(r.length)}}},a.exports=t.default},95834:function(a,t,e){"use strict";var i=e(15755).default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=i(e(89653)),n=i(e(71604)),u=["רא","שנ","של","רב","ח","שי","שב","שמ","ת","ע"];t.default={ordinalNumber:(0,n.default)({matchPattern:/^(\d+|(ראשון|שני|שלישי|רביעי|חמישי|שישי|שביעי|שמיני|תשיעי|עשירי|ראשונה|שנייה|שלישית|רביעית|חמישית|שישית|שביעית|שמינית|תשיעית|עשירית))/i,parsePattern:/^(\d+|רא|שנ|של|רב|ח|שי|שב|שמ|ת|ע)/i,valueCallback:function(a){var t=parseInt(a,10);return isNaN(t)?u.indexOf(a)+1:t}}),era:(0,r.default)({matchPatterns:{narrow:/^ל(ספירה|פנה״ס)/i,abbreviated:/^ל(ספירה|פנה״ס)/i,wide:/^ל(פני ה)?ספירה/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^לפ/i,/^לס/i]},defaultParseWidth:"any"}),quarter:(0,r.default)({matchPatterns:{narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^רבעון [1234]/i},defaultMatchWidth:"wide",parsePatterns:{any:[/1/i,/2/i,/3/i,/4/i]},defaultParseWidth:"any",valueCallback:function(a){return a+1}}),month:(0,r.default)({matchPatterns:{narrow:/^\d+/i,abbreviated:/^(ינו|פבר|מרץ|אפר|מאי|יוני|יולי|אוג|ספט|אוק|נוב|דצמ)׳?/i,wide:/^(ינואר|פברואר|מרץ|אפריל|מאי|יוני|יולי|אוגוסט|ספטמבר|אוקטובר|נובמבר|דצמבר)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^1$/i,/^2/i,/^3/i,/^4/i,/^5/i,/^6/i,/^7/i,/^8/i,/^9/i,/^10/i,/^11/i,/^12/i],any:[/^ינ/i,/^פ/i,/^מר/i,/^אפ/i,/^מא/i,/^יונ/i,/^יול/i,/^אוג/i,/^ס/i,/^אוק/i,/^נ/i,/^ד/i]},defaultParseWidth:"any"}),day:(0,r.default)({matchPatterns:{narrow:/^[אבגדהוש]׳/i,short:/^[אבגדהוש]׳/i,abbreviated:/^(שבת|יום (א|ב|ג|ד|ה|ו)׳)/i,wide:/^יום (ראשון|שני|שלישי|רביעי|חמישי|שישי|שבת)/i},defaultMatchWidth:"wide",parsePatterns:{abbreviated:[/א׳$/i,/ב׳$/i,/ג׳$/i,/ד׳$/i,/ה׳$/i,/ו׳$/i,/^ש/i],wide:[/ן$/i,/ני$/i,/לישי$/i,/עי$/i,/מישי$/i,/שישי$/i,/ת$/i],any:[/^א/i,/^ב/i,/^ג/i,/^ד/i,/^ה/i,/^ו/i,/^ש/i]},defaultParseWidth:"any"}),dayPeriod:(0,r.default)({matchPatterns:{any:/^(אחר ה|ב)?(חצות|צהריים|בוקר|ערב|לילה|אחה״צ|לפנה״צ)/i},defaultMatchWidth:"any",parsePatterns:{any:{am:/^לפ/i,pm:/^אחה/i,midnight:/^ח/i,noon:/^צ/i,morning:/בוקר/i,afternoon:/בצ|אחר/i,evening:/ערב/i,night:/לילה/i}},defaultParseWidth:"any"})},a.exports=t.default},15755:function(a){a.exports=function(a){return a&&a.__esModule?a:{default:a}},a.exports.__esModule=!0,a.exports.default=a.exports}}]);