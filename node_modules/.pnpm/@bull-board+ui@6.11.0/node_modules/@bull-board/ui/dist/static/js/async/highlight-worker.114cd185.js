(()=>{"use strict";var e={12445:function(e,r,t){var n=t(19294),a=t(49436);n.Z.registerLanguage("json",a.Z),n.Z.registerLanguage("stacktrace",function(){let e={className:"number",begin:":\\d+:\\d+",relevance:5};return{case_insensitive:!0,contains:[{className:"type",begin:/^\w*Error:\s*/,relevance:40,contains:[{className:"title",begin:/.*/,end:/$/,excludeStart:!0,endsWithParent:!0}]},{className:"trace-line",begin:/^\s*at/,end:/$/,keywords:"at as async prototype anonymous function",contains:[{className:"code-path",begin:/\(/,end:/\)$/,excludeEnd:!0,excludeBegin:!0,contains:[e]}]},e]}});let s=n.Z;self.onmessage=e=>{let{data:r={}}=e,{id:t="",code:n="",language:a=""}=r;if(!t||!n||!a)return;let i=s.highlightAuto(n,[a]);self.postMessage({code:i.value,id:t})}}},r={};function t(n){var a=r[n];if(void 0!==a)return a.exports;var s=r[n]={exports:{}};return e[n](s,s.exports,t),s.exports}t.m=e,t.x=()=>{var e=t.O(void 0,["85093"],function(){return t(12445)});return t.O(e)},t.d=(e,r)=>{for(var n in r)t.o(r,n)&&!t.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:r[n]})},t.f={},t.e=e=>Promise.all(Object.keys(t.f).reduce((r,n)=>(t.f[n](e,r),r),[])),t.u=e=>"static/js/async/"+e+".630d8684.js",t.miniCssF=e=>""+e+".css",t.h=()=>"5e41a86d22abda51",t.g=(()=>{if("object"==typeof globalThis)return globalThis;try{return this||Function("return this")()}catch(e){if("object"==typeof window)return window}})(),t.o=(e,r)=>Object.prototype.hasOwnProperty.call(e,r),(()=>{var e=[];t.O=(r,n,a,s)=>{if(n){s=s||0;for(var i=e.length;i>0&&e[i-1][2]>s;i--)e[i]=e[i-1];e[i]=[n,a,s];return}for(var o=1/0,i=0;i<e.length;i++){for(var[n,a,s]=e[i],c=!0,l=0;l<n.length;l++)(!1&s||o>=s)&&Object.keys(t.O).every(e=>t.O[e](n[l]))?n.splice(l--,1):(c=!1,s<o&&(o=s));if(c){e.splice(i--,1);var u=a();void 0!==u&&(r=u)}}return r}})(),t.rv=()=>"1.3.12",(()=>{var e=t.x;t.x=()=>t.e("85093").then(e)})(),(()=>{t.g.importScripts&&(e=t.g.location+"");var e,r=t.g.document;if(!e&&r&&(r.currentScript&&"SCRIPT"===r.currentScript.tagName.toUpperCase()&&(e=r.currentScript.src),!e)){var n=r.getElementsByTagName("script");if(n.length)for(var a=n.length-1;a>-1&&(!e||!/^http(s?):/.test(e));)e=n[a--].src}if(!e)throw Error("Automatic publicPath is not supported in this browser");t.p=(e=e.replace(/^blob:/,"").replace(/#.*$/,"").replace(/\?.*$/,"").replace(/\/[^\/]+$/,"/"))+"../../../"})(),(()=>{var e={16651:1};t.f.i=(r,n)=>{e[r]||importScripts(t.p+t.u(r))};var r=self.webpackChunk_bull_board_ui=self.webpackChunk_bull_board_ui||[],n=r.push.bind(r);r.push=r=>{var[a,s,i]=r;for(var o in s)t.o(s,o)&&(t.m[o]=s[o]);for(i&&i(t);a.length;)e[a.pop()]=1;n(r)}})(),t.ruid="bundler=rspack@1.3.12",t.x()})();