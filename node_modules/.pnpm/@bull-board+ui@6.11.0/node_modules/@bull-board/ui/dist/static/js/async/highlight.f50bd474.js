"use strict";(self.webpackChunk_bull_board_ui=self.webpackChunk_bull_board_ui||[]).push([["54183"],{39429:function(e,n,a){a.d(n,{highlighter:()=>i});var s=a(19294),t=a(49436),c=a(48462);s.Z.registerLanguage("json",t.Z),s.Z.registerLanguage("stacktrace",c.q);let i=s.Z},48462:function(e,n,a){a.d(n,{q:()=>s});function s(){let e={className:"number",begin:":\\d+:\\d+",relevance:5};return{case_insensitive:!0,contains:[{className:"type",begin:/^\w*Error:\s*/,relevance:40,contains:[{className:"title",begin:/.*/,end:/$/,excludeStart:!0,endsWithParent:!0}]},{className:"trace-line",begin:/^\s*at/,end:/$/,keywords:"at as async prototype anonymous function",contains:[{className:"code-path",begin:/\(/,end:/\)$/,excludeEnd:!0,excludeBegin:!0,contains:[e]}]},e]}}}}]);