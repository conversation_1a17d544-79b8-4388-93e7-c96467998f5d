#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Desktop/task-server/node_modules/.pnpm/node-gyp-build-optional-packages@5.2.2/node_modules/node-gyp-build-optional-packages/node_modules:/Users/<USER>/Desktop/task-server/node_modules/.pnpm/node-gyp-build-optional-packages@5.2.2/node_modules:/Users/<USER>/Desktop/task-server/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Desktop/task-server/node_modules/.pnpm/node-gyp-build-optional-packages@5.2.2/node_modules/node-gyp-build-optional-packages/node_modules:/Users/<USER>/Desktop/task-server/node_modules/.pnpm/node-gyp-build-optional-packages@5.2.2/node_modules:/Users/<USER>/Desktop/task-server/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../node-gyp-build-optional-packages/bin.js" "$@"
else
  exec node  "$basedir/../../../node-gyp-build-optional-packages/bin.js" "$@"
fi
