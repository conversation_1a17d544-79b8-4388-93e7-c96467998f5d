{"name": "task-server", "version": "1.0.0", "description": "Task server with Bull queues and Bull Board for Railway deployment", "main": "src/index.js", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "test": "node test-startup.js", "test:jobs": "node examples/add-jobs.js", "migrate:example": "node examples/migration-example.js", "redis:start": "docker run -d -p 6379:6379 --name task-server-redis redis:alpine", "redis:stop": "docker stop task-server-redis && docker rm task-server-redis"}, "keywords": ["bull", "queue", "task", "railway", "redis", "crawler"], "author": "", "license": "MIT", "dependencies": {"@bull-board/api": "^6.11.0", "@bull-board/express": "^6.11.0", "axios": "^1.6.2", "bull": "^4.12.2", "cheerio": "^1.0.0-rc.12", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "redis": "^4.6.12", "winston": "^3.11.0"}, "devDependencies": {"nodemon": "^3.0.2"}, "engines": {"node": ">=18.0.0"}}