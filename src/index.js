require("dotenv").config();
const express = require("express");
const cors = require("cors");
const { createBullBoard } = require("@bull-board/api");
const { BullAdapter } = require("@bull-board/api/bullAdapter");
const { ExpressAdapter } = require("@bull-board/express");
const logger = require("./utils/logger");

// Import queues
const crawlingQueue = require("./queues/crawling");
const databaseQueue = require("./queues/database");
const notificationQueue = require("./queues/notification");

// Import processors
require("./processors/crawlingProcessor");
require("./processors/databaseProcessor");
require("./processors/notificationProcessor");

const app = express();
const PORT = process.env.PORT || 3001; // 改为 3001 避免与其他服务冲突

// Middleware
app.use(cors());
app.use(express.json());

// Bull Board setup
const serverAdapter = new ExpressAdapter();
serverAdapter.setBasePath("/admin/queues");

const { addQueue, removeQueue, setQueues, replaceQueues } = createBullBoard({
  queues: [
    new BullAdapter(crawlingQueue),
    new BullAdapter(databaseQueue),
    new BullAdapter(notificationQueue),
  ],
  serverAdapter: serverAdapter,
});

app.use("/admin/queues", serverAdapter.getRouter());

// Health check endpoint
app.get("/health", (req, res) => {
  res.json({
    status: "ok",
    timestamp: new Date().toISOString(),
    queues: {
      crawling: crawlingQueue.name,
      database: databaseQueue.name,
      notification: notificationQueue.name,
    },
  });
});

// API endpoints for adding jobs
app.post("/api/jobs/crawling", async (req, res) => {
  try {
    const { url, options = {} } = req.body;

    if (!url) {
      return res.status(400).json({ error: "URL is required" });
    }

    const job = await crawlingQueue.add(
      "crawl-page",
      {
        url,
        options,
        timestamp: new Date().toISOString(),
      },
      {
        attempts: 3,
        backoff: {
          type: "exponential",
          delay: 2000,
        },
      }
    );

    logger.info(`🔥 Crawling job added: ${job.id} for URL: ${url}`);

    res.json({
      success: true,
      jobId: job.id,
      message: "Crawling job added successfully",
    });
  } catch (error) {
    logger.error(`🔥 Error adding crawling job: ${error.message}`);
    res.status(500).json({ error: "Failed to add crawling job" });
  }
});

app.post("/api/jobs/database", async (req, res) => {
  try {
    const { operation, data, options = {} } = req.body;

    if (!operation || !data) {
      return res.status(400).json({ error: "Operation and data are required" });
    }

    const job = await databaseQueue.add(
      "db-operation",
      {
        operation,
        data,
        options,
        timestamp: new Date().toISOString(),
      },
      {
        attempts: 3,
        backoff: {
          type: "exponential",
          delay: 1000,
        },
      }
    );

    logger.info(`🔥 Database job added: ${job.id} for operation: ${operation}`);

    res.json({
      success: true,
      jobId: job.id,
      message: "Database job added successfully",
    });
  } catch (error) {
    logger.error(`🔥 Error adding database job: ${error.message}`);
    res.status(500).json({ error: "Failed to add database job" });
  }
});

// API endpoint for adding notification jobs
app.post("/api/jobs/notification", async (req, res) => {
  try {
    const { type, message, options = {} } = req.body;

    if (!type || !message) {
      return res.status(400).json({ error: "Type and message are required" });
    }

    let jobData = {
      message,
      options,
      timestamp: new Date().toISOString(),
    };

    let jobOptions = {
      attempts: 3,
      backoff: {
        type: "exponential",
        delay: 1000,
      },
    };

    // Handle different notification types
    switch (type) {
      case "lark-message":
        // Single message
        break;

      case "lark-recurring":
        // Recurring messages every X seconds
        jobData.intervalSeconds = options.intervalSeconds || 5;
        jobData.maxCount = options.maxCount || 10;
        break;

      case "lark-scheduled":
        // Scheduled message
        if (!options.scheduleTime) {
          return res
            .status(400)
            .json({ error: "scheduleTime is required for scheduled messages" });
        }
        jobData.scheduleTime = options.scheduleTime;

        // Calculate delay for scheduled job
        const scheduleDate = new Date(options.scheduleTime);
        const now = new Date();
        const delay = Math.max(0, scheduleDate.getTime() - now.getTime());

        if (delay > 0) {
          jobOptions.delay = delay;
        }
        break;

      default:
        return res
          .status(400)
          .json({ error: `Unknown notification type: ${type}` });
    }

    const job = await notificationQueue.add(type, jobData, jobOptions);

    logger.info(
      `🔥 Notification job added: ${job.id} - type: ${type}, message: ${message}`
    );

    res.json({
      success: true,
      jobId: job.id,
      type,
      message: "Notification job added successfully",
    });
  } catch (error) {
    logger.error(`🔥 Error adding notification job: ${error.message}`);
    res.status(500).json({ error: "Failed to add notification job" });
  }
});

// Get job status
app.get("/api/jobs/:jobId/status", async (req, res) => {
  try {
    const { jobId } = req.params;

    // Try to find job in all queues
    let job = await crawlingQueue.getJob(jobId);
    if (!job) {
      job = await databaseQueue.getJob(jobId);
    }
    if (!job) {
      job = await notificationQueue.getJob(jobId);
    }

    if (!job) {
      return res.status(404).json({ error: "Job not found" });
    }

    const state = await job.getState();
    const progress = job.progress();

    res.json({
      id: job.id,
      state,
      progress,
      data: job.data,
      processedOn: job.processedOn,
      finishedOn: job.finishedOn,
      failedReason: job.failedReason,
    });
  } catch (error) {
    logger.error(`🔥 Error getting job status: ${error.message}`);
    res.status(500).json({ error: "Failed to get job status" });
  }
});

// Root endpoint
app.get("/", (req, res) => {
  res.json({
    message: "Task Server is running",
    endpoints: {
      health: "/health",
      bullBoard: "/admin/queues",
      addCrawlingJob: "POST /api/jobs/crawling",
      addDatabaseJob: "POST /api/jobs/database",
      addNotificationJob: "POST /api/jobs/notification",
      getJobStatus: "GET /api/jobs/:jobId/status",
    },
  });
});

// Error handling middleware
app.use((error, req, res, next) => {
  logger.error(`🔥 Unhandled error: ${error.message}`);
  res.status(500).json({ error: "Internal server error" });
});

// Graceful shutdown
process.on("SIGTERM", async () => {
  logger.info("🔥 SIGTERM received, shutting down gracefully");

  await crawlingQueue.close();
  await databaseQueue.close();
  await notificationQueue.close();

  process.exit(0);
});

process.on("SIGINT", async () => {
  logger.info("🔥 SIGINT received, shutting down gracefully");

  await crawlingQueue.close();
  await databaseQueue.close();
  await notificationQueue.close();

  process.exit(0);
});

// 智能端口选择函数
function startServer(port) {
  const server = app.listen(port, () => {
    logger.info(`🔥 Task server is running on port ${port}`);
    logger.info(
      `🔥 Bull Board available at http://localhost:${port}/admin/queues`
    );
    logger.info(`🔥 Health check: http://localhost:${port}/health`);
  });

  server.on("error", (err) => {
    if (err.code === "EADDRINUSE") {
      const nextPort = parseInt(port) + 1;
      logger.warn(`🔥 Port ${port} is in use, trying port ${nextPort}`);
      startServer(nextPort);
    } else {
      logger.error(`🔥 Server error: ${err.message}`);
      process.exit(1);
    }
  });

  return server;
}

// 启动服务器
startServer(PORT);
