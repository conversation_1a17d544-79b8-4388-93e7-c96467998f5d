const notificationQueue = require('../queues/notification');
const logger = require('../utils/logger');

// Lark webhook URL
const LARK_WEBHOOK_URL = "https://open.larksuite.com/open-apis/bot/v2/hook/153dda5d-bbb6-4ac7-933c-d9c0e57217b4";

// Process Lark notification jobs
notificationQueue.process('lark-message', async (job) => {
  const { message, options = {} } = job.data;
  
  logger.info(`🔥 Starting Lark notification job ${job.id} - message: ${message}`);
  
  try {
    job.progress(10);
    
    // Prepare the payload for Lark webhook
    const payload = {
      msg_type: "text",
      content: { 
        text: message 
      }
    };
    
    job.progress(30);
    
    // Send message to Lark webhook
    const response = await fetch(LARK_WEBHOOK_URL, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(payload),
      mode: "no-cors", // ⚠️ This hides response data
    });
    
    job.progress(80);
    
    // Note: Due to no-cors mode, we can't read the response
    // We'll assume success if no error was thrown
    const result = {
      message,
      webhookUrl: LARK_WEBHOOK_URL,
      sentAt: new Date().toISOString(),
      processingTime: Date.now() - job.timestamp,
      status: 'sent' // We assume success due to no-cors limitation
    };
    
    job.progress(100);
    
    logger.info(`🔥 Lark notification job ${job.id} completed successfully`);
    
    return result;
    
  } catch (error) {
    logger.error(`🔥 Lark notification job ${job.id} failed: ${error.message}`);
    throw new Error(`Failed to send Lark message: ${error.message}`);
  }
});

// Process recurring Lark messages (every 5 seconds)
notificationQueue.process('lark-recurring', async (job) => {
  const { message, intervalSeconds = 5, maxCount = 10 } = job.data;
  
  logger.info(`🔥 Starting recurring Lark notification job ${job.id} - interval: ${intervalSeconds}s, max: ${maxCount}`);
  
  try {
    let sentCount = 0;
    const results = [];
    
    while (sentCount < maxCount) {
      job.progress((sentCount / maxCount) * 100);
      
      // Prepare message with counter
      const messageWithCounter = `${message} (${sentCount + 1}/${maxCount}) - ${new Date().toLocaleString('ja-JP')}`;
      
      // Send message to Lark webhook
      const payload = {
        msg_type: "text",
        content: { 
          text: messageWithCounter 
        }
      };
      
      try {
        const response = await fetch(LARK_WEBHOOK_URL, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(payload),
          mode: "no-cors",
        });
        
        results.push({
          count: sentCount + 1,
          message: messageWithCounter,
          sentAt: new Date().toISOString(),
          status: 'sent'
        });
        
        logger.info(`🔥 Sent recurring message ${sentCount + 1}/${maxCount}: ${messageWithCounter}`);
        
      } catch (sendError) {
        logger.error(`🔥 Failed to send message ${sentCount + 1}: ${sendError.message}`);
        results.push({
          count: sentCount + 1,
          message: messageWithCounter,
          sentAt: new Date().toISOString(),
          status: 'failed',
          error: sendError.message
        });
      }
      
      sentCount++;
      
      // Wait for the specified interval (except for the last message)
      if (sentCount < maxCount) {
        await new Promise(resolve => setTimeout(resolve, intervalSeconds * 1000));
      }
    }
    
    job.progress(100);
    
    const result = {
      originalMessage: message,
      intervalSeconds,
      totalSent: sentCount,
      maxCount,
      results,
      completedAt: new Date().toISOString(),
      processingTime: Date.now() - job.timestamp
    };
    
    logger.info(`🔥 Recurring Lark notification job ${job.id} completed - sent ${sentCount} messages`);
    
    return result;
    
  } catch (error) {
    logger.error(`🔥 Recurring Lark notification job ${job.id} failed: ${error.message}`);
    throw new Error(`Failed to send recurring Lark messages: ${error.message}`);
  }
});

// Process scheduled Lark messages
notificationQueue.process('lark-scheduled', async (job) => {
  const { message, scheduleTime, options = {} } = job.data;
  
  logger.info(`🔥 Starting scheduled Lark notification job ${job.id} - scheduled for: ${scheduleTime}`);
  
  try {
    job.progress(20);
    
    // Check if it's time to send
    const now = new Date();
    const scheduledDate = new Date(scheduleTime);
    
    if (now < scheduledDate) {
      const waitTime = scheduledDate.getTime() - now.getTime();
      logger.info(`🔥 Waiting ${waitTime}ms until scheduled time: ${scheduleTime}`);
      
      // Wait until scheduled time
      await new Promise(resolve => setTimeout(resolve, waitTime));
    }
    
    job.progress(60);
    
    // Send the message
    const messageWithTime = `${message} - 送信時刻: ${new Date().toLocaleString('ja-JP')}`;
    
    const payload = {
      msg_type: "text",
      content: { 
        text: messageWithTime 
      }
    };
    
    const response = await fetch(LARK_WEBHOOK_URL, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(payload),
      mode: "no-cors",
    });
    
    job.progress(100);
    
    const result = {
      message: messageWithTime,
      originalMessage: message,
      scheduledTime: scheduleTime,
      actualSentTime: new Date().toISOString(),
      webhookUrl: LARK_WEBHOOK_URL,
      processingTime: Date.now() - job.timestamp
    };
    
    logger.info(`🔥 Scheduled Lark notification job ${job.id} completed successfully`);
    
    return result;
    
  } catch (error) {
    logger.error(`🔥 Scheduled Lark notification job ${job.id} failed: ${error.message}`);
    throw new Error(`Failed to send scheduled Lark message: ${error.message}`);
  }
});

logger.info('🔥 Notification processor initialized');
