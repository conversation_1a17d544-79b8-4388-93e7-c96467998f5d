const Queue = require('bull');
const redisConfig = require('../config/redis');
const logger = require('../utils/logger');

// Create notification queue
const notificationQueue = new Queue('notification', {
  redis: redisConfig,
  defaultJobOptions: {
    removeOnComplete: 100, // Keep last 100 completed jobs
    removeOnFail: 50,      // Keep last 50 failed jobs
    attempts: 3,
    backoff: {
      type: 'exponential',
      delay: 1000,
    },
  },
});

// Queue event listeners
notificationQueue.on('completed', (job, result) => {
  logger.info(`🔥 Notification job ${job.id} completed successfully`);
});

notificationQueue.on('failed', (job, err) => {
  logger.error(`🔥 Notification job ${job.id} failed: ${err.message}`);
});

notificationQueue.on('stalled', (job) => {
  logger.warn(`🔥 Notification job ${job.id} stalled`);
});

notificationQueue.on('progress', (job, progress) => {
  logger.info(`🔥 Notification job ${job.id} progress: ${progress}%`);
});

module.exports = notificationQueue;
